// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// 用户模型
model User {
  id                     String    @id @default(uuid()) // 唯一标识
  name                   String                        // 用户名
  password               String                        // 密码
  email                  String    @unique             // 电子邮件，唯一
  image                  String?                       // 用户头像
  subscriptionLevel      SubscriptionLevel    @default(BASIC)     // 订阅等级
  subscriptionStartDate  DateTime?                     // 订阅开始日期
  subscriptionEndDate    DateTime?                     // 订阅结束日期
  subscriptionStatus     SubscriptionStatus   @default(ACTIVE)    // 订阅状态
  createdAt              DateTime  @default(now())     // 创建时间
  updatedAt              DateTime  @updatedAt          // 更新时间
  thirdPartyId           String?                       // 第三方认证ID
  provider               String?                       // 认证提供方
  oauthAccessToken       String?                       // OAuth访问令牌
  oauthRefreshToken      String?                       // OAuth刷新令牌
  tokenExpiresAt         DateTime?                     // 令牌过期时间
  lastLoginAt            DateTime?                     // 最后登录时间
  workspaces             Workspace[]                   // 用户的工作空间
  websites               Website[]                     // 用户的页面
  pages                  Page[]                        // 用户的页面
  workspaceMemberships   WorkspaceMember[]             // 用户的工作空间成员关系
  revokedTokens          RevokedToken[]
  auditLogs             PageAuditLog[]                // 页面审计日志
  accessLogs            UserAccessLog[]               // 用户访问日志

  // === 新增CMS内容关联 ===
  createdContents        ContentNode[]                 // 用户创建的内容
  contentRevisions       ContentRevision[]             // 用户创建的内容版本
  contentAuditLogs       ContentAuditLog[]             // 用户的内容审计日志

  @@unique([provider, thirdPartyId])
  @@unique([provider, email])
}

enum SubscriptionLevel {
  BASIC
  PLUS
  PRO
  ENTERPRISE
  PRIVATE
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  PENDING
  CANCELLED
}

// 工作空间模型
model Workspace {
  id            String               @id @default(uuid())  // 工作空间的唯一标识符
  name          String                                      // 工作空间名称
  userId        String                                      // 创建工作空间的用户ID
  user          User                 @relation(fields: [userId], references: [id]) // 关联的用户
  createdAt     DateTime             @default(now())       // 工作空间创建时间
  updatedAt     DateTime             @updatedAt            // 工作空间最后更新时间
  isActive      Boolean              @default(true)        // 表示工作空间是否处于活跃状态，默认为活跃
  visibility    WorkspaceVisibility  @default(PRIVATE)     // 工作空间的可见性，默认为私有
  description   String?                                    // 工作空间的描述，可选
  tags          String[]                                   // 工作空间的标签，可多个
  websites      Website[]                                   // 工作空间包含的网站
  pages         Page[]                                     // 工作空间包含的页面
  members       WorkspaceMember[]                          // 工作空间的成员
  quota         Quota?                                     // 工作空间的配额设置，可选

  // === 新增CMS内容关联 ===
  contents      ContentNode[]                              // 工作空间的内容
  contentCategories Category[]                             // 工作空间的内容分类
  contentTags   Tag[]                                      // 工作空间的内容标签
}

// 工作空间可见性枚举
enum WorkspaceVisibility {
  PUBLIC
  PRIVATE
}

// 工作空间成员模型
model WorkspaceMember {
  id           String         @id @default(uuid())  // 工作空间成员的唯一标识符
  workspaceId  String                                // 关联的工作空间ID
  workspace    Workspace     @relation(fields: [workspaceId], references: [id]) // 所属工作空间
  userId       String                                // 关联的用户ID
  user         User          @relation(fields: [userId], references: [id]) // 关联的用户
  role         WorkspaceRole @default(MEMBER)       // 用户在工作空间中的角色，默认为MEMBER
  createdAt    DateTime      @default(now())         // 成员加入时间
  updatedAt    DateTime      @updatedAt              // 成员信息最后更新时间
}


// 工作空间角色枚举
enum WorkspaceRole {
  OWNER
  ADMIN
  MEMBER
  GUEST
}

enum DomainStatus {
  PENDING     // DNS 记录待配置
  ACTIVE      // DNS 记录已配置且正常工作
  FAILED      // DNS 记录验证失败
}

model CustomDomain {
  id          String       @id @default(uuid())
  domain      String       @unique
  status      DomainStatus @default(PENDING)
  websiteId   String       @unique
  website     Website      @relation(fields: [websiteId], references: [id], onDelete: Cascade)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@index([websiteId])
}

enum Language {
  EN // 英文
  CN // 中文
  ZH // 繁体中文
  ES // 西班牙语
  HI // 印地语
  FR // 法语
  DE // 德语
  RU // 俄语
  PT // 葡萄牙语
  AR // 阿拉伯语
  JP // 日语
  KR // 韩语
  IT // 意大利语
  TR // 土耳其语
  PL // 波兰语
  NL // 荷兰语
  ID // 印尼语
  TH // 泰语
  VI // 越南语
  SV // 瑞典语
}

model Website {
  id                String       @id @default(uuid())
  name              String
  domain            String       @unique //自定义二级域名  xxxx.lit.page
  logo              String?                                //LOGO 地址
  defaultLanguage   Language      @default(EN)                                 // 默认语言，可选字段
  description       String?                               // 表单描述，可选字段
  tags              String[]                              // 表单标签，可多个

  workspaceId String
  workspace         Workspace     @relation(fields: [workspaceId], references: [id])
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  pages       Page[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  customDomain CustomDomain?                             // 改为一对一关系
  integrations      Integration[]                         // 与页面关联的集成
  configuration   Json                                     // 网站品牌配置，仅默认语言

  // 旧的 JSON 字段，保留用于兼容性，后续将逐步迁移到独立模型
  headers         Json?                                    // 多语言头部 { CN: { type: 'Header' }, EN: { type: 'Header' } }
  footers         Json?                                    // 多语言尾部 { CN: { type: 'Footer' }, EN: { type: 'Footer' } }

  // 新增关联
  websiteHeaders  WebsiteHeader[]                         // 网站的所有 Header
  websiteFooters  WebsiteFooter[]                         // 网站的所有 Footer
  publishRecords  PublishRecord[]                         // 网站的发布记录

  // SEO 关联
  seo             WebsiteSEO?                            // 网站 SEO 设置

  // === 新增CMS内容关联 ===
  contents        ContentNode[]                           // 网站的内容
  contentCategories Category[]                            // 网站的内容分类
  contentTags     Tag[]                                   // 网站的内容标签
}

// 组件状态枚举
enum ComponentStatus {
  INITIALIZED      // 初始化状态，还没有任何版本
  DRAFT           // 初始草稿，从未发布过
  PUBLISHED       // 已发布且无修改
  MODIFIED        // 已发布且有未发布的修改
  ARCHIVED        // 已归档
}

// 版本状态枚举
enum VersionStatus {
  DRAFT           // 草稿状态
  PUBLISHED       // 已发布状态
}

enum PageStatus {
  INITIALIZED      // 初始化状态，还没有任何版本
  DRAFT           // 初始草稿，从未发布过
  PUBLISHED       // 已发布且无修改
  MODIFIED        // 已发布且有未发布的修改
  ARCHIVED        // 已归档
  DELETED         // 已删除（软删除）
}

enum PageType {
  LANDING
  ABOUT
  FEATURE
  PRICING
  FAQ
  TERMS
  BLOG
}

model Page {
  id                String        @id @default(uuid())    // 页面的唯一标识符
  nanoid            String        @unique                 // 页面访问 ID
  language          Language      @default(EN)
  pageType          PageType      @default(LANDING)       // 页面类型 landing、about...
  slug              String                               // 自定义URL slug
  name              String
  title             String                                // 页面标题
  description       String?                               // 页面描述，可选字段
  metaKeywords      String[]
  metaDescription   String?
  ogTitle           String?                              // Open Graph 标题
  ogDescription     String?                              // Open Graph 描述
  ogImage           String?                              // Open Graph 图片 URL
  tags              String[]                              // 页面标签，可多个
  isTemplate        Boolean       @default(false)         // 是否为模板，默认为非模板
  userId            String                                // 创建页面的用户ID
  user              User          @relation(fields: [userId], references: [id]) // 创建用户
  workspaceId       String                                // 页面所属的工作空间ID
  workspace         Workspace     @relation(fields: [workspaceId], references: [id]) // 所属工作空间
  websiteId         String                                // 页面所属的工作空间ID
  website           Website     @relation(fields: [websiteId], references: [id]) // 所属工作空间

  draftVersionId     String?                               // 当前草稿版本ID
  publishedVersionId String?                              // 当前发布版本ID
  // currentVersionId   String?                               // 当前活跃的页面版本ID，可选，后续版本移除
  status            PageStatus     @default(INITIALIZED)  // 暂时使用 DRAFT 作为默认值

  // isPublished       Boolean?                              // 页面是否已发布，默认未发布，稍后删除通用 status 判断
  // isDeleted         Boolean?                              // 软删除标记状态, 统一使用 status 来判断

  publishedAt       DateTime?                             // 页面发布时间，可选
  createdAt         DateTime  @default(now())         // 页面创建时间
  updatedAt         DateTime  @updatedAt              // 页面最后更新时间

  versions          PageVersion[]                         // 页面的版本历史
  auditLogs         PageAuditLog[]                        // 页面审计日志
  abTests           ABTest[]                              // 关联的AB测试
  
  // 新增：当前使用的 Header/Footer 直接引用
  currentHeaderId   String?                               // 当前使用的 Header ID
  currentHeader     WebsiteHeader? @relation("PageCurrentHeader", fields: [currentHeaderId], references: [id])
  currentFooterId   String?                               // 当前使用的 Footer ID
  currentFooter     WebsiteFooter? @relation("PageCurrentFooter", fields: [currentFooterId], references: [id])

  // SEO 关联
  seo               PageSEO?                              // 页面 SEO 设置

  @@unique([websiteId, slug, language])
  @@index([userId])
  @@index([workspaceId])
  @@index([websiteId])
}

model PageVersion {
  id              String    @id @default(uuid())  // 页面版本的唯一标识
  pageId          String                            // 关联页面的ID
  page            Page      @relation(fields: [pageId], references: [id]) // 所属页面
  versionNumber   Int                               // 版本号
  theme           String                            // 页面的主题或样式
  configuration   Json                              // 版本特定的配置
  changelog       String?                           // 版本变更日志
  publishedAt     DateTime?                         // 仅保留发布时间
  createdAt       DateTime  @default(now())         // 创建时间
  
  // 版本复制关系
  copiedFromVersionId String?                       // 复制来源版本 ID
  copiedFromVersion   PageVersion? @relation("CopiedPageVersions", fields: [copiedFromVersionId], references: [id])
  copiedToVersions    PageVersion[] @relation("CopiedPageVersions")
  
  // 新增关联
  headerVersionId String?                           // 关联的 Header 版本 ID
  headerVersion   WebsiteHeaderVersion? @relation(fields: [headerVersionId], references: [id])
  footerVersionId String?                           // 关联的 Footer 版本 ID
  footerVersion   WebsiteFooterVersion? @relation(fields: [footerVersionId], references: [id])
  
  @@index([pageId])
  @@index([headerVersionId])
  @@index([footerVersionId])
  @@index([copiedFromVersionId])
  @@index([createdAt])
}

// 网站 Header 模型
model WebsiteHeader {
  id                String           @id @default(uuid())  // 唯一标识符
  websiteId         String                                 // 关联的网站 ID
  website           Website          @relation(fields: [websiteId], references: [id]) // 关联的网站
  language          Language                               // 语言
  variant           String           @default("default")   // 变体类型，默认为 default
  draftVersionId    String?          @unique               // 当前草稿版本 ID
  draftVersion      WebsiteHeaderVersion? @relation("HeaderDraftVersion", fields: [draftVersionId], references: [id])
  publishedVersionId String?         @unique               // 当前发布版本 ID
  publishedVersion  WebsiteHeaderVersion? @relation("HeaderPublishedVersion", fields: [publishedVersionId], references: [id])
  status            ComponentStatus  @default(INITIALIZED)  // 状态
  createdAt         DateTime         @default(now())       // 创建时间
  updatedAt         DateTime         @updatedAt            // 更新时间
  publishedAt       DateTime?                              // 发布时间
  createdBy         String                                 // 创建者 ID
  publishedBy       String?                                // 发布者 ID
  isDeleted         Boolean          @default(false)       // 软删除标记
  
  // 关联
  versions          WebsiteHeaderVersion[] @relation("HeaderVersions")
  
  // 新增：被页面直接引用的关系
  usedInPages       Page[] @relation("PageCurrentHeader")
  
  @@unique([websiteId, language])
  @@index([websiteId])
  @@index([websiteId, status])
  @@index([websiteId, isDeleted])
  @@index([language, variant])
}

// 网站 Header 版本模型
model WebsiteHeaderVersion {
  id              String         @id @default(uuid())  // 唯一标识符
  headerId        String                               // 关联的 Header ID
  header          WebsiteHeader  @relation("HeaderVersions", fields: [headerId], references: [id]) // 关联的 Header
  versionNumber   Int                                  // 版本号
  variant         String         @default("default")   // 变体类型，默认为 default
  configuration   Json                                 // 版本配置
  changelog       String?                              // 变更日志
  createdAt       DateTime       @default(now())       // 创建时间
  createdBy       String                               // 创建者 ID
  status          VersionStatus  @default(DRAFT)       // 版本状态
  
  // 版本复制关系
  copiedFromVersionId String?                          // 复制来源版本 ID
  copiedFromVersion   WebsiteHeaderVersion? @relation("CopiedHeaderVersions", fields: [copiedFromVersionId], references: [id])
  copiedToVersions    WebsiteHeaderVersion[] @relation("CopiedHeaderVersions")
  
  // 关联
  draftHeader     WebsiteHeader? @relation("HeaderDraftVersion")
  publishedHeader WebsiteHeader? @relation("HeaderPublishedVersion")
  pageVersions    PageVersion[]
  publishedComponents PublishedComponent[]
  
  @@index([headerId])
  @@index([status])
  @@index([createdAt])
  @@index([headerId, status])
  @@index([headerId, variant])
  @@index([copiedFromVersionId])
}

// 网站 Footer 模型
model WebsiteFooter {
  id                String           @id @default(uuid())  // 唯一标识符
  websiteId         String                                 // 关联的网站 ID
  website           Website          @relation(fields: [websiteId], references: [id]) // 关联的网站
  language          Language                               // 语言
  variant           String           @default("default")   // 变体类型，默认为 default
  draftVersionId    String?          @unique               // 当前草稿版本 ID
  draftVersion      WebsiteFooterVersion? @relation("FooterDraftVersion", fields: [draftVersionId], references: [id])
  publishedVersionId String?         @unique               // 当前发布版本 ID
  publishedVersion  WebsiteFooterVersion? @relation("FooterPublishedVersion", fields: [publishedVersionId], references: [id])
  status            ComponentStatus  @default(INITIALIZED)  // 状态
  createdAt         DateTime         @default(now())       // 创建时间
  updatedAt         DateTime         @updatedAt            // 更新时间
  publishedAt       DateTime?                              // 发布时间
  createdBy         String                                 // 创建者 ID
  publishedBy       String?                                // 发布者 ID
  isDeleted         Boolean          @default(false)       // 软删除标记
  
  // 关联
  versions          WebsiteFooterVersion[] @relation("FooterVersions")
  
  // 新增：被页面直接引用的关系
  usedInPages       Page[] @relation("PageCurrentFooter")
  
  @@unique([websiteId, language])
  @@index([websiteId])
  @@index([websiteId, status])
  @@index([websiteId, isDeleted])
  @@index([language, variant])
}

// 网站 Footer 版本模型
model WebsiteFooterVersion {
  id              String         @id @default(uuid())  // 唯一标识符
  footerId        String                               // 关联的 Footer ID
  footer          WebsiteFooter  @relation("FooterVersions", fields: [footerId], references: [id]) // 关联的 Footer
  versionNumber   Int                                  // 版本号
  variant         String         @default("default")   // 变体类型，默认为 default
  configuration   Json                                 // 版本配置
  changelog       String?                              // 变更日志
  createdAt       DateTime       @default(now())       // 创建时间
  createdBy       String                               // 创建者 ID
  status          VersionStatus  @default(DRAFT)       // 版本状态
  
  // 版本复制关系
  copiedFromVersionId String?                          // 复制来源版本 ID
  copiedFromVersion   WebsiteFooterVersion? @relation("CopiedFooterVersions", fields: [copiedFromVersionId], references: [id])
  copiedToVersions    WebsiteFooterVersion[] @relation("CopiedFooterVersions")
  
  // 关联
  draftFooter     WebsiteFooter? @relation("FooterDraftVersion")
  publishedFooter WebsiteFooter? @relation("FooterPublishedVersion")
  pageVersions    PageVersion[]
  publishedComponents PublishedComponent[]
  
  @@index([footerId])
  @@index([status])
  @@index([createdAt])
  @@index([footerId, status])
  @@index([footerId, variant])
  @@index([copiedFromVersionId])
}

// 发布状态枚举
enum PublishStatus {
  SUCCESS         // 发布成功
  FAILED          // 发布失败
  PENDING         // 发布中
  CANCELLED       // 发布取消
}

// 组件类型枚举
enum ComponentType {
  HEADER          // 头部
  FOOTER          // 尾部
  PAGE            // 页面
}

// 发布记录模型
model PublishRecord {
  id              String         @id @default(uuid())  // 唯一标识符
  websiteId       String                               // 关联的网站 ID
  website         Website        @relation(fields: [websiteId], references: [id]) // 关联的网站
  publishedAt     DateTime       @default(now())       // 发布时间
  publishedBy     String                               // 发布者 ID
  publishNote     String?                              // 发布说明
  status          PublishStatus  @default(SUCCESS)     // 发布状态
  
  // 关联
  publishedComponents PublishedComponent[]             // 发布的组件
  
  @@index([websiteId])
}

// 已发布组件模型
model PublishedComponent {
  id              String         @id @default(uuid())  // 唯一标识符
  publishRecordId String                               // 关联的发布记录 ID
  publishRecord   PublishRecord  @relation(fields: [publishRecordId], references: [id]) // 关联的发布记录
  componentType   ComponentType                        // 组件类型（Header、Footer、Page）
  componentId     String                               // 组件 ID
  headerVersionId String?                              // Header 版本 ID
  headerVersion   WebsiteHeaderVersion? @relation(fields: [headerVersionId], references: [id])
  footerVersionId String?                              // Footer 版本 ID
  footerVersion   WebsiteFooterVersion? @relation(fields: [footerVersionId], references: [id])
  language        Language                             // 语言
  
  @@index([publishRecordId])
}

model Integration {
  id            String   @id @default(uuid())  // 集成的唯一标识符
  websiteId        String                        // 关联页面的ID
  website          Website     @relation(fields: [websiteId], references: [id]) // 关联的页面
  service       String                        // 集成服务的名称
  configuration Json                          // 集成的配置信息
  enabled       Boolean   @default(true)      // 集成是否启用，默认为启用
  createdAt     DateTime  @default(now())     // 创建时间
  updatedAt     DateTime  @updatedAt          // 更新时间
}

model AuthProvider {
  id           String   @id @default(uuid())  // 唯一标识符
  name         String                       // 认证提供商名称（例如 "google", "github"）
  clientId     String                       // OAuth客户端ID
  clientSecret String                       // OAuth客户端密钥
  redirectUri  String                       // OAuth重定向URI
  createdAt    DateTime @default(now())     // 创建时间
  updatedAt    DateTime @updatedAt          // 更新时间
}

model ABTest {
  id          String   @id @default(uuid())  // AB测试的唯一标识
  pageId      String                       // 关联的页面ID
  page        Page     @relation(fields: [pageId], references: [id]) // 关联的页面
  versionAId  String                       // A版本ID
  versionBId  String                       // B版本ID
  description String?                      // 测试描述
  startDate   DateTime                     // 测试开始时间
  endDate     DateTime?                    // 测试结束时间
  status      String                       // 测试状态
  createdAt   DateTime @default(now())     // 创建时间
  updatedAt   DateTime @updatedAt          // 更新时间
  participants ABTestParticipant[]         // 测试参与者
  results     ABTestResult[]               // 测试结果
}

model ABTestParticipant {
  id              String   @id @default(uuid()) // 唯一标识符
  abTestId        String                       // AB测试ID
  abTest          ABTest   @relation(fields: [abTestId], references: [id]) // 所属AB测试
  userId          String                       // 用户ID
  assignedVersion String                       // 分配的版本
  createdAt       DateTime @default(now())     // 加入时间
}

model ABTestResult {
  id             String   @id @default(uuid())  // 唯一标识符
  abTestId       String                       // AB测试ID
  abTest         ABTest   @relation(fields: [abTestId], references: [id]) // 所属AB测试
  userId         String                       // 用户ID
  formVersionId  String                       // 页面版本ID
  data           Json                         // 收集的数据
  metrics        Json                         // 性能指标
  createdAt      DateTime @default(now())     // 创建时间
}

// 使用审计日志来追踪所有操作
model PageAuditLog {
  id              String    @id @default(uuid())
  pageId          String
  page            Page      @relation(fields: [pageId], references: [id])
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  action          PageAuditAction    // 如：DRAFT_SAVED, PUBLISHED, ARCHIVED
  versionId       String?   // 相关版本ID
  metadata        Json?     // 可以包含额外信息
  createdAt       DateTime  @default(now())

  @@index([pageId])
  @@index([userId])
}

enum PageAuditAction {
  DRAFT_SAVED
  PUBLISHED
  MODIFIED
  ARCHIVED
  RESTORED
  DELETED
  VERSION_CREATED
  VERSION_RESTORED
  METADATA_UPDATED
  VERSION_ROLLBACK
}

// === CMS 相关枚举类型 ===

// 内容状态
enum ContentStatus {
  DRAFT           // 草稿状态
  PUBLISHED       // 已发布
  ARCHIVED        // 已归档
}

// 内容类型
enum ContentType {
  BLOG            // 博客文章
  DOC             // 文档
  CHANGELOG       // 更新日志
  FAQ             // 常见问题
  PRODUCT         // 产品介绍
  ANNOUNCEMENT    // 公告通知
  TUTORIAL        // 教程指南
  CASE_STUDY      // 案例研究
  WHITEPAPER      // 白皮书
  COURSE          // 课程
  LESSON          // 课时
  VIDEO           // 视频
  EVENT           // 活动
  JOB_POSTING     // 职位发布
  PODCAST         // 播客
  PORTFOLIO       // 作品集
  REVIEW          // 评测
  RECIPE          // 食谱
  CUSTOM          // 自定义类型
}

// 分类类型
enum CategoryType {
  GENERAL         // 通用分类
  BLOG            // 博客分类
  DOC             // 文档分类
  FAQ             // FAQ分类
  CHANGELOG       // 更新日志分类
  PRODUCT         // 产品分类
  TUTORIAL        // 教程分类
  ANNOUNCEMENT    // 公告分类
  CASE_STUDY      // 案例研究分类
  WHITEPAPER      // 白皮书分类
}

// 审计操作类型
enum ContentAuditAction {
  CREATED         // 内容创建
  DRAFT_SAVED     // 草稿保存
  PUBLISHED       // 内容发布
  MODIFIED        // 内容修改
  ARCHIVED        // 内容归档
  RESTORED        // 内容恢复
  DELETED         // 内容删除
  REVISION_CREATED // 新版本创建
  REVISION_PUBLISHED // 版本发布
  METADATA_UPDATED // 元数据更新
  CATEGORY_CHANGED // 分类变更
  TAGS_UPDATED    // 标签更新
  AUTHOR_UPDATED  // 作者信息更新
  SEO_UPDATED     // SEO信息更新
}

// 配额模型
model Quota {
  id                         String       @id @default(uuid()) // 配额的唯一标识符
  workspaceId                String       @unique              // 关联的工作空间ID
  workspace                  Workspace    @relation(fields: [workspaceId], references: [id]) // 所属工作空间
  websiteCreated               Int          @default(0)          // 创建的页面总数量，默认为0
  websiteCreationLimit          Int          @default(5)          // 页面创建限制，默认为5
  pagesCreated               Int          @default(0)          // 创建的页面总数量，默认为0
  pageCreationLimit          Int          @default(50)          // 页面创建限制，默认为50
  monthlyPageViewsLimit      Int          @default(1000)       // 每月页面视图限制，默认为1000
  createdAt                  DateTime     @default(now())      // 配额设置创建时间
  updatedAt                  DateTime     @updatedAt           // 配额设置最后更新时间
  quotaUsages                QuotaUsage[]                      // 配额使用记录

  // === 新增CMS内容配额 ===
  contentsCreated            Int          @default(0)          // 已创建内容数
  contentCreationLimit       Int          @default(100)        // 内容创建限制
  contentRevisionsLimit      Int          @default(1000)       // 内容版本数限制
}

// 配额使用记录模型
model QuotaUsage {
  id                 String   @id @default(uuid())  // 配额使用记录的唯一标识符
  quotaId            String                        // 关联的配额ID
  quota              Quota    @relation(fields: [quotaId], references: [id]) // 所属配额
  month              DateTime                     // 记录的月份
  pageViewsUsed      Int       @default(0)        // 已使用的页面视图数，默认为0
  createdAt          DateTime @default(now())     // 记录创建时间
  updatedAt          DateTime @updatedAt          // 记录最后更新时间
}

model RevokedToken {
  id          String    @id @default(uuid())
  token       String    @unique
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  expiresAt   DateTime
  revokedAt   DateTime  @default(now())
}

model UserAccessLog {
  id        String   @id @default(cuid())
  userId    String
  ipAddress String?
  userAgent String?  @db.Text
  path      String
  method    String
  createdAt DateTime @default(now())

  // 关联
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@map("user_access_logs")
}

// SEO 相关枚举类型
enum XCardType {
  SUMMARY
  SUMMARY_LARGE_IMAGE
  APP
  PLAYER
}

enum ChangeFrequency {
  ALWAYS
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  NEVER
}

enum SchemaType {
  ARTICLE
  PRODUCT
  FAQ
  HOWTO
  EVENT
  ORGANIZATION
  PERSON
  WEBSITE
  LOCALBUSINESS
  RECIPE
  COURSE
  REVIEW
  BREADCRUMB
  CUSTOM
}

// SEO 相关模型
// 网站级 SEO 设置
model WebsiteSEO {
  id                String    @id @default(uuid())
  websiteId         String    @unique                      // 关联网站 ID
  website           Website   @relation(fields: [websiteId], references: [id])
  
  // 全局元数据
  siteName          String?                               // 网站名称
  siteDescription   String?                               // 网站描述
  titleTemplate     String?                               // 标题模板，例如 "%s | Site Name"
  defaultDescription String?                              // 默认描述
  globalKeywords    String[]                              // 全局关键词
  
  // Robots 和 Sitemap 设置
  robotsTxt         String?                               // 自定义 robots.txt 内容
  sitemapSettings   Json?                                 // Sitemap 配置
  
  // 结构化数据
  organizationSchema Json?                                // 组织结构化数据
  websiteSchema     Json?                                 // 网站结构化数据
  
  // 社交媒体默认设置
  defaultOgImage    String?                               // 默认 OG 图片
  ogTitleTemplate   String?                               // OG 标题模板
  ogDescriptionTemplate String?                           // OG 描述模板
  defaultXCard      XCardType?   @default(SUMMARY_LARGE_IMAGE) // 默认 X 卡片类型
  xUsername         String?                               // X 用户名
  
  // 多语言设置
  hreflangSettings  Json?                                 // Hreflang 设置
  
  // 安全和性能设置
  forceHttps        Boolean   @default(true)              // 强制使用 HTTPS
  httpToHttpsRedirect Boolean  @default(true)             // HTTP 重定向到 HTTPS
  cacheControl      String?                               // 缓存控制指令
  resourceCompression Boolean? @default(false)            // 资源压缩
  browserCaching    Boolean?  @default(false)             // 浏览器缓存
  lazyLoadingImages Boolean?  @default(false)             // 图片懒加载
  
  // 分析跟踪
  analyticsSettings Json?                                 // 分析工具设置
  
  // 品牌标识
  brandSettings     Json?                                 // 品牌设置
  
  // SEO 评分
  seoScores         Json?                                 // SEO 评分数据
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  @@index([websiteId])
  @@index([createdAt])
  @@index([updatedAt])
  
  // 关联
  pages             PageSEO[]
}

// 页面级 SEO 设置
model PageSEO {
  id                String    @id @default(uuid())
  pageId            String    @unique                      // 关联页面 ID
  page              Page      @relation(fields: [pageId], references: [id])
  
  // 基本 SEO
  title             String?                               // 自定义标题
  useCustomTitle    Boolean   @default(true)              // 是否覆盖网站级标题模板
  description       String?                               // 自定义描述
  keywords          String[]                              // 关键词
  
  // 社交媒体
  ogTitle           String?                               // Open Graph 标题
  ogDescription     String?                               // Open Graph 描述
  ogImage           String?                               // Open Graph 图片 URL
  xTitle            String?                               // X 标题
  xDescription      String?                               // X 描述
  xImage            String?                               // X 图片 URL
  xCardType         XCardType?   @default(SUMMARY_LARGE_IMAGE) // X 卡片类型
  xCardData         Json?                                 // X 卡片特定数据（APP 和 PLAYER 类型）
  
  // 结构化数据
  schemaType        SchemaType?                           // 结构化数据类型
  schemaData        Json?                                 // 结构化数据内容
  
  // 高级设置
  canonicalUrl      String?                               // 规范 URL
  robots            Json?                                 // Robots 指令
  hreflangLinks     Json?                                 // Hreflang 链接
  priority          Float?    @default(0.5)               // Sitemap 优先级
  changeFrequency   ChangeFrequency?   @default(MONTHLY)  // 更新频率
  
  // 页面内容分析（自动生成，不直接编辑）
  contentAnalysis   Json?                                 // 页面内容分析结果
  
  // 继承设置
  inheritFromSite   Boolean   @default(true)              // 是否继承网站设置
  websiteSeoId      String?                               // 关联的网站 SEO ID
  websiteSeo        WebsiteSEO? @relation(fields: [websiteSeoId], references: [id])
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  @@index([pageId])
  @@index([websiteSeoId])
  @@index([createdAt])
  @@index([schemaType])
  @@index([priority])
  @@index([changeFrequency])
}

// ===================================
// CMS 内容管理系统数据模型
// ===================================

// 内容节点模型 - 内容的身份管理，存储内容的基本信息和状态
model ContentNode {
  id                String @id @default(uuid())

  // === 层级关联（参考Page模型） ===
  userId            String                              // 创建用户ID
  user              User @relation(fields: [userId], references: [id])
  workspaceId       String                              // 所属工作空间ID
  workspace         Workspace @relation(fields: [workspaceId], references: [id])
  websiteId         String                              // 所属网站ID
  website           Website @relation(fields: [websiteId], references: [id])

  // === 内容基础信息 ===
  type              ContentType                         // 内容类型
  slug              String                              // URL slug
  title             String                              // 标题
  excerpt           String?                             // 摘要
  language          Language @default(EN)               // 语言

  // === 作者显示信息（JSON结构，支持自定义和扩展） ===
  authorInfo        Json                                // 作者信息（JSON格式）

  // === 内容状态管理 ===
  status            ContentStatus @default(DRAFT)       // 内容状态
  publishedAt       DateTime?                           // 发布时间
  createdAt         DateTime @default(now())            // 创建时间
  updatedAt         DateTime @updatedAt                 // 更新时间

  // === 版本管理 ===
  currentRevisionId String? @unique                     // 当前版本ID
  currentRevision   ContentRevision? @relation("CurrentRevision", fields: [currentRevisionId], references: [id])

  // === 分类和标签 ===
  categoryId        String?                             // 分类ID
  category          Category? @relation(fields: [categoryId], references: [id])

  // === 关联关系 ===
  revisions         ContentRevision[] @relation("NodeRevisions")  // 版本历史
  tags              ContentNodeTag[]                    // 标签关联
  auditLogs         ContentAuditLog[]                   // 审计日志

  // === 唯一性约束和索引 ===
  @@unique([websiteId, slug, language])                 // 同网站同语言下slug唯一
  @@index([userId])                                     // 按用户查询
  @@index([workspaceId])                                // 按工作空间查询
  @@index([websiteId])                                  // 按网站查询
  @@index([websiteId, type, status])                   // 按网站+类型+状态查询
  @@index([websiteId, status, publishedAt])            // 按网站+状态+发布时间查询
  @@index([authorInfo])                                 // 按作者信息查询（JSON索引）
  @@index([categoryId])                                 // 按分类查询
}

// 内容版本模型 - 存储内容的具体数据和版本历史
model ContentRevision {
  id              String @id @default(uuid())
  nodeId          String                                // 关联的内容节点ID
  node            ContentNode @relation("NodeRevisions", fields: [nodeId], references: [id])
  createdById     String                                // 版本创建者ID
  createdBy       User @relation(fields: [createdById], references: [id])

  // === 版本信息 ===
  version         Int                                   // 版本号（从1开始递增）
  data            Json                                  // 内容数据（JSON格式）
  changelog       String?                               // 版本变更说明
  isPublished     Boolean @default(false)               // 是否已发布

  // === 时间信息 ===
  createdAt       DateTime @default(now())              // 版本创建时间
  publishedAt     DateTime?                             // 版本发布时间

  // === 关联关系 ===
  currentNode     ContentNode? @relation("CurrentRevision")  // 作为当前版本的节点

  // === 索引 ===
  @@unique([nodeId, version])                           // 同节点下版本号唯一
  @@index([nodeId, isPublished])                       // 按节点和发布状态查询
  @@index([createdAt])                                  // 按创建时间查询
  @@index([createdById])                                // 按创建者查询
}

// 分类模型 - 内容的层级分类管理，支持树形结构
model Category {
  id            String @id @default(uuid())
  workspaceId   String                                  // 关联工作空间
  workspace     Workspace @relation(fields: [workspaceId], references: [id])
  websiteId     String                                  // 关联网站
  website       Website @relation(fields: [websiteId], references: [id])

  // === 分类信息 ===
  name          String                                  // 分类名称
  slug          String                                  // URL slug
  description   String?                                 // 分类描述

  // === 层级关系 ===
  parentId      String?                                 // 父分类ID（支持层级）
  parent        Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children      Category[] @relation("CategoryHierarchy")

  // === 配置信息 ===
  order         Int @default(0)                         // 排序
  type          CategoryType @default(GENERAL)          // 分类类型
  language      Language @default(EN)                   // 语言
  isActive      Boolean @default(true)                  // 是否启用

  // === 关联 ===
  contents      ContentNode[]                           // 分类下的内容

  // === 时间信息 ===
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // === 索引 ===
  @@unique([workspaceId, slug, language])               // 工作空间内slug唯一
  @@index([workspaceId])
  @@index([websiteId])
  @@index([parentId])
  @@index([type, language])
  @@index([order])
}

// 标签模型 - 内容的横向标签管理，支持多对多关联
model Tag {
  id            String @id @default(uuid())
  workspaceId   String                                  // 关联工作空间
  workspace     Workspace @relation(fields: [workspaceId], references: [id])
  websiteId     String                                  // 关联网站
  website       Website @relation(fields: [websiteId], references: [id])

  // === 标签信息 ===
  name          String                                  // 标签名称
  color         String?                                 // 标签颜色（十六进制）
  description   String?                                 // 标签描述
  language      Language @default(EN)                   // 语言
  isActive      Boolean @default(true)                  // 是否启用

  // === 关联 ===
  contentNodes  ContentNodeTag[]                        // 标签关联的内容

  // === 时间信息 ===
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // === 索引 ===
  @@unique([workspaceId, name, language])               // 工作空间内标签名唯一
  @@index([workspaceId])
  @@index([websiteId])
  @@index([language])
}

// 内容标签关联模型 - 内容与标签的多对多关联表
model ContentNodeTag {
  contentId     String                                  // 内容ID
  tagId         String                                  // 标签ID

  // === 关联 ===
  content       ContentNode @relation(fields: [contentId], references: [id], onDelete: Cascade)
  tag           Tag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  // === 扩展字段（预留） ===
  weight        Int @default(1)                         // 标签权重（预留）
  createdAt     DateTime @default(now())                // 关联创建时间

  // === 主键 ===
  @@id([contentId, tagId])
  @@index([tagId])                                      // 按标签查询内容
}

// 内容审计日志模型 - 记录内容的所有操作历史，用于审计和追踪
model ContentAuditLog {
  id              String @id @default(uuid())
  contentId       String                                // 关联内容ID
  content         ContentNode @relation(fields: [contentId], references: [id])
  userId          String                                // 操作用户ID
  user            User @relation(fields: [userId], references: [id])

  // === 操作信息 ===
  action          ContentAuditAction                    // 操作类型
  revisionId      String?                               // 相关版本ID
  metadata        Json?                                 // 额外元数据

  // === 时间信息 ===
  createdAt       DateTime @default(now())              // 操作时间

  // === 索引 ===
  @@index([contentId])                                  // 按内容查询
  @@index([userId])                                     // 按用户查询
  @@index([action])                                     // 按操作类型查询
  @@index([createdAt])                                  // 按时间查询
  @@index([contentId, createdAt])                      // 内容操作时间线
}
