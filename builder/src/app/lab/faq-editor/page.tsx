/**
 * FAQ编辑器测试页面
 * 
 * 用于测试 FAQ 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import faqConfig from '@/content-schema/FAQ';
import { 
  HelpCircle, 
  MessageSquare, 
  Tag, 
  Search, 
  Eye, 
  ThumbsUp, 
  ThumbsDown, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  Hash,
  ExternalLink
} from 'lucide-react';

// FAQ数据类型定义
interface RelatedQuestion {
  question: string;
  link: string;
}

interface HelpfulVotes {
  positive: number;
  negative: number;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
}

interface FAQData {
  question: string;
  answer: string;
  category: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  searchKeywords: string[];
  relatedQuestions: RelatedQuestion[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  lastUpdated: string;
  isPublished: boolean;
  viewCount: number;
  helpfulVotes: HelpfulVotes;
  seo: SEO;
}

// 默认FAQ数据
const defaultFAQData: FAQData = {
  question: "How do I get started with your platform?",
  answer: "Getting started is easy! Simply sign up for an account, complete the onboarding process, and you'll be ready to use all our features. Our step-by-step guide will walk you through the initial setup.",
  category: "Getting Started",
  tags: ["setup", "onboarding", "beginner"],
  priority: "medium",
  searchKeywords: ["start", "begin", "setup", "account", "registration"],
  relatedQuestions: [
    {
      question: "What features are included in the free plan?",
      link: "#free-plan"
    },
    {
      question: "How do I upgrade my account?",
      link: "#upgrade"
    }
  ],
  difficulty: "beginner",
  lastUpdated: new Date().toISOString().split('T')[0],
  isPublished: true,
  viewCount: 0,
  helpfulVotes: {
    positive: 0,
    negative: 0
  },
  seo: {
    metaTitle: "How to Get Started - FAQ | Your Platform",
    metaDescription: "Learn how to get started with our platform. Step-by-step guide for new users to set up their account and begin using our features."
  }
};

// 优先级配置
const priorityConfig = {
  low: { 
    label: 'Low Priority', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: CheckCircle
  },
  medium: { 
    label: 'Medium Priority', 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    icon: Clock
  },
  high: { 
    label: 'High Priority', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: AlertCircle
  },
  critical: { 
    label: 'Critical', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: AlertCircle
  }
} as const;

// 难度配置
const difficultyConfig = {
  beginner: { 
    label: 'Beginner', 
    color: 'text-green-700 bg-green-50 border-green-200'
  },
  intermediate: { 
    label: 'Intermediate', 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200'
  },
  advanced: { 
    label: 'Advanced', 
    color: 'text-red-700 bg-red-50 border-red-200'
  }
} as const;

const FAQEditorTestPage = () => {
  const [formData, setFormData] = useState<FAQData>(defaultFAQData);

  const handleSave = (data: FAQData) => {
    console.log('Saved FAQ data:', data);
    setFormData(data);
  };

  const PriorityIcon = priorityConfig[formData.priority].icon;
  const helpfulnessRatio = formData.helpfulVotes.positive + formData.helpfulVotes.negative > 0 
    ? (formData.helpfulVotes.positive / (formData.helpfulVotes.positive + formData.helpfulVotes.negative) * 100).toFixed(1)
    : 0;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <HelpCircle className="w-8 h-8 text-blue-600" />
            FAQ Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the FAQ content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              FAQ Editor
            </h2>
            <BlockEditorShadcn
              config={faqConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              FAQ Preview
            </h2>
            
            <div className="space-y-6">
              {/* FAQ标题和元信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formData.question}
                  </h3>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${priorityConfig[formData.priority].color}`}>
                      <PriorityIcon className="w-3 h-3" />
                      {priorityConfig[formData.priority].label}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${difficultyConfig[formData.difficulty].color}`}>
                      {difficultyConfig[formData.difficulty].label}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {formData.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formData.lastUpdated}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    {formData.viewCount} views
                  </span>
                  {formData.isPublished ? (
                    <span className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      Published
                    </span>
                  ) : (
                    <span className="flex items-center gap-1 text-gray-400">
                      <Clock className="w-4 h-4" />
                      Draft
                    </span>
                  )}
                </div>
              </div>

              {/* FAQ答案 */}
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown>{formData.answer}</ReactMarkdown>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 搜索关键词 */}
              {formData.searchKeywords.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Search className="w-4 h-4" />
                    Search Keywords
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.searchKeywords.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 相关问题 */}
              {formData.relatedQuestions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <ExternalLink className="w-4 h-4" />
                    Related Questions
                  </h4>
                  <ul className="space-y-2">
                    {formData.relatedQuestions.map((related, index) => (
                      <li key={index}>
                        <a
                          href={related.link}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center gap-1"
                        >
                          <HelpCircle className="w-3 h-3" />
                          {related.question}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 有用性投票 */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Helpfulness
                </h4>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <ThumbsUp className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {formData.helpfulVotes.positive} helpful
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ThumbsDown className="w-4 h-4 text-red-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {formData.helpfulVotes.negative} not helpful
                    </span>
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    ({helpfulnessRatio}% helpful)
                  </div>
                </div>
              </div>

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQEditorTestPage;
