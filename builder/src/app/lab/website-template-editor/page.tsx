/**
 * 网站模版编辑器测试页面
 * 
 * 用于测试 WebsiteTemplate 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import websiteTemplateConfig from '@/content-schema/WebsiteTemplate';
import { 
  Globe, 
  Edit, 
  Eye, 
  Star, 
  Clock, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Monitor,
  Tablet,
  Smartphone,
  Code,
  Palette,
  Zap,
  Shield,
  Settings,
  Download,
  ExternalLink,
  Play,
  Image,
  Layout,
  Layers,
  Package
} from 'lucide-react';

// 网站模版数据类型定义
interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

interface Feature {
  name: string;
  description: string;
  category: string;
  included: boolean;
}

interface Page {
  name: string;
  description: string;
  type: string;
  required: boolean;
}

interface Performance {
  loadTime: string;
  lighthouseScore: number;
  mobileOptimized: boolean;
}

interface Technical {
  framework: string;
  cssFramework: string;
  responsive: boolean;
  browserSupport: string[];
  performance: Performance;
}

interface Support {
  included: boolean;
  duration: string;
  channels: string[];
}

interface Pricing {
  type: string;
  price: number;
  currency: string;
  license: string;
  support: Support;
}

interface Credentials {
  username: string;
  password: string;
}

interface Demo {
  liveUrl?: string;
  adminUrl?: string;
  credentials?: Credentials;
}

interface TemplateImage {
  url: string;
  caption: string;
  alt: string;
  type: string;
  isPrimary: boolean;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface WebsiteTemplateData {
  title: string;
  slug: string;
  description: string;
  category: string;
  industry: string[];
  designStyle: string;
  colorScheme: ColorScheme;
  features: Feature[];
  pages: Page[];
  technical: Technical;
  pricing: Pricing;
  demo?: Demo;
  images: TemplateImage[];
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认网站模版数据
const defaultWebsiteTemplateData: WebsiteTemplateData = {
  title: "现代商务网站模版 - 专业企业展示解决方案",
  slug: "modern-business-website-template",
  description: "这是一个现代化的商务网站模版，专为中小企业和创业公司设计。包含首页、关于我们、服务介绍、案例展示、联系我们等完整页面，响应式设计，支持多种定制选项。",
  category: "business",
  industry: ["technology", "consulting"],
  designStyle: "modern",
  colorScheme: {
    primary: "#3B82F6",
    secondary: "#10B981",
    accent: "#F59E0B",
    background: "#FFFFFF",
    text: "#1F2937"
  },
  features: [
    {
      name: "响应式设计",
      description: "完美适配桌面、平板和手机设备",
      category: "design",
      included: true
    },
    {
      name: "SEO优化",
      description: "内置SEO最佳实践，提升搜索引擎排名",
      category: "seo",
      included: true
    },
    {
      name: "快速加载",
      description: "优化的代码结构，确保页面快速加载",
      category: "performance",
      included: true
    },
    {
      name: "联系表单",
      description: "内置联系表单，支持邮件通知",
      category: "functionality",
      included: true
    }
  ],
  pages: [
    {
      name: "首页",
      description: "企业主页，展示核心业务和价值主张",
      type: "home",
      required: true
    },
    {
      name: "关于我们",
      description: "公司介绍、团队展示和企业文化",
      type: "about",
      required: true
    },
    {
      name: "服务介绍",
      description: "详细的服务项目和解决方案",
      type: "services",
      required: true
    },
    {
      name: "联系我们",
      description: "联系方式、地址和联系表单",
      type: "contact",
      required: true
    }
  ],
  technical: {
    framework: "react",
    cssFramework: "tailwind",
    responsive: true,
    browserSupport: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"],
    performance: {
      loadTime: "< 3秒",
      lighthouseScore: 95,
      mobileOptimized: true
    }
  },
  pricing: {
    type: "premium",
    price: 299,
    currency: "CNY",
    license: "single-site",
    support: {
      included: true,
      duration: "6个月",
      channels: ["email", "documentation"]
    }
  },
  demo: {
    liveUrl: "https://demo.example.com",
    adminUrl: "https://demo.example.com/admin",
    credentials: {
      username: "demo",
      password: "demo123"
    }
  },
  images: [
    {
      url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=800&fit=crop",
      caption: "现代商务网站模版 - 桌面端首页展示",
      alt: "网站模版桌面端截图",
      type: "desktop",
      isPrimary: true
    },
    {
      url: "https://images.unsplash.com/photo-**********-87deedd944c3?w=800&h=1200&fit=crop",
      caption: "移动端响应式设计展示",
      alt: "网站模版移动端截图",
      type: "mobile",
      isPrimary: false
    }
  ],
  tags: ["商务", "企业", "响应式", "现代", "专业"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "现代商务网站模版 - 专业企业展示解决方案 | 网站模版",
    metaDescription: "专为中小企业设计的现代化商务网站模版，包含完整页面结构，响应式设计，SEO优化，快速部署。",
    keywords: ["网站模版", "商务模版", "企业网站", "响应式设计"]
  }
};

// 分类配置
const categoryConfig = {
  business: { label: '商务企业', icon: '💼', color: 'text-blue-600' },
  portfolio: { label: '作品集', icon: '🎨', color: 'text-purple-600' },
  ecommerce: { label: '电商', icon: '🛒', color: 'text-green-600' },
  blog: { label: '博客', icon: '📝', color: 'text-orange-600' },
  landing: { label: '落地页', icon: '🚀', color: 'text-red-600' },
  saas: { label: 'SaaS', icon: '☁️', color: 'text-cyan-600' },
  agency: { label: '代理机构', icon: '🏢', color: 'text-indigo-600' },
  restaurant: { label: '餐饮', icon: '🍽️', color: 'text-yellow-600' },
  education: { label: '教育', icon: '🎓', color: 'text-emerald-600' },
  healthcare: { label: '医疗', icon: '🏥', color: 'text-pink-600' },
  nonprofit: { label: '非营利', icon: '🤝', color: 'text-teal-600' },
  personal: { label: '个人', icon: '👤', color: 'text-gray-600' },
  other: { label: '其他', icon: '📦', color: 'text-gray-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const WebsiteTemplateEditorTestPage = () => {
  const [formData, setFormData] = useState<WebsiteTemplateData>(defaultWebsiteTemplateData);

  const handleSave = (data: WebsiteTemplateData) => {
    console.log('Saved website template data:', data);
    setFormData(data);
  };

  const categoryInfo = categoryConfig[formData.category as keyof typeof categoryConfig];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getFeatureIcon = (category: string) => {
    const icons = {
      design: Palette,
      functionality: Settings,
      performance: Zap,
      seo: Target,
      accessibility: Shield,
      integration: Layers,
      customization: Code
    };
    return icons[category as keyof typeof icons] || Package;
  };

  const getFrameworkLabel = (framework: string) => {
    const labels = {
      react: '⚛️ React',
      vue: '💚 Vue.js',
      angular: '🅰️ Angular',
      'html-css': '🌐 HTML/CSS',
      wordpress: '📝 WordPress',
      nextjs: '▲ Next.js',
      nuxtjs: '💚 Nuxt.js',
      gatsby: '🚀 Gatsby',
      other: '📦 其他'
    };
    return labels[framework as keyof typeof labels] || framework;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Globe className="w-8 h-8 text-blue-600" />
            Website Template Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试WebsiteTemplate内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              网站模版编辑器
            </h2>
            <BlockEditorShadcn
              config={websiteTemplateConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              网站模版预览
            </h2>
            
            <div className="space-y-6">
              {/* 模版头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${categoryInfo.color}`}>
                        {categoryInfo.icon} {categoryInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formData.designStyle}风格
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 价格和技术信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                      <DollarSign className="w-4 h-4" />
                      ¥{formData.pricing.price}
                    </div>
                    <div className="text-xs text-gray-500">{formData.pricing.type === 'free' ? '免费' : '付费'}</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600 flex items-center justify-center gap-1">
                      <Layout className="w-4 h-4" />
                      {formData.pages.length}
                    </div>
                    <div className="text-xs text-gray-500">页面数</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600 flex items-center justify-center gap-1">
                      <Code className="w-4 h-4" />
                      {getFrameworkLabel(formData.technical.framework).split(' ')[1]}
                    </div>
                    <div className="text-xs text-gray-500">技术框架</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 flex items-center justify-center gap-1">
                      <Zap className="w-4 h-4" />
                      {formData.technical.performance.lighthouseScore}
                    </div>
                    <div className="text-xs text-gray-500">性能评分</div>
                  </div>
                </div>

                {/* 主要图片 */}
                {formData.images.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.images[0].url}
                      alt={formData.images[0].alt}
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.images[0].caption}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteTemplateEditorTestPage;
