/**
 * 播客编辑器测试页面
 * 
 * 用于测试 Podcast 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import podcastConfig from '@/content-schema/Podcast';
import { 
  Mic, 
  Edit, 
  Eye, 
  Clock, 
  Play, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  Download,
  Share2,
  Star,
  DollarSign,
  Globe,
  Lock,
  User,
  Award,
  Headphones,
  Volume2,
  FileAudio,
  MessageSquare
} from 'lucide-react';

// 播客数据类型定义
interface AudioFile {
  url: string;
  format: string;
  duration: number;
  fileSize: number;
  quality: string;
  bitrate: number;
}

interface TranscriptSegment {
  startTime: number;
  endTime: number;
  text: string;
  speaker?: string;
  confidence?: number;
}

interface Transcript {
  fullText: string;
  timestampedSegments: TranscriptSegment[];
  language: string;
  accuracy: number;
}

interface Chapter {
  title: string;
  startTime: number;
  endTime?: number;
  description?: string;
  keywords?: string[];
}

interface Host {
  name: string;
  role: string;
  bio?: string;
  avatar?: string;
  social?: {
    website?: string;
    twitter?: string;
    linkedin?: string;
    email?: string;
  };
}

interface SeriesInfo {
  seriesTitle: string;
  seriesDescription: string;
  totalEpisodes: number;
}

interface Subscription {
  rssUrl: string;
  applePodcasts?: string;
  spotify?: string;
  googlePodcasts?: string;
  subscriberCount: number;
}

interface Analytics {
  totalPlays: number;
  uniqueListeners: number;
  averageListenTime: number;
  completionRate: number;
  downloads: number;
  shares: number;
  ratings: {
    average: number;
    count: number;
  };
}

interface Monetization {
  isPremium: boolean;
  price: number;
  currency: string;
  sponsorship?: {
    hasSponsor: boolean;
    sponsorName?: string;
    sponsorUrl?: string;
    sponsorMessage?: string;
  };
  donations?: {
    enabled: boolean;
    platforms?: Array<{
      platform: string;
      url: string;
    }>;
  };
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface PodcastData {
  title: string;
  slug: string;
  description: string;
  episodeNumber: number;
  season: number;
  seriesInfo: SeriesInfo;
  audioFile: AudioFile;
  transcript?: Transcript;
  chapters: Chapter[];
  hosts: Host[];
  category: string;
  tags: string[];
  language: string;
  explicit: boolean;
  copyright: string;
  subscription?: Subscription;
  analytics?: Analytics;
  monetization?: Monetization;
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认播客数据
const defaultPodcastData: PodcastData = {
  title: "AI技术前沿：深度学习在自然语言处理中的应用",
  slug: "ai-tech-frontier-nlp-deep-learning",
  description: "本期播客深入探讨人工智能技术的最新发展，重点分析深度学习在自然语言处理领域的突破性应用。我们将讨论GPT、BERT等模型的技术原理，以及它们在实际业务中的应用案例和未来发展趋势。",
  episodeNumber: 1,
  season: 1,
  seriesInfo: {
    seriesTitle: "AI技术前沿",
    seriesDescription: "探索人工智能技术的最新发展和应用趋势",
    totalEpisodes: 10
  },
  audioFile: {
    url: "https://example.com/podcasts/ai-tech-frontier-ep1.mp3",
    format: "mp3",
    duration: 2400,
    fileSize: 45,
    quality: "high",
    bitrate: 128
  },
  transcript: {
    fullText: "欢迎收听AI技术前沿播客。我是主播张三，今天我们要深入探讨深度学习在自然语言处理中的应用...\n\n[00:30] 首先，让我们回顾一下自然语言处理的发展历程...\n\n[02:15] 深度学习的引入彻底改变了NLP领域...",
    timestampedSegments: [
      {
        startTime: 0,
        endTime: 30,
        text: "欢迎收听AI技术前沿播客。我是主播张三，今天我们要深入探讨深度学习在自然语言处理中的应用。",
        speaker: "张三",
        confidence: 0.95
      },
      {
        startTime: 30,
        endTime: 135,
        text: "首先，让我们回顾一下自然语言处理的发展历程。从早期的规则基础方法，到统计机器学习，再到现在的深度学习时代。",
        speaker: "张三",
        confidence: 0.92
      }
    ],
    language: "zh-CN",
    accuracy: 0.95
  },
  chapters: [
    {
      title: "开场介绍",
      startTime: 0,
      endTime: 120,
      description: "播客介绍和今日话题概述",
      keywords: ["介绍", "AI", "深度学习"]
    },
    {
      title: "NLP发展历程",
      startTime: 120,
      endTime: 600,
      description: "回顾自然语言处理技术的发展历程",
      keywords: ["NLP", "历史", "发展"]
    },
    {
      title: "深度学习应用",
      startTime: 600,
      endTime: 1800,
      description: "深度学习在NLP中的具体应用案例",
      keywords: ["深度学习", "应用", "案例"]
    },
    {
      title: "未来展望",
      startTime: 1800,
      endTime: 2400,
      description: "技术发展趋势和未来展望",
      keywords: ["未来", "趋势", "展望"]
    }
  ],
  hosts: [
    {
      name: "张三",
      role: "host",
      bio: "AI技术专家，拥有10年机器学习研发经验，专注于自然语言处理和深度学习技术。",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      social: {
        website: "https://zhangsan.ai",
        twitter: "https://twitter.com/zhangsan_ai",
        email: "<EMAIL>"
      }
    }
  ],
  category: "technology",
  tags: ["AI", "深度学习", "NLP", "技术", "人工智能"],
  language: "zh-CN",
  explicit: false,
  copyright: "© 2024 AI技术前沿播客. 保留所有权利。",
  subscription: {
    rssUrl: "https://example.com/podcast/ai-tech-frontier/rss",
    subscriberCount: 0
  },
  analytics: {
    totalPlays: 0,
    uniqueListeners: 0,
    averageListenTime: 0,
    completionRate: 0,
    downloads: 0,
    shares: 0,
    ratings: {
      average: 0,
      count: 0
    }
  },
  monetization: {
    isPremium: false,
    price: 0,
    currency: "CNY",
    sponsorship: {
      hasSponsor: false
    },
    donations: {
      enabled: false
    }
  },
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "AI技术前沿：深度学习在自然语言处理中的应用 | 播客",
    metaDescription: "深入探讨人工智能技术的最新发展，分析深度学习在自然语言处理领域的突破性应用。专业技术播客，值得收听。",
    keywords: ["AI播客", "深度学习", "自然语言处理", "技术播客"]
  }
};

// 播客分类配置
const categoryConfig = {
  technology: { label: '科技', icon: '🔬', color: 'text-blue-600' },
  business: { label: '商业', icon: '💼', color: 'text-green-600' },
  education: { label: '教育', icon: '📚', color: 'text-purple-600' },
  entertainment: { label: '娱乐', icon: '🎭', color: 'text-pink-600' },
  news: { label: '新闻', icon: '📰', color: 'text-orange-600' },
  health: { label: '健康', icon: '🏥', color: 'text-red-600' },
  science: { label: '科学', icon: '🧪', color: 'text-indigo-600' },
  arts: { label: '艺术', icon: '🎨', color: 'text-yellow-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  scheduled: { label: '定时发布', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  private: { label: '私有', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const PodcastEditorTestPage = () => {
  const [formData, setFormData] = useState<PodcastData>(defaultPodcastData);

  const handleSave = (data: PodcastData) => {
    console.log('Saved podcast data:', data);
    setFormData(data);
  };

  const categoryInfo = categoryConfig[formData.category as keyof typeof categoryConfig];
  
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Mic className="w-8 h-8 text-blue-600" />
            Podcast Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Podcast内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              播客编辑器
            </h2>
            <BlockEditorShadcn
              config={podcastConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              播客预览
            </h2>
            
            <div className="space-y-6">
              {/* 播客头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${categoryInfo.color}`}>
                        {categoryInfo.icon} {categoryInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        第{formData.season}季 第{formData.episodeNumber}集
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDuration(formData.audioFile.duration)}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                    {formData.explicit && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
                        敏感内容
                      </span>
                    )}
                  </div>
                </div>

                {/* 播客播放器模拟 */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center gap-4">
                    <button className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                      <Play className="w-6 h-6 ml-1" />
                    </button>
                    <div className="flex-1">
                      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-1">
                        <span>00:00</span>
                        <span>{formatDuration(formData.audioFile.duration)}</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }} />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Volume2 className="w-5 h-5 text-gray-500" />
                      <Download className="w-5 h-5 text-gray-500 cursor-pointer hover:text-gray-700" />
                      <Share2 className="w-5 h-5 text-gray-500 cursor-pointer hover:text-gray-700" />
                    </div>
                  </div>
                </div>

                {/* 播客统计信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{formData.analytics?.totalPlays || 0}</div>
                    <div className="text-xs text-gray-500">播放次数</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{formData.analytics?.downloads || 0}</div>
                    <div className="text-xs text-gray-500">下载次数</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{formData.subscription?.subscriberCount || 0}</div>
                    <div className="text-xs text-gray-500">订阅人数</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">{formData.analytics?.shares || 0}</div>
                    <div className="text-xs text-gray-500">分享次数</div>
                  </div>
                </div>

                {/* 系列信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">
                    {formData.seriesInfo.seriesTitle}
                  </h4>
                  <div className="text-sm text-blue-700 dark:text-blue-400 mb-2">
                    {formData.seriesInfo.seriesDescription}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400">
                    共 {formData.seriesInfo.totalEpisodes} 集
                  </div>
                </div>
              </div>

              {/* 主播信息 */}
              {formData.hosts.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <User className="w-5 h-5" />
                    主播信息
                  </h4>
                  <div className="space-y-3">
                    {formData.hosts.map((host, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {host.avatar && (
                          <img
                            src={host.avatar}
                            alt={host.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {host.name}
                            </h5>
                            <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300">
                              {host.role === 'host' ? '主播' : host.role === 'co-host' ? '联合主播' : '嘉宾'}
                            </span>
                          </div>
                          {host.bio && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {host.bio}
                            </p>
                          )}
                          {host.social && (
                            <div className="flex items-center gap-3 text-sm">
                              {host.social.website && (
                                <a href={host.social.website} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  网站
                                </a>
                              )}
                              {host.social.twitter && (
                                <a href={host.social.twitter} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  Twitter
                                </a>
                              )}
                              {host.social.email && (
                                <a href={`mailto:${host.social.email}`} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  邮箱
                                </a>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 章节标记 */}
              {formData.chapters.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    章节标记
                  </h4>
                  <div className="space-y-2">
                    {formData.chapters.map((chapter, index) => (
                      <div key={index} className="flex items-center justify-between py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {chapter.title}
                          </h5>
                          {chapter.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {chapter.description}
                            </p>
                          )}
                          {chapter.keywords && chapter.keywords.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {chapter.keywords.map((keyword, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                                >
                                  {keyword}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 ml-4">
                          {formatDuration(chapter.startTime)}
                          {chapter.endTime && ` - ${formatDuration(chapter.endTime)}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 音频文件信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <FileAudio className="w-5 h-5" />
                  音频文件
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">格式:</span>
                      <span className="ml-2 text-gray-900 dark:text-white uppercase">
                        {formData.audioFile.format}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">时长:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formatDuration(formData.audioFile.duration)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">文件大小:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formData.audioFile.fileSize} MB
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">音质:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formData.audioFile.quality === 'high' ? '高音质' :
                         formData.audioFile.quality === 'lossless' ? '无损' :
                         formData.audioFile.quality === 'medium' ? '中等' : '低音质'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">比特率:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formData.audioFile.bitrate} kbps
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 转录文本 */}
              {formData.transcript && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    转录文本
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        语言: {formData.transcript.language === 'zh-CN' ? '中文' : formData.transcript.language}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        准确度: {(formData.transcript.accuracy * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div className="prose prose-sm max-w-none dark:prose-invert bg-white dark:bg-gray-800 rounded p-3 max-h-48 overflow-y-auto">
                      <pre className="whitespace-pre-wrap text-sm">
                        {formData.transcript.fullText}
                      </pre>
                    </div>

                    {formData.transcript.timestampedSegments.length > 0 && (
                      <div className="mt-4">
                        <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">时间戳分段</h5>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {formData.transcript.timestampedSegments.slice(0, 3).map((segment, index) => (
                            <div key={index} className="text-xs bg-white dark:bg-gray-800 rounded p-2">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-blue-600 dark:text-blue-400">
                                  {formatDuration(segment.startTime)} - {formatDuration(segment.endTime)}
                                </span>
                                {segment.speaker && (
                                  <span className="text-gray-500 dark:text-gray-400">
                                    {segment.speaker}
                                  </span>
                                )}
                              </div>
                              <p className="text-gray-700 dark:text-gray-300">
                                {segment.text}
                              </p>
                            </div>
                          ))}
                          {formData.transcript.timestampedSegments.length > 3 && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                              还有 {formData.transcript.timestampedSegments.length - 3} 个分段...
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 订阅信息 */}
              {formData.subscription && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Headphones className="w-5 h-5" />
                    订阅信息
                  </h4>
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-green-700 dark:text-green-300 font-medium">RSS订阅:</span>
                        <a
                          href={formData.subscription.rssUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-green-800 dark:text-green-200 hover:underline"
                        >
                          订阅链接
                        </a>
                      </div>
                      <div>
                        <span className="text-green-700 dark:text-green-300 font-medium">订阅人数:</span>
                        <span className="ml-2 text-green-800 dark:text-green-200">
                          {formData.subscription.subscriberCount.toLocaleString()}
                        </span>
                      </div>
                      {formData.subscription.applePodcasts && (
                        <div>
                          <span className="text-green-700 dark:text-green-300 font-medium">Apple Podcasts:</span>
                          <a
                            href={formData.subscription.applePodcasts}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 text-green-800 dark:text-green-200 hover:underline"
                          >
                            收听
                          </a>
                        </div>
                      )}
                      {formData.subscription.spotify && (
                        <div>
                          <span className="text-green-700 dark:text-green-300 font-medium">Spotify:</span>
                          <a
                            href={formData.subscription.spotify}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 text-green-800 dark:text-green-200 hover:underline"
                          >
                            收听
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* 播放统计 */}
              {formData.analytics && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    播放统计
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">{formData.analytics.uniqueListeners}</div>
                      <div className="text-xs text-gray-500">独立听众</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-green-600">
                        {formatDuration(formData.analytics.averageListenTime)}
                      </div>
                      <div className="text-xs text-gray-500">平均收听时长</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">
                        {formData.analytics.completionRate.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-500">完成率</div>
                    </div>
                  </div>

                  {formData.analytics.ratings.count > 0 && (
                    <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex items-center">
                          {renderStars(formData.analytics.ratings.average)}
                        </div>
                        <span className="text-sm text-yellow-700 dark:text-yellow-300">
                          {formData.analytics.ratings.average.toFixed(1)} ({formData.analytics.ratings.count} 评价)
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 变现设置 */}
              {formData.monetization && (formData.monetization.isPremium || formData.monetization.sponsorship?.hasSponsor) && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    变现信息
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    {formData.monetization.isPremium && (
                      <div className="mb-3">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                          付费内容 - ¥{formData.monetization.price}
                        </span>
                      </div>
                    )}

                    {formData.monetization.sponsorship?.hasSponsor && (
                      <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                        <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">赞助商</h5>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {formData.monetization.sponsorship.sponsorName && (
                            <div>
                              <span className="font-medium">赞助商:</span> {formData.monetization.sponsorship.sponsorName}
                            </div>
                          )}
                          {formData.monetization.sponsorship.sponsorMessage && (
                            <div className="mt-1">
                              <span className="font-medium">赞助信息:</span> {formData.monetization.sponsorship.sponsorMessage}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    播客标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* 版权信息 */}
              <div className="text-center text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-4">
                {formData.copyright}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PodcastEditorTestPage;
