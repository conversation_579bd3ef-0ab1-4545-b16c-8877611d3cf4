/**
 * 活动编辑器测试页面
 * 
 * 用于测试 Event 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import eventConfig from '@/content-schema/Event';
import { 
  Calendar, 
  Edit, 
  Eye, 
  Clock, 
  MapPin, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  DollarSign,
  Globe,
  Lock,
  User,
  Award,
  Building,
  Mail,
  Phone,
  ExternalLink
} from 'lucide-react';

// 活动数据类型定义
interface Location {
  type: 'online' | 'offline' | 'hybrid';
  venue?: string;
  address?: string;
  city?: string;
  country?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  onlineUrl?: string;
  onlinePlatform?: string;
}

interface Capacity {
  maxAttendees: number;
  currentAttendees: number;
  waitlistEnabled: boolean;
  waitlistCount: number;
}

interface Registration {
  isRequired?: boolean;
  registrationUrl?: string;
  registrationDeadline?: string;
  approvalRequired?: boolean;
  fee?: {
    isFree: boolean;
    currency: string;
    earlyBirdPrice: number;
    regularPrice: number;
    studentPrice: number;
    earlyBirdDeadline?: string;
  };
}

interface AgendaItem {
  time: string;
  duration: number;
  title: string;
  description?: string;
  type: 'keynote' | 'session' | 'workshop' | 'panel' | 'break' | 'networking' | 'lunch';
  speaker?: string;
  location?: string;
  isBreak: boolean;
}

interface Speaker {
  name: string;
  title: string;
  company?: string;
  bio?: string;
  avatar?: string;
  social?: {
    website?: string;
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
  topics?: string[];
}

interface Sponsor {
  name: string;
  logo?: string;
  website?: string;
  level: 'platinum' | 'gold' | 'silver' | 'bronze' | 'partner';
  description?: string;
}

interface Organizer {
  name: string;
  logo?: string;
  website?: string;
  contact?: {
    email?: string;
    phone?: string;
  };
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface EventData {
  title: string;
  slug: string;
  description: string;
  type: 'conference' | 'workshop' | 'webinar' | 'meetup' | 'training' | 'networking' | 'exhibition' | 'competition';
  category: string;
  tags: string[];
  startDate: string;
  endDate: string;
  timezone: string;
  location: Location;
  capacity: Capacity;
  registration?: Registration;
  agenda: AgendaItem[];
  speakers: Speaker[];
  sponsors: Sponsor[];
  organizer: Organizer;
  status: 'draft' | 'published' | 'registration-open' | 'registration-closed' | 'ongoing' | 'completed' | 'cancelled';
  visibility: 'public' | 'private' | 'invite-only';
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认活动数据
const defaultEventData: EventData = {
  title: "2024年前端技术大会：探索Web开发的未来",
  slug: "frontend-tech-conference-2024",
  description: "加入我们的年度前端技术大会，与行业专家和开发者一起探讨最新的Web开发技术趋势。本次大会将涵盖React、Vue、Angular等主流框架的最新发展，以及AI在前端开发中的应用。无论你是初学者还是资深开发者，都能在这里找到有价值的内容和灵感。",
  type: "conference",
  category: "技术会议",
  tags: ["前端开发", "技术大会", "Web技术", "React"],
  startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  endDate: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000).toISOString(),
  timezone: "Asia/Shanghai",
  location: {
    type: "hybrid",
    venue: "上海国际会议中心",
    address: "上海市浦东新区滨江大道2727号",
    city: "上海",
    country: "中国",
    coordinates: {
      latitude: 31.2304,
      longitude: 121.4737
    },
    onlineUrl: "https://zoom.us/j/123456789",
    onlinePlatform: "zoom"
  },
  capacity: {
    maxAttendees: 500,
    currentAttendees: 0,
    waitlistEnabled: true,
    waitlistCount: 0
  },
  registration: {
    isRequired: true,
    registrationUrl: "https://example.com/register/frontend-conference-2024",
    approvalRequired: false,
    fee: {
      isFree: false,
      currency: "CNY",
      earlyBirdPrice: 299,
      regularPrice: 399,
      studentPrice: 199
    }
  },
  agenda: [
    {
      time: "09:00",
      duration: 30,
      title: "签到和早餐",
      description: "参会者签到，享用早餐，网络交流",
      type: "networking",
      location: "大厅",
      isBreak: true
    },
    {
      time: "09:30",
      duration: 60,
      title: "开幕主题演讲：前端技术的未来趋势",
      description: "探讨前端技术的发展方向和未来机遇",
      type: "keynote",
      speaker: "张三 - 前端技术专家",
      location: "主会场",
      isBreak: false
    },
    {
      time: "10:30",
      duration: 15,
      title: "茶歇",
      type: "break",
      location: "休息区",
      isBreak: true
    }
  ],
  speakers: [
    {
      name: "张三",
      title: "前端技术专家",
      company: "TechCorp",
      bio: "拥有10年前端开发经验，React核心贡献者，专注于现代Web技术和性能优化。",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      social: {
        website: "https://zhangsan.dev",
        github: "https://github.com/zhangsan",
        twitter: "https://twitter.com/zhangsan"
      },
      topics: ["React最新特性", "前端性能优化"]
    }
  ],
  sponsors: [],
  organizer: {
    name: "前端开发者社区",
    logo: "https://example.com/organizer-logo.png",
    website: "https://frontend-community.org",
    contact: {
      email: "<EMAIL>",
      phone: "+86 138 0013 8000"
    }
  },
  status: "draft",
  visibility: "public",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "2024年前端技术大会 - 探索Web开发的未来 | 技术会议",
    metaDescription: "参加2024年前端技术大会，与行业专家探讨React、Vue、AI等最新Web技术。线上线下同步进行，立即报名参与。",
    keywords: ["前端大会", "技术会议", "Web开发", "React会议"]
  }
};

// 活动类型配置
const typeConfig = {
  conference: { label: '会议', icon: '🎤', color: 'text-blue-600' },
  workshop: { label: '工作坊', icon: '🛠️', color: 'text-green-600' },
  webinar: { label: '网络研讨会', icon: '💻', color: 'text-purple-600' },
  meetup: { label: '聚会', icon: '🤝', color: 'text-orange-600' },
  training: { label: '培训', icon: '📚', color: 'text-indigo-600' },
  networking: { label: '社交活动', icon: '🌐', color: 'text-pink-600' },
  exhibition: { label: '展览', icon: '🏛️', color: 'text-gray-600' },
  competition: { label: '竞赛', icon: '🏆', color: 'text-yellow-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  published: { label: '已发布', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  'registration-open': { label: '报名开放', color: 'text-green-700 bg-green-50 border-green-200' },
  'registration-closed': { label: '报名截止', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' },
  ongoing: { label: '进行中', color: 'text-purple-700 bg-purple-50 border-purple-200' },
  completed: { label: '已完成', color: 'text-green-700 bg-green-50 border-green-200' },
  cancelled: { label: '已取消', color: 'text-red-700 bg-red-50 border-red-200' }
} as const;

// 地点类型配置
const locationTypeConfig = {
  online: { label: '线上活动', icon: Globe, color: 'text-blue-600' },
  offline: { label: '线下活动', icon: MapPin, color: 'text-green-600' },
  hybrid: { label: '线上线下混合', icon: Users, color: 'text-purple-600' }
} as const;

const EventEditorTestPage = () => {
  const [formData, setFormData] = useState<EventData>(defaultEventData);

  const handleSave = (data: EventData) => {
    console.log('Saved event data:', data);
    setFormData(data);
  };

  const typeInfo = typeConfig[formData.type];
  const LocationIcon = locationTypeConfig[formData.location.type].icon;
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventDuration = () => {
    const start = new Date(formData.startDate);
    const end = new Date(formData.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Calendar className="w-8 h-8 text-blue-600" />
            Event Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Event内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              活动编辑器
            </h2>
            <BlockEditorShadcn
              config={eventConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              活动预览
            </h2>
            
            <div className="space-y-6">
              {/* 活动头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${typeInfo.color}`}>
                        {typeInfo.icon} {typeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {getEventDuration()} 天
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status].color}`}>
                      {statusConfig[formData.status].label}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${locationTypeConfig[formData.location.type].color}`}>
                      <LocationIcon className="w-3 h-3" />
                      {locationTypeConfig[formData.location.type].label}
                    </span>
                  </div>
                </div>

                {/* 活动基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">开始时间</div>
                      <div className="text-xs text-gray-500">{formatDate(formData.startDate)}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Clock className="w-5 h-5 text-green-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">结束时间</div>
                      <div className="text-xs text-gray-500">{formatDate(formData.endDate)}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Users className="w-5 h-5 text-purple-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">参会人数</div>
                      <div className="text-xs text-gray-500">
                        {formData.capacity.currentAttendees} / {formData.capacity.maxAttendees}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <MapPin className="w-5 h-5 text-orange-600" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">地点</div>
                      <div className="text-xs text-gray-500">
                        {formData.location.venue || formData.location.city}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 报名信息 */}
                {formData.registration && formData.registration.isRequired && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-1">
                          活动报名
                        </h4>
                        <div className="text-sm text-blue-700 dark:text-blue-400">
                          {formData.registration.fee?.isFree ? '免费参加' : 
                           `早鸟价: ¥${formData.registration.fee?.earlyBirdPrice} | 正常价: ¥${formData.registration.fee?.regularPrice}`}
                        </div>
                      </div>
                      <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        立即报名
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 活动议程 */}
              {formData.agenda.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    活动议程
                  </h4>
                  <div className="space-y-3">
                    {formData.agenda.map((item, index) => (
                      <div key={index} className={`flex items-start gap-4 p-4 rounded-lg border ${item.isBreak ? 'bg-gray-50 border-gray-200 dark:bg-gray-700 dark:border-gray-600' : 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700'}`}>
                        <div className="text-sm font-mono text-gray-500 dark:text-gray-400 min-w-[60px]">
                          {item.time}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {item.title}
                            </h5>
                            <span className="text-xs px-2 py-1 rounded bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300">
                              {item.duration}分钟
                            </span>
                          </div>
                          {item.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {item.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                            {item.speaker && (
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {item.speaker}
                              </span>
                            )}
                            {item.location && (
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {item.location}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 演讲者 */}
              {formData.speakers.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <User className="w-5 h-5" />
                    演讲者
                  </h4>
                  <div className="grid grid-cols-1 gap-4">
                    {formData.speakers.map((speaker, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {speaker.avatar && (
                          <img
                            src={speaker.avatar}
                            alt={speaker.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {speaker.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {speaker.title}
                            {speaker.company && ` @ ${speaker.company}`}
                          </p>
                          {speaker.bio && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {speaker.bio}
                            </p>
                          )}
                          {speaker.topics && speaker.topics.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-2">
                              {speaker.topics.map((topic, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                                >
                                  {topic}
                                </span>
                              ))}
                            </div>
                          )}
                          {speaker.social && (
                            <div className="flex items-center gap-3">
                              {speaker.social.website && (
                                <a href={speaker.social.website} className="text-blue-600 hover:text-blue-800 text-sm">
                                  <ExternalLink className="w-4 h-4" />
                                </a>
                              )}
                              {speaker.social.github && (
                                <a href={speaker.social.github} className="text-blue-600 hover:text-blue-800 text-sm">
                                  GitHub
                                </a>
                              )}
                              {speaker.social.twitter && (
                                <a href={speaker.social.twitter} className="text-blue-600 hover:text-blue-800 text-sm">
                                  Twitter
                                </a>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 赞助商 */}
              {formData.sponsors.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    赞助商
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.sponsors.map((sponsor, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {sponsor.logo && (
                          <img
                            src={sponsor.logo}
                            alt={sponsor.name}
                            className="w-12 h-12 object-contain"
                          />
                        )}
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {sponsor.name}
                          </h5>
                          <span className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300">
                            {sponsor.level === 'platinum' ? '白金赞助商' :
                             sponsor.level === 'gold' ? '金牌赞助商' :
                             sponsor.level === 'silver' ? '银牌赞助商' :
                             sponsor.level === 'bronze' ? '铜牌赞助商' : '合作伙伴'}
                          </span>
                          {sponsor.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {sponsor.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 主办方信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  主办方
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-3">
                    {formData.organizer.logo && (
                      <img
                        src={formData.organizer.logo}
                        alt={formData.organizer.name}
                        className="w-16 h-8 object-contain bg-white rounded"
                      />
                    )}
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {formData.organizer.name}
                      </h5>
                      {formData.organizer.website && (
                        <a
                          href={formData.organizer.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          访问官网
                        </a>
                      )}
                    </div>
                  </div>

                  {formData.organizer.contact && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {formData.organizer.contact.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-gray-500" />
                          <a
                            href={`mailto:${formData.organizer.contact.email}`}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            {formData.organizer.contact.email}
                          </a>
                        </div>
                      )}
                      {formData.organizer.contact.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-600 dark:text-gray-300">
                            {formData.organizer.contact.phone}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 地点详情 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  活动地点
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">类型:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {locationTypeConfig[formData.location.type].label}
                      </span>
                    </div>
                    {formData.location.venue && (
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">场地:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formData.location.venue}
                        </span>
                      </div>
                    )}
                    {formData.location.address && (
                      <div className="col-span-2">
                        <span className="text-gray-500 dark:text-gray-400">地址:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formData.location.address}
                        </span>
                      </div>
                    )}
                    {formData.location.onlineUrl && (
                      <div className="col-span-2">
                        <span className="text-gray-500 dark:text-gray-400">在线链接:</span>
                        <a
                          href={formData.location.onlineUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          加入会议
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    活动标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventEditorTestPage;
