/**
 * 评测编辑器测试页面
 * 
 * 用于测试 Review 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import reviewConfig from '@/content-schema/Review';
import { 
  Star, 
  Edit, 
  Eye, 
  ThumbsUp, 
  ThumbsDown, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Zap,
  Shield,
  Clock
} from 'lucide-react';

// 评测数据类型定义
interface Price {
  amount: number;
  currency: string;
  priceRange: string;
}

interface Specification {
  name: string;
  value: string;
  category: string;
}

interface ProductImage {
  url: string;
  caption: string;
  alt: string;
}

interface ReviewedItem {
  name: string;
  brand: string;
  model: string;
  category: string;
  price: Price;
  specifications: Specification[];
  images: ProductImage[];
}

interface OverallRating {
  score: number;
  maxScore: number;
  recommendation: string;
}

interface DetailedRating {
  aspect: string;
  score: number;
  maxScore: number;
  weight: number;
  comment: string;
}

interface ProsCons {
  pros: Array<{
    title: string;
    description: string;
    importance: string;
  }>;
  cons: Array<{
    title: string;
    description: string;
    severity: string;
  }>;
}

interface TargetAudience {
  primary: string[];
  notRecommended: string[];
  useCase: string[];
}

interface Competitor {
  name: string;
  brand: string;
  price: number;
  advantages: string[];
  disadvantages: string[];
}

interface Alternative {
  name: string;
  reason: string;
  priceAdvantage: boolean;
}

interface Comparison {
  competitors: Competitor[];
  alternatives: Alternative[];
}

interface TestingMethod {
  duration: string;
  environment: string;
  methodology: string[];
}

interface Conclusion {
  summary: string;
  verdict: string;
  buyRecommendation: string;
}

interface Reviewer {
  name: string;
  title: string;
  bio: string;
  avatar: string;
  social?: {
    website?: string;
    twitter?: string;
    weibo?: string;
  };
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface ReviewData {
  title: string;
  slug: string;
  description: string;
  reviewedItem: ReviewedItem;
  overallRating: OverallRating;
  detailedRatings: DetailedRating[];
  prosAndCons: ProsCons;
  targetAudience: TargetAudience;
  comparison: Comparison;
  testingMethod: TestingMethod;
  conclusion: Conclusion;
  reviewer: Reviewer;
  category: string;
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认评测数据
const defaultReviewData: ReviewData = {
  title: "iPhone 15 Pro 深度评测：AI摄影与钛金属设计的完美结合",
  slug: "iphone-15-pro-comprehensive-review",
  description: "经过两周的深度使用，我们对iPhone 15 Pro进行了全面评测。从全新的钛金属设计到强大的A17 Pro芯片，再到革命性的AI摄影功能，这款手机在多个方面都有显著提升。本评测将详细分析其优缺点，帮助你做出购买决策。",
  reviewedItem: {
    name: "Apple iPhone 15 Pro",
    brand: "Apple",
    model: "iPhone 15 Pro",
    category: "smartphone",
    price: {
      amount: 7999,
      currency: "CNY",
      priceRange: "premium"
    },
    specifications: [
      {
        name: "屏幕尺寸",
        value: "6.1英寸 Super Retina XDR OLED",
        category: "display"
      },
      {
        name: "处理器",
        value: "A17 Pro 芯片",
        category: "performance"
      },
      {
        name: "摄像头",
        value: "48MP 主摄 + 12MP 超广角 + 12MP 长焦",
        category: "camera"
      },
      {
        name: "存储",
        value: "128GB/256GB/512GB/1TB",
        category: "storage"
      }
    ],
    images: [
      {
        url: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop",
        caption: "iPhone 15 Pro 钛金属外观",
        alt: "iPhone 15 Pro 产品图片"
      }
    ]
  },
  overallRating: {
    score: 8.5,
    maxScore: 10,
    recommendation: "recommended"
  },
  detailedRatings: [
    {
      aspect: "设计与工艺",
      score: 9.0,
      maxScore: 10,
      weight: 0.2,
      comment: "钛金属材质手感出色，边框设计更加圆润，整体质感显著提升"
    },
    {
      aspect: "性能表现",
      score: 9.5,
      maxScore: 10,
      weight: 0.25,
      comment: "A17 Pro芯片性能强劲，游戏和多任务处理都非常流畅"
    },
    {
      aspect: "摄影能力",
      score: 8.5,
      maxScore: 10,
      weight: 0.25,
      comment: "AI摄影功能实用，夜景模式有改进，但变焦能力仍有提升空间"
    },
    {
      aspect: "续航表现",
      score: 7.5,
      maxScore: 10,
      weight: 0.15,
      comment: "日常使用一天无压力，但重度使用仍需要充电"
    },
    {
      aspect: "性价比",
      score: 7.0,
      maxScore: 10,
      weight: 0.15,
      comment: "价格较高，但考虑到功能和品质，性价比尚可接受"
    }
  ],
  prosAndCons: {
    pros: [
      {
        title: "钛金属设计质感出色",
        description: "全新的钛金属材质不仅减轻了重量，还提供了更好的手感和耐用性",
        importance: "high"
      },
      {
        title: "A17 Pro芯片性能强劲",
        description: "3nm工艺的A17 Pro芯片在性能和能效方面都有显著提升",
        importance: "critical"
      },
      {
        title: "AI摄影功能实用",
        description: "智能场景识别和计算摄影功能让拍照变得更加简单",
        importance: "high"
      }
    ],
    cons: [
      {
        title: "价格偏高",
        description: "起售价7999元，对于普通消费者来说价格门槛较高",
        severity: "major"
      },
      {
        title: "续航仍有提升空间",
        description: "虽然比前代有改进，但重度使用仍需要一天多次充电",
        severity: "moderate"
      }
    ]
  },
  targetAudience: {
    primary: ["苹果生态用户", "摄影爱好者", "商务人士"],
    notRecommended: ["预算有限用户", "安卓深度用户"],
    useCase: ["日常通讯", "移动办公", "摄影创作", "游戏娱乐"]
  },
  comparison: {
    competitors: [
      {
        name: "Samsung Galaxy S24 Ultra",
        brand: "Samsung",
        price: 8999,
        advantages: ["S Pen支持", "更大屏幕", "更强变焦"],
        disadvantages: ["Android生态", "价格更高"]
      }
    ],
    alternatives: [
      {
        name: "iPhone 14 Pro",
        reason: "如果预算有限，iPhone 14 Pro仍然是很好的选择，性能差距不大但价格更实惠",
        priceAdvantage: true
      }
    ]
  },
  testingMethod: {
    duration: "2周深度使用",
    environment: "日常使用环境，包括办公、娱乐、拍照等多种场景",
    methodology: ["性能跑分测试", "续航测试", "摄影样张对比", "日常使用体验"]
  },
  conclusion: {
    summary: "iPhone 15 Pro是一款优秀的旗舰手机，钛金属设计和A17 Pro芯片都是亮点。虽然价格较高，但对于追求品质和性能的用户来说，仍然值得推荐。",
    verdict: "very-good",
    buyRecommendation: "buy-now"
  },
  reviewer: {
    name: "张三",
    title: "科技评测编辑",
    bio: "资深科技产品评测师，专注于消费电子产品评测5年，对手机、笔记本等数码产品有深入了解。",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    social: {
      website: "https://zhangsan.tech",
      twitter: "https://twitter.com/zhangsan_tech"
    }
  },
  category: "smartphone",
  tags: ["iPhone", "苹果", "手机评测", "旗舰手机"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "iPhone 15 Pro 深度评测：AI摄影与钛金属设计完美结合 | 科技评测",
    metaDescription: "iPhone 15 Pro 全面评测，详细分析钛金属设计、A17 Pro芯片性能、AI摄影功能。包含优缺点对比、购买建议，帮你做出明智选择。",
    keywords: ["iPhone 15 Pro评测", "苹果手机", "钛金属", "A17 Pro"]
  }
};

// 产品分类配置
const categoryConfig = {
  smartphone: { label: '智能手机', icon: '📱', color: 'text-blue-600' },
  laptop: { label: '笔记本电脑', icon: '💻', color: 'text-green-600' },
  tablet: { label: '平板电脑', icon: '📱', color: 'text-purple-600' },
  smartwatch: { label: '智能手表', icon: '⌚', color: 'text-orange-600' },
  headphones: { label: '耳机', icon: '🎧', color: 'text-pink-600' },
  camera: { label: '相机', icon: '📷', color: 'text-indigo-600' },
  gaming: { label: '游戏设备', icon: '🎮', color: 'text-red-600' },
  software: { label: '软件', icon: '💿', color: 'text-yellow-600' },
  service: { label: '服务', icon: '🔧', color: 'text-teal-600' },
  other: { label: '其他', icon: '📦', color: 'text-gray-600' }
} as const;

// 推荐度配置
const recommendationConfig = {
  'highly-recommended': { label: '强烈推荐', color: 'text-green-700 bg-green-50 border-green-200', icon: '🌟' },
  'recommended': { label: '推荐', color: 'text-blue-700 bg-blue-50 border-blue-200', icon: '👍' },
  'conditional': { label: '有条件推荐', color: 'text-yellow-700 bg-yellow-50 border-yellow-200', icon: '🤔' },
  'not-recommended': { label: '不推荐', color: 'text-red-700 bg-red-50 border-red-200', icon: '👎' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const ReviewEditorTestPage = () => {
  const [formData, setFormData] = useState<ReviewData>(defaultReviewData);

  const handleSave = (data: ReviewData) => {
    console.log('Saved review data:', data);
    setFormData(data);
  };

  const categoryInfo = categoryConfig[formData.category as keyof typeof categoryConfig];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const renderStars = (rating: number, maxRating: number = 10) => {
    const stars = Math.round((rating / maxRating) * 5);
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getImportanceColor = (importance: string) => {
    const colors = {
      low: 'text-green-600',
      medium: 'text-yellow-600',
      high: 'text-orange-600',
      critical: 'text-red-600'
    };
    return colors[importance as keyof typeof colors] || colors.medium;
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      minor: 'text-green-600',
      moderate: 'text-yellow-600',
      major: 'text-orange-600',
      critical: 'text-red-600'
    };
    return colors[severity as keyof typeof colors] || colors.moderate;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Star className="w-8 h-8 text-blue-600" />
            Review Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Review内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              评测编辑器
            </h2>
            <BlockEditorShadcn
              config={reviewConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              评测预览
            </h2>
            
            <div className="space-y-6">
              {/* 评测头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${categoryInfo.color}`}>
                        {categoryInfo.icon} {categoryInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formData.testingMethod.duration}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 总体评分 */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="text-3xl font-bold text-blue-600">
                          {formData.overallRating.score}
                        </div>
                        <div className="text-gray-500">/ {formData.overallRating.maxScore}</div>
                        <div className="flex items-center ml-2">
                          {renderStars(formData.overallRating.score, formData.overallRating.maxScore)}
                        </div>
                      </div>
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${recommendationConfig[formData.overallRating.recommendation as keyof typeof recommendationConfig].color}`}>
                        {recommendationConfig[formData.overallRating.recommendation as keyof typeof recommendationConfig].icon} {recommendationConfig[formData.overallRating.recommendation as keyof typeof recommendationConfig].label}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-600 dark:text-gray-300 mb-1">
                        评测产品
                      </div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {formData.reviewedItem.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        ¥{formData.reviewedItem.price.amount.toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 评测者信息 */}
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <img
                    src={formData.reviewer.avatar}
                    alt={formData.reviewer.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {formData.reviewer.name}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.reviewer.title}
                    </div>
                  </div>
                </div>
              </div>

              {/* 详细评分 */}
              {formData.detailedRatings.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    详细评分
                  </h4>
                  <div className="space-y-3">
                    {formData.detailedRatings.map((rating, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {rating.aspect}
                          </h5>
                          <div className="flex items-center gap-2">
                            <div className="text-lg font-bold text-blue-600">
                              {rating.score}
                            </div>
                            <div className="text-gray-500">/ {rating.maxScore}</div>
                            <div className="flex items-center">
                              {renderStars(rating.score, rating.maxScore)}
                            </div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {rating.comment}
                        </p>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(rating.score / rating.maxScore) * 100}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 优缺点分析 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  优缺点分析
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 优点 */}
                  <div>
                    <h5 className="font-medium text-green-700 dark:text-green-300 mb-3 flex items-center gap-2">
                      <ThumbsUp className="w-4 h-4" />
                      优点
                    </h5>
                    <div className="space-y-2">
                      {formData.prosAndCons.pros.map((pro, index) => (
                        <div key={index} className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                          <div className="flex items-start justify-between mb-1">
                            <h6 className="font-medium text-green-900 dark:text-green-300">
                              {pro.title}
                            </h6>
                            <span className={`text-xs ${getImportanceColor(pro.importance)}`}>
                              {pro.importance === 'critical' ? '🔴 关键' :
                               pro.importance === 'high' ? '🟠 高' :
                               pro.importance === 'medium' ? '🟡 中' : '🟢 低'}
                            </span>
                          </div>
                          <p className="text-sm text-green-800 dark:text-green-400">
                            {pro.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 缺点 */}
                  <div>
                    <h5 className="font-medium text-red-700 dark:text-red-300 mb-3 flex items-center gap-2">
                      <ThumbsDown className="w-4 h-4" />
                      缺点
                    </h5>
                    <div className="space-y-2">
                      {formData.prosAndCons.cons.map((con, index) => (
                        <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                          <div className="flex items-start justify-between mb-1">
                            <h6 className="font-medium text-red-900 dark:text-red-300">
                              {con.title}
                            </h6>
                            <span className={`text-xs ${getSeverityColor(con.severity)}`}>
                              {con.severity === 'critical' ? '🔴 致命' :
                               con.severity === 'major' ? '🟠 严重' :
                               con.severity === 'moderate' ? '🟡 中等' : '🟢 轻微'}
                            </span>
                          </div>
                          <p className="text-sm text-red-800 dark:text-red-400">
                            {con.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 适用人群 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  适用人群
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">推荐用户</h5>
                    <div className="space-y-1">
                      {formData.targetAudience.primary.map((user, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-green-100 text-green-700 border border-green-200 rounded mr-1 mb-1"
                        >
                          ✅ {user}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">不推荐用户</h5>
                    <div className="space-y-1">
                      {formData.targetAudience.notRecommended.map((user, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-red-100 text-red-700 border border-red-200 rounded mr-1 mb-1"
                        >
                          ❌ {user}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">使用场景</h5>
                    <div className="space-y-1">
                      {formData.targetAudience.useCase.map((useCase, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-700 border border-blue-200 rounded mr-1 mb-1"
                        >
                          📱 {useCase}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 竞品对比 */}
              {formData.comparison.competitors.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    竞品对比
                  </h4>
                  <div className="space-y-4">
                    {formData.comparison.competitors.map((competitor, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {competitor.name}
                          </h5>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            ¥{competitor.price.toLocaleString()}
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h6 className="text-sm font-medium text-green-700 dark:text-green-300 mb-2 flex items-center gap-1">
                              <TrendingUp className="w-3 h-3" />
                              相对优势
                            </h6>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              {competitor.advantages.map((advantage, idx) => (
                                <li key={idx} className="flex items-start gap-1">
                                  <span className="text-green-500 mt-0.5">+</span>
                                  {advantage}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h6 className="text-sm font-medium text-red-700 dark:text-red-300 mb-2 flex items-center gap-1">
                              <TrendingDown className="w-3 h-3" />
                              相对劣势
                            </h6>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              {competitor.disadvantages.map((disadvantage, idx) => (
                                <li key={idx} className="flex items-start gap-1">
                                  <span className="text-red-500 mt-0.5">-</span>
                                  {disadvantage}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 替代方案 */}
              {formData.comparison.alternatives.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    替代方案
                  </h4>
                  <div className="space-y-3">
                    {formData.comparison.alternatives.map((alternative, index) => (
                      <div key={index} className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="font-medium text-yellow-900 dark:text-yellow-300">
                            {alternative.name}
                          </h5>
                          {alternative.priceAdvantage && (
                            <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-700">
                              <DollarSign className="w-3 h-3 mr-1" />
                              价格优势
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-yellow-800 dark:text-yellow-400">
                          {alternative.reason}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 测试方法 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  测试方法
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">测试周期:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formData.testingMethod.duration}
                      </span>
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-gray-500 dark:text-gray-400">测试环境:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {formData.testingMethod.environment}
                      </span>
                    </div>
                  </div>

                  {formData.testingMethod.methodology.length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">测试方法</h5>
                      <div className="flex flex-wrap gap-2">
                        {formData.testingMethod.methodology.map((method, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                          >
                            {method}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 评测结论 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  评测结论
                </h4>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-lg">
                      {formData.conclusion.verdict === 'excellent' ? '🌟' :
                       formData.conclusion.verdict === 'very-good' ? '👍' :
                       formData.conclusion.verdict === 'good' ? '😊' :
                       formData.conclusion.verdict === 'average' ? '😐' : '👎'}
                    </span>
                    <span className="font-medium text-blue-900 dark:text-blue-300">
                      {formData.conclusion.verdict === 'excellent' ? '优秀' :
                       formData.conclusion.verdict === 'very-good' ? '很好' :
                       formData.conclusion.verdict === 'good' ? '良好' :
                       formData.conclusion.verdict === 'average' ? '一般' : '较差'}
                    </span>
                    <span className="text-sm px-2 py-1 rounded bg-blue-100 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300">
                      {formData.conclusion.buyRecommendation === 'buy-now' ? '💰 立即购买' :
                       formData.conclusion.buyRecommendation === 'wait-for-discount' ? '⏰ 等待降价' :
                       formData.conclusion.buyRecommendation === 'consider-alternatives' ? '🤔 考虑其他' : '❌ 避免购买'}
                    </span>
                  </div>
                  <p className="text-blue-800 dark:text-blue-300">
                    {formData.conclusion.summary}
                  </p>
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    评测标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewEditorTestPage;
