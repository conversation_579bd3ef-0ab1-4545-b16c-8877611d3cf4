/**
 * 课程编辑器测试页面
 * 
 * 用于测试 Course 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import courseConfig from '@/content-schema/Course';
import { 
  GraduationCap, 
  Edit, 
  Eye, 
  Clock, 
  Users, 
  Star,
  CheckCircle,
  AlertCircle,
  BookOpen,
  Calendar,
  Tag,
  Hash,
  Award,
  DollarSign,
  PlayCircle,
  Target,
  User
} from 'lucide-react';

// 课程数据类型定义
interface Prerequisite {
  title: string;
  description?: string;
  required: boolean;
  level: 'basic' | 'intermediate' | 'advanced';
}

interface Lesson {
  lessonTitle: string;
  duration: number;
  type: 'video' | 'text' | 'quiz' | 'assignment' | 'live';
  isFree: boolean;
}

interface CurriculumModule {
  moduleTitle: string;
  moduleDescription?: string;
  duration: number;
  lessons: Lesson[];
}

interface Instructor {
  name: string;
  title: string;
  bio?: string;
  avatar?: string;
  experience?: number;
  specialties: string[];
  social?: {
    website?: string;
    linkedin?: string;
    github?: string;
  };
}

interface Pricing {
  currency: string;
  originalPrice: number;
  currentPrice: number;
  isFree: boolean;
  hasDiscount: boolean;
  discountEndDate?: string;
}

interface Duration {
  totalMinutes: number;
  totalLessons: number;
  estimatedWeeks: number;
}

interface Feature {
  feature: string;
  description?: string;
  icon: string;
}

interface Rating {
  average: number;
  count: number;
}

interface Certificate {
  available: boolean;
  certificateName?: string;
  completionRequirement: number;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface CourseData {
  title: string;
  slug: string;
  subtitle?: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  category: string;
  tags: string[];
  learningObjectives: string[];
  prerequisites: Prerequisite[];
  curriculum: CurriculumModule[];
  instructors: Instructor[];
  pricing: Pricing;
  duration: Duration;
  features: Feature[];
  status: 'draft' | 'published' | 'archived' | 'coming-soon';
  publishDate: string;
  lastUpdated: string;
  enrollmentCount: number;
  rating: Rating;
  certificate: Certificate;
  seo: SEO;
}

// 默认课程数据
const defaultCourseData: CourseData = {
  title: "React 全栈开发实战课程",
  slug: "react-fullstack-development",
  subtitle: "从零基础到项目实战，掌握现代Web开发技术栈",
  description: "本课程将带你从React基础知识开始，逐步深入学习现代Web开发的核心技术。通过理论学习和实战项目，你将掌握React、Node.js、数据库等全栈开发技能，最终能够独立开发完整的Web应用程序。",
  level: "intermediate",
  category: "Web开发",
  tags: ["React", "JavaScript", "全栈开发", "前端"],
  learningObjectives: [
    "掌握React组件开发和状态管理",
    "学会使用Node.js构建后端API",
    "理解前后端数据交互和数据库操作",
    "能够独立开发完整的全栈Web应用"
  ],
  prerequisites: [
    {
      title: "HTML/CSS基础",
      description: "了解基本的HTML标签和CSS样式",
      required: true,
      level: "basic"
    },
    {
      title: "JavaScript基础",
      description: "掌握JavaScript基本语法和ES6特性",
      required: true,
      level: "intermediate"
    }
  ],
  curriculum: [
    {
      moduleTitle: "React基础入门",
      moduleDescription: "学习React的核心概念和基础用法",
      duration: 180,
      lessons: [
        {
          lessonTitle: "React简介和环境搭建",
          duration: 30,
          type: "video",
          isFree: true
        },
        {
          lessonTitle: "组件和JSX语法",
          duration: 45,
          type: "video",
          isFree: false
        }
      ]
    }
  ],
  instructors: [
    {
      name: "张三",
      title: "高级前端工程师",
      bio: "拥有8年Web开发经验，专注于React生态系统和现代前端技术。曾在多家知名互联网公司担任技术负责人。",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      experience: 8,
      specialties: ["React", "JavaScript", "Node.js", "TypeScript"],
      social: {
        website: "https://example.com",
        linkedin: "https://linkedin.com/in/zhangsan",
        github: "https://github.com/zhangsan"
      }
    }
  ],
  pricing: {
    currency: "CNY",
    originalPrice: 299,
    currentPrice: 199,
    isFree: false,
    hasDiscount: true
  },
  duration: {
    totalMinutes: 1200,
    totalLessons: 24,
    estimatedWeeks: 8
  },
  features: [
    {
      feature: "项目实战",
      description: "通过真实项目案例学习，理论与实践相结合",
      icon: "Code"
    },
    {
      feature: "终身学习",
      description: "一次购买，终身可以重复学习和复习",
      icon: "Infinity"
    },
    {
      feature: "社群支持",
      description: "加入学习社群，与同学和讲师互动交流",
      icon: "Users"
    }
  ],
  status: "draft",
  publishDate: new Date().toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  enrollmentCount: 0,
  rating: {
    average: 0,
    count: 0
  },
  certificate: {
    available: true,
    certificateName: "React全栈开发认证证书",
    completionRequirement: 80
  },
  seo: {
    metaTitle: "React全栈开发实战课程 - 从零基础到项目实战",
    metaDescription: "专业的React全栈开发课程，包含前端React、后端Node.js、数据库等完整技术栈。通过实战项目学习，快速掌握现代Web开发技能。",
    keywords: ["React课程", "全栈开发", "JavaScript培训", "Web开发教程"]
  }
};

// 难度级别配置
const levelConfig = {
  beginner: { 
    label: '初级', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: '🟢'
  },
  intermediate: { 
    label: '中级', 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    icon: '🟡'
  },
  advanced: { 
    label: '高级', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: '🟠'
  },
  expert: { 
    label: '专家级', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: '🔴'
  }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  'coming-soon': { label: '即将推出', color: 'text-blue-700 bg-blue-50 border-blue-200' }
} as const;

const CourseEditorTestPage = () => {
  const [formData, setFormData] = useState<CourseData>(defaultCourseData);

  const handleSave = (data: CourseData) => {
    console.log('Saved course data:', data);
    setFormData(data);
  };

  const totalDuration = Math.floor(formData.duration.totalMinutes / 60);
  const hasDiscount = formData.pricing.hasDiscount && formData.pricing.currentPrice < formData.pricing.originalPrice;
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <GraduationCap className="w-8 h-8 text-blue-600" />
            Course Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Course内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              课程编辑器
            </h2>
            <BlockEditorShadcn
              config={courseConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              课程预览
            </h2>
            
            <div className="space-y-6">
              {/* 课程头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    {formData.subtitle && (
                      <p className="text-lg text-gray-600 dark:text-gray-300 mb-3">
                        {formData.subtitle}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${levelConfig[formData.level].color}`}>
                      <span>{levelConfig[formData.level].icon}</span>
                      {levelConfig[formData.level].label}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusConfig[formData.status].color}`}>
                      {statusConfig[formData.status].label}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {formData.description}
                </p>

                {/* 课程统计信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{totalDuration}h</div>
                    <div className="text-sm text-gray-500">总时长</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{formData.duration.totalLessons}</div>
                    <div className="text-sm text-gray-500">课时数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{formData.enrollmentCount}</div>
                    <div className="text-sm text-gray-500">学员数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{formData.duration.estimatedWeeks}</div>
                    <div className="text-sm text-gray-500">预计周数</div>
                  </div>
                </div>

                {/* 价格信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <DollarSign className="w-5 h-5 text-blue-600" />
                        <span className="font-medium text-blue-900 dark:text-blue-300">
                          {formData.pricing.isFree ? '免费课程' : '课程价格'}
                        </span>
                      </div>
                      {!formData.pricing.isFree && (
                        <div className="flex items-center gap-2">
                          {hasDiscount && (
                            <span className="text-lg line-through text-gray-500">
                              ¥{formData.pricing.originalPrice}
                            </span>
                          )}
                          <span className="text-2xl font-bold text-blue-600">
                            ¥{formData.pricing.currentPrice}
                          </span>
                          {hasDiscount && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                              限时优惠
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      {formData.pricing.isFree ? '免费学习' : '立即报名'}
                    </button>
                  </div>
                </div>
              </div>

              {/* 学习目标 */}
              {formData.learningObjectives.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    学习目标
                  </h4>
                  <ul className="space-y-2">
                    {formData.learningObjectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        {objective}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 前置条件 */}
              {formData.prerequisites.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    前置条件
                  </h4>
                  <div className="space-y-2">
                    {formData.prerequisites.map((prereq, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className={`w-2 h-2 rounded-full mt-2 ${prereq.required ? 'bg-red-500' : 'bg-blue-500'}`} />
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {prereq.title}
                            {prereq.required && (
                              <span className="text-red-500 text-sm ml-1">*</span>
                            )}
                          </h5>
                          {prereq.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {prereq.description}
                            </p>
                          )}
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            要求水平: {prereq.level === 'basic' ? '基础' : prereq.level === 'intermediate' ? '中等' : '高级'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 课程大纲 */}
              {formData.curriculum.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    课程大纲
                  </h4>
                  <div className="space-y-4">
                    {formData.curriculum.map((module, moduleIndex) => (
                      <div key={moduleIndex} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            模块 {moduleIndex + 1}: {module.moduleTitle}
                          </h5>
                          <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {Math.floor(module.duration / 60)}h {module.duration % 60}m
                          </span>
                        </div>

                        {module.moduleDescription && (
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                            {module.moduleDescription}
                          </p>
                        )}

                        {module.lessons.length > 0 && (
                          <div className="space-y-2">
                            {module.lessons.map((lesson, lessonIndex) => (
                              <div key={lessonIndex} className="flex items-center justify-between py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded">
                                <div className="flex items-center gap-2">
                                  <PlayCircle className="w-4 h-4 text-blue-600" />
                                  <span className="text-sm text-gray-900 dark:text-white">
                                    {lesson.lessonTitle}
                                  </span>
                                  {lesson.isFree && (
                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                                      免费
                                    </span>
                                  )}
                                </div>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {lesson.duration}分钟
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 讲师信息 */}
              {formData.instructors.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <User className="w-5 h-5" />
                    讲师团队
                  </h4>
                  <div className="space-y-4">
                    {formData.instructors.map((instructor, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {instructor.avatar && (
                          <img
                            src={instructor.avatar}
                            alt={instructor.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {instructor.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {instructor.title}
                            {instructor.experience && (
                              <span className="ml-2 text-gray-500">
                                • {instructor.experience}年经验
                              </span>
                            )}
                          </p>
                          {instructor.bio && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {instructor.bio}
                            </p>
                          )}
                          {instructor.specialties.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {instructor.specialties.map((specialty, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                                >
                                  {specialty}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 课程特色 */}
              {formData.features.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    课程特色
                  </h4>
                  <div className="grid grid-cols-1 gap-3">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {feature.feature}
                          </h5>
                          {feature.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {feature.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 评分和证书 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 评分信息 */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-2">课程评分</h5>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center">
                      {renderStars(formData.rating.average)}
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.rating.average.toFixed(1)} ({formData.rating.count} 评价)
                    </span>
                  </div>
                </div>

                {/* 证书信息 */}
                {formData.certificate.available && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">完成证书</h5>
                    <div className="flex items-center gap-2">
                      <Award className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-300">
                        {formData.certificate.certificateName}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      完成度达到 {formData.certificate.completionRequirement}% 即可获得
                    </p>
                  </div>
                )}
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    课程标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseEditorTestPage;
