/**
 * 民宿编辑器测试页面
 * 
 * 用于测试 VacationRental 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import vacationRentalConfig from '@/content-schema/VacationRental';
import { 
  Home, 
  Edit, 
  Eye, 
  Star, 
  Clock, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Lightbulb,
  ShoppingCart,
  Timer,
  Thermometer,
  Play,
  Monitor,
  Tablet,
  Laptop,
  Camera,
  Mic,
  MapPin,
  Mail,
  Phone,
  ExternalLink,
  Github,
  Linkedin,
  Twitter,
  MessageCircle,
  BookOpen,
  Youtube,
  Instagram,
  Trophy,
  GraduationCap,
  Briefcase,
  Code,
  Heart,
  Languages,
  Bed,
  Bath,
  Car,
  Wifi,
  Coffee,
  Tv,
  AirVent,
  Shield,
  PawPrint,
  Cigarette,
  PartyPopper,
  Baby,
  Volume2,
  VolumeX
} from 'lucide-react';

// 民宿数据类型定义
interface Location {
  address: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  latitude: number;
  longitude: number;
  neighborhood: string;
}

interface Accommodation {
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  beds: number;
  area: number;
  checkIn: string;
  checkOut: string;
  minStay: number;
}

interface Pricing {
  basePrice: number;
  currency: string;
  weekendSurcharge: number;
  cleaningFee: number;
  securityDeposit: number;
  extraGuestFee: number;
  seasonalPricing: Array<{
    season: string;
    startDate: string;
    endDate: string;
    priceMultiplier: number;
  }>;
}

interface Amenity {
  name: string;
  category: string;
  description: string;
  available: boolean;
  extraCharge: boolean;
}

interface HouseRules {
  smokingAllowed: boolean;
  petsAllowed: boolean;
  partiesAllowed: boolean;
  childrenAllowed: boolean;
  quietHours: {
    start: string;
    end: string;
  };
  additionalRules: string[];
}

interface Host {
  name: string;
  avatar: string;
  bio: string;
  responseTime: string;
  languages: string[];
  joinDate: string;
  verified: boolean;
}

interface RentalImage {
  url: string;
  caption: string;
  alt: string;
  type: string;
  isPrimary: boolean;
}

interface Availability {
  instantBook: boolean;
  advanceNotice: number;
  preparationTime: number;
  maxStayLength: number;
  blockedDates: Array<{
    startDate: string;
    endDate: string;
    reason: string;
  }>;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface VacationRentalData {
  title: string;
  slug: string;
  description: string;
  propertyType: string;
  location: Location;
  accommodation: Accommodation;
  pricing: Pricing;
  amenities: Amenity[];
  houseRules: HouseRules;
  host: Host;
  images: RentalImage[];
  availability: Availability;
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认民宿数据
const defaultVacationRentalData: VacationRentalData = {
  title: "山景别墅 - 温馨舒适的度假民宿体验",
  slug: "mountain-view-villa-vacation-rental",
  description: "坐落在风景如画的山脚下，这间精心装修的别墅民宿为您提供宁静舒适的度假体验。拥有宽敞的客厅、现代化厨房和私人花园，是家庭聚会和朋友聚餐的理想选择。",
  propertyType: "villa",
  location: {
    address: "北京市怀柔区雁栖湖风景区",
    city: "北京",
    state: "北京市",
    country: "中国",
    zipCode: "101400",
    latitude: 40.4319,
    longitude: 116.6312,
    neighborhood: "雁栖湖风景区"
  },
  accommodation: {
    maxGuests: 6,
    bedrooms: 3,
    bathrooms: 2,
    beds: 4,
    area: 150,
    checkIn: "15:00",
    checkOut: "11:00",
    minStay: 1
  },
  pricing: {
    basePrice: 580,
    currency: "CNY",
    weekendSurcharge: 20,
    cleaningFee: 100,
    securityDeposit: 500,
    extraGuestFee: 50,
    seasonalPricing: [
      {
        season: "夏季旺季",
        startDate: "2024-06-01",
        endDate: "2024-08-31",
        priceMultiplier: 1.3
      }
    ]
  },
  amenities: [
    {
      name: "免费WiFi",
      category: "basic",
      description: "全屋覆盖高速无线网络",
      available: true,
      extraCharge: false
    },
    {
      name: "空调",
      category: "basic",
      description: "每个房间都配备独立空调",
      available: true,
      extraCharge: false
    },
    {
      name: "厨房",
      category: "kitchen",
      description: "设备齐全的现代化厨房",
      available: true,
      extraCharge: false
    },
    {
      name: "私人花园",
      category: "outdoor",
      description: "带有户外座椅的私人花园",
      available: true,
      extraCharge: false
    },
    {
      name: "停车位",
      category: "basic",
      description: "免费私人停车位",
      available: true,
      extraCharge: false
    }
  ],
  houseRules: {
    smokingAllowed: false,
    petsAllowed: false,
    partiesAllowed: false,
    childrenAllowed: true,
    quietHours: {
      start: "22:00",
      end: "08:00"
    },
    additionalRules: [
      "请保持房屋整洁",
      "损坏物品需要赔偿",
      "退房时请关闭所有电器"
    ]
  },
  host: {
    name: "李女士",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    bio: "热情好客的本地房东，熟悉周边环境，乐于为客人提供旅行建议和帮助。",
    responseTime: "within-few-hours",
    languages: ["中文", "英语"],
    joinDate: "2020-01-01",
    verified: true
  },
  images: [
    {
      url: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop",
      caption: "民宿外观 - 山景别墅全貌",
      alt: "山景别墅外观",
      type: "exterior",
      isPrimary: true
    },
    {
      url: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
      caption: "宽敞明亮的客厅",
      alt: "客厅内景",
      type: "living-room",
      isPrimary: false
    }
  ],
  availability: {
    instantBook: true,
    advanceNotice: 1,
    preparationTime: 2,
    maxStayLength: 30,
    blockedDates: []
  },
  tags: ["山景", "别墅", "家庭友好", "宠物友好", "度假"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "山景别墅民宿 - 温馨舒适的度假体验 | 民宿预订",
    metaDescription: "坐落在风景如画的山脚下的精美别墅民宿，提供宁静舒适的度假体验。设施齐全，环境优美，是家庭聚会和朋友聚餐的理想选择。",
    keywords: ["山景民宿", "别墅度假", "家庭民宿", "北京民宿"]
  }
};

// 房屋类型配置
const propertyTypeConfig = {
  apartment: { label: '公寓', icon: '🏠', color: 'text-blue-600' },
  house: { label: '独栋房屋', icon: '🏡', color: 'text-green-600' },
  villa: { label: '别墅', icon: '🏘️', color: 'text-purple-600' },
  cabin: { label: '小木屋', icon: '🏕️', color: 'text-brown-600' },
  cottage: { label: '乡村小屋', icon: '🏚️', color: 'text-orange-600' },
  loft: { label: '阁楼', icon: '🏢', color: 'text-gray-600' },
  townhouse: { label: '联排别墅', icon: '🏘️', color: 'text-indigo-600' },
  other: { label: '其他', icon: '📦', color: 'text-gray-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const VacationRentalEditorTestPage = () => {
  const [formData, setFormData] = useState<VacationRentalData>(defaultVacationRentalData);

  const handleSave = (data: VacationRentalData) => {
    console.log('Saved vacation rental data:', data);
    setFormData(data);
  };

  const propertyTypeInfo = propertyTypeConfig[formData.propertyType as keyof typeof propertyTypeConfig];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getAmenityIcon = (category: string) => {
    const icons = {
      basic: Wifi,
      kitchen: Coffee,
      bathroom: Bath,
      bedroom: Bed,
      entertainment: Tv,
      outdoor: Heart,
      safety: Shield,
      accessibility: Heart,
      other: Heart
    };
    return icons[category as keyof typeof icons] || Heart;
  };

  const getResponseTimeLabel = (responseTime: string) => {
    const labels = {
      'within-hour': '⚡ 1小时内',
      'within-few-hours': '🕐 几小时内',
      'within-day': '📅 1天内',
      'few-days': '📆 几天内'
    };
    return labels[responseTime as keyof typeof labels] || responseTime;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Home className="w-8 h-8 text-blue-600" />
            Vacation Rental Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试VacationRental内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              民宿编辑器
            </h2>
            <BlockEditorShadcn
              config={vacationRentalConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              民宿预览
            </h2>
            
            <div className="space-y-6">
              {/* 民宿头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${propertyTypeInfo.color}`}>
                        {propertyTypeInfo.icon} {propertyTypeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formData.accommodation.area}㎡
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <div className="flex items-center gap-1 text-gray-600 dark:text-gray-300 mb-3">
                      <MapPin className="w-4 h-4" />
                      {formData.location.neighborhood}, {formData.location.city}
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 住宿基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                      <Users className="w-4 h-4" />
                      {formData.accommodation.maxGuests}
                    </div>
                    <div className="text-xs text-gray-500">最多入住</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600 flex items-center justify-center gap-1">
                      <Bed className="w-4 h-4" />
                      {formData.accommodation.bedrooms}
                    </div>
                    <div className="text-xs text-gray-500">卧室</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600 flex items-center justify-center gap-1">
                      <Bath className="w-4 h-4" />
                      {formData.accommodation.bathrooms}
                    </div>
                    <div className="text-xs text-gray-500">浴室</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 flex items-center justify-center gap-1">
                      <DollarSign className="w-4 h-4" />
                      ¥{formData.pricing.basePrice}
                    </div>
                    <div className="text-xs text-gray-500">每晚</div>
                  </div>
                </div>

                {/* 主要图片 */}
                {formData.images.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.images[0].url}
                      alt={formData.images[0].alt}
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.images[0].caption}
                    </p>
                  </div>
                )}
              </div>

              {/* 设施服务 */}
              {formData.amenities.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Wifi className="w-5 h-5" />
                    设施服务
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.amenities.map((amenity, index) => {
                      const IconComponent = getAmenityIcon(amenity.category);
                      return (
                        <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                          <IconComponent className="w-5 h-5 text-blue-600" />
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-900 dark:text-white">
                                {amenity.name}
                              </span>
                              {amenity.extraCharge && (
                                <span className="text-xs px-2 py-1 bg-orange-100 text-orange-700 rounded">
                                  额外收费
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {amenity.description}
                            </p>
                          </div>
                          {amenity.available && (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 价格信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  价格信息
                </h4>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        ¥{formData.pricing.basePrice}
                      </div>
                      <div className="text-sm text-blue-700 dark:text-blue-300">基础价格/晚</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">
                        +{formData.pricing.weekendSurcharge}%
                      </div>
                      <div className="text-sm text-green-700 dark:text-green-300">周末加价</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-600">
                        ¥{formData.pricing.cleaningFee}
                      </div>
                      <div className="text-sm text-orange-700 dark:text-orange-300">清洁费</div>
                    </div>
                  </div>
                  <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <div>押金: ¥{formData.pricing.securityDeposit}</div>
                    <div>额外客人费用: ¥{formData.pricing.extraGuestFee}/人/晚</div>
                    <div>最少入住: {formData.accommodation.minStay}晚</div>
                  </div>
                </div>
              </div>

              {/* 房东信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <User className="w-5 h-5" />
                  房东信息
                </h4>
                <div className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <img
                    src={formData.host.avatar}
                    alt={formData.host.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {formData.host.name}
                      </h5>
                      {formData.host.verified && (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {formData.host.bio}
                    </p>
                    <div className="flex flex-wrap gap-4 text-xs text-gray-500">
                      <div>回复时间: {getResponseTimeLabel(formData.host.responseTime)}</div>
                      <div>加入时间: {formatDate(formData.host.joinDate)}</div>
                      <div>语言: {formData.host.languages.join(', ')}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 民宿标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    民宿标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VacationRentalEditorTestPage;
