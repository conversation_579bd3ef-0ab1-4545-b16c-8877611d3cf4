/**
 * 作品集编辑器测试页面
 * 
 * 用于测试 Portfolio 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import portfolioConfig from '@/content-schema/Portfolio';
import { 
  FolderOpen, 
  Edit, 
  Eye, 
  Calendar, 
  Code, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  ExternalLink,
  Play,
  Download,
  Star,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Trophy,
  Newspaper
} from 'lucide-react';

// 作品集数据类型定义
interface Technology {
  name: string;
  category: string;
  proficiency: string;
  version?: string;
}

interface Feature {
  title: string;
  description: string;
  priority: string;
  status: string;
}

interface Links {
  liveDemo?: string;
  repository?: string;
  documentation?: string;
  presentation?: string;
  video?: string;
}

interface Screenshot {
  url: string;
  caption: string;
  alt: string;
}

interface VideoMedia {
  url: string;
  title: string;
  duration?: number;
  thumbnail?: string;
}

interface Media {
  screenshots: Screenshot[];
  videos: VideoMedia[];
}

interface Metric {
  name: string;
  value: string;
  unit?: string;
  description?: string;
}

interface Award {
  title: string;
  organization: string;
  date: string;
  description?: string;
}

interface Recognition {
  title: string;
  media: string;
  url: string;
  date: string;
}

interface Achievements {
  metrics: Metric[];
  awards: Award[];
  recognition: Recognition[];
}

interface TeamMember {
  name: string;
  role: string;
  avatar?: string;
  bio?: string;
  contribution?: string;
  social?: {
    linkedin?: string;
    github?: string;
    website?: string;
  };
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface PortfolioData {
  title: string;
  slug: string;
  description: string;
  category: string;
  type: string;
  status: string;
  startDate: string;
  endDate: string;
  duration: string;
  technologies: Technology[];
  features: Feature[];
  links: Links;
  media: Media;
  achievements: Achievements;
  team: TeamMember[];
  tags: string[];
  visibility: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认作品集数据
const defaultPortfolioData: PortfolioData = {
  title: "智能客服系统 - AI驱动的多渠道客户服务平台",
  slug: "ai-customer-service-platform",
  description: "这是一个基于人工智能技术的智能客服系统，支持多渠道接入（网页、微信、APP），具备自然语言理解、智能问答、情感分析等功能。系统采用微服务架构，支持高并发访问，为企业提供7x24小时的智能客户服务。",
  category: "ai-ml",
  type: "commercial",
  status: "completed",
  startDate: "2024-01-15",
  endDate: "2024-06-30",
  duration: "6个月",
  technologies: [
    {
      name: "React",
      category: "frontend",
      proficiency: "advanced",
      version: "18.2"
    },
    {
      name: "Node.js",
      category: "backend",
      proficiency: "advanced",
      version: "20.x"
    },
    {
      name: "Python",
      category: "ai-ml",
      proficiency: "expert",
      version: "3.11"
    },
    {
      name: "TensorFlow",
      category: "ai-ml",
      proficiency: "advanced",
      version: "2.13"
    },
    {
      name: "MongoDB",
      category: "database",
      proficiency: "intermediate",
      version: "7.0"
    }
  ],
  features: [
    {
      title: "智能问答系统",
      description: "基于NLP技术的智能问答，支持多轮对话和上下文理解",
      priority: "critical",
      status: "completed"
    },
    {
      title: "多渠道接入",
      description: "支持网页、微信、APP等多种渠道的统一接入",
      priority: "high",
      status: "completed"
    },
    {
      title: "情感分析",
      description: "实时分析用户情感，提供个性化服务",
      priority: "medium",
      status: "completed"
    },
    {
      title: "数据分析仪表板",
      description: "提供客服数据的可视化分析和报表",
      priority: "medium",
      status: "completed"
    }
  ],
  links: {
    liveDemo: "https://demo.ai-customer-service.com",
    repository: "https://github.com/username/ai-customer-service",
    documentation: "https://docs.ai-customer-service.com"
  },
  media: {
    screenshots: [
      {
        url: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop",
        caption: "智能客服系统主界面",
        alt: "AI客服系统主界面截图"
      },
      {
        url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop",
        caption: "数据分析仪表板",
        alt: "客服数据分析仪表板截图"
      }
    ],
    videos: []
  },
  achievements: {
    metrics: [
      {
        name: "响应时间",
        value: "< 500ms",
        unit: "毫秒",
        description: "平均API响应时间"
      },
      {
        name: "准确率",
        value: "95%",
        unit: "百分比",
        description: "智能问答准确率"
      },
      {
        name: "用户满意度",
        value: "4.8",
        unit: "分",
        description: "用户评分（5分制）"
      }
    ],
    awards: [],
    recognition: []
  },
  team: [
    {
      name: "张三",
      role: "项目负责人 & 全栈工程师",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      bio: "资深全栈工程师，专注于AI技术和Web开发",
      contribution: "负责整体架构设计、AI模型训练和前端开发",
      social: {
        github: "https://github.com/zhangsan",
        linkedin: "https://linkedin.com/in/zhangsan",
        website: "https://zhangsan.dev"
      }
    }
  ],
  tags: ["AI", "客服系统", "React", "Node.js", "机器学习"],
  visibility: "public",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "智能客服系统 - AI驱动的多渠道客户服务平台 | 项目作品集",
    metaDescription: "基于人工智能技术的智能客服系统，支持多渠道接入，具备自然语言理解、智能问答、情感分析等功能。查看项目详情和技术实现。",
    keywords: ["AI客服", "智能客服系统", "React项目", "机器学习应用"]
  }
};

// 项目分类配置
const categoryConfig = {
  'web-development': { label: 'Web开发', icon: '🌐', color: 'text-blue-600' },
  'mobile-app': { label: '移动应用', icon: '📱', color: 'text-green-600' },
  'desktop-app': { label: '桌面应用', icon: '💻', color: 'text-purple-600' },
  'ai-ml': { label: 'AI/机器学习', icon: '🤖', color: 'text-red-600' },
  'data-science': { label: '数据科学', icon: '📊', color: 'text-orange-600' },
  'design': { label: '设计', icon: '🎨', color: 'text-pink-600' },
  'game': { label: '游戏开发', icon: '🎮', color: 'text-indigo-600' },
  'blockchain': { label: '区块链', icon: '⛓️', color: 'text-yellow-600' },
  'iot': { label: '物联网', icon: '🌐', color: 'text-teal-600' },
  'other': { label: '其他', icon: '🔧', color: 'text-gray-600' }
} as const;

// 项目类型配置
const typeConfig = {
  personal: { label: '个人项目', icon: '👤', color: 'text-blue-600' },
  commercial: { label: '商业项目', icon: '💼', color: 'text-green-600' },
  'open-source': { label: '开源项目', icon: '🔓', color: 'text-purple-600' },
  academic: { label: '学术项目', icon: '🎓', color: 'text-orange-600' },
  freelance: { label: '自由职业', icon: '💰', color: 'text-pink-600' },
  team: { label: '团队项目', icon: '👥', color: 'text-indigo-600' }
} as const;

// 状态配置
const statusConfig = {
  planning: { label: '规划中', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  'in-progress': { label: '进行中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  completed: { label: '已完成', color: 'text-green-700 bg-green-50 border-green-200' },
  maintenance: { label: '维护中', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

// 可见性配置
const visibilityConfig = {
  public: { label: '公开', icon: Globe, color: 'text-green-600' },
  private: { label: '私有', icon: Lock, color: 'text-red-600' },
  'portfolio-only': { label: '仅作品集', icon: FolderOpen, color: 'text-blue-600' }
} as const;

const PortfolioEditorTestPage = () => {
  const [formData, setFormData] = useState<PortfolioData>(defaultPortfolioData);

  const handleSave = (data: PortfolioData) => {
    console.log('Saved portfolio data:', data);
    setFormData(data);
  };

  const categoryInfo = categoryConfig[formData.category as keyof typeof categoryConfig];
  const typeInfo = typeConfig[formData.type as keyof typeof typeConfig];
  const VisibilityIcon = visibilityConfig[formData.visibility as keyof typeof visibilityConfig].icon;
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getTechColor = (category: string) => {
    const colors = {
      frontend: 'bg-blue-100 text-blue-700 border-blue-200',
      backend: 'bg-green-100 text-green-700 border-green-200',
      database: 'bg-purple-100 text-purple-700 border-purple-200',
      'ai-ml': 'bg-red-100 text-red-700 border-red-200',
      devops: 'bg-orange-100 text-orange-700 border-orange-200',
      design: 'bg-pink-100 text-pink-700 border-pink-200',
      testing: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      other: 'bg-gray-100 text-gray-700 border-gray-200'
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'text-green-600',
      medium: 'text-yellow-600',
      high: 'text-orange-600',
      critical: 'text-red-600'
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <FolderOpen className="w-8 h-8 text-blue-600" />
            Portfolio Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Portfolio内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              作品集编辑器
            </h2>
            <BlockEditorShadcn
              config={portfolioConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              项目预览
            </h2>
            
            <div className="space-y-6">
              {/* 项目头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${categoryInfo.color}`}>
                        {categoryInfo.icon} {categoryInfo.label}
                      </span>
                      <span className={`text-sm ${typeInfo.color}`}>
                        {typeInfo.icon} {typeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formData.duration}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${visibilityConfig[formData.visibility as keyof typeof visibilityConfig].color}`}>
                      <VisibilityIcon className="w-3 h-3" />
                      {visibilityConfig[formData.visibility as keyof typeof visibilityConfig].label}
                    </span>
                  </div>
                </div>

                {/* 项目时间线 */}
                <div className="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>开始: {formatDate(formData.startDate)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>结束: {formatDate(formData.endDate)}</span>
                  </div>
                </div>

                {/* 项目链接 */}
                <div className="flex flex-wrap gap-2">
                  {formData.links.liveDemo && (
                    <a
                      href={formData.links.liveDemo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                    >
                      <ExternalLink className="w-4 h-4" />
                      在线演示
                    </a>
                  )}
                  {formData.links.repository && (
                    <a
                      href={formData.links.repository}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 px-3 py-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
                    >
                      <Code className="w-4 h-4" />
                      代码仓库
                    </a>
                  )}
                  {formData.links.documentation && (
                    <a
                      href={formData.links.documentation}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                    >
                      <Download className="w-4 h-4" />
                      项目文档
                    </a>
                  )}
                </div>
              </div>

              {/* 技术栈 */}
              {formData.technologies.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Code className="w-5 h-5" />
                    技术栈
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.technologies.map((tech, index) => (
                      <div key={index} className={`flex items-center justify-between p-3 rounded-lg border ${getTechColor(tech.category)}`}>
                        <div>
                          <span className="font-medium">
                            {tech.name}
                            {tech.version && <span className="text-xs ml-1">v{tech.version}</span>}
                          </span>
                          <div className="text-xs opacity-75">
                            {tech.category === 'frontend' ? '前端' :
                             tech.category === 'backend' ? '后端' :
                             tech.category === 'database' ? '数据库' :
                             tech.category === 'ai-ml' ? 'AI/ML' :
                             tech.category === 'devops' ? '运维' :
                             tech.category === 'design' ? '设计' :
                             tech.category === 'testing' ? '测试' : '其他'}
                          </div>
                        </div>
                        <span className="text-xs px-2 py-1 rounded bg-white/50">
                          {tech.proficiency === 'expert' ? '专家' :
                           tech.proficiency === 'advanced' ? '高级' :
                           tech.proficiency === 'intermediate' ? '中级' : '初学'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 核心功能 */}
              {formData.features.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    核心功能
                  </h4>
                  <div className="space-y-3">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {feature.title}
                          </h5>
                          <div className="flex items-center gap-2">
                            <span className={`text-xs ${getPriorityColor(feature.priority)}`}>
                              {feature.priority === 'critical' ? '🔴 关键' :
                               feature.priority === 'high' ? '🟠 高' :
                               feature.priority === 'medium' ? '🟡 中' : '🟢 低'}
                            </span>
                            <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-700">
                              {feature.status === 'completed' ? '✅ 已完成' :
                               feature.status === 'in-progress' ? '🚧 开发中' :
                               feature.status === 'testing' ? '🧪 测试中' : '📋 计划中'}
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 项目截图 */}
              {formData.media.screenshots.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Image className="w-5 h-5" />
                    项目截图
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.media.screenshots.map((screenshot, index) => (
                      <div key={index} className="group relative">
                        <img
                          src={screenshot.url}
                          alt={screenshot.alt}
                          className="w-full h-48 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg" />
                        <div className="mt-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {screenshot.caption}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 项目成果 */}
              {formData.achievements.metrics.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    项目成果
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {formData.achievements.metrics.map((metric, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600 mb-1">
                          {metric.value}
                        </div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                          {metric.name}
                        </div>
                        {metric.description && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {metric.description}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 获得奖项 */}
              {formData.achievements.awards.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Trophy className="w-5 h-5" />
                    获得奖项
                  </h4>
                  <div className="space-y-3">
                    {formData.achievements.awards.map((award, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <Trophy className="w-5 h-5 text-yellow-600 mt-0.5" />
                        <div className="flex-1">
                          <h5 className="font-medium text-yellow-900 dark:text-yellow-300">
                            {award.title}
                          </h5>
                          <div className="text-sm text-yellow-700 dark:text-yellow-400 mb-1">
                            {award.organization} • {formatDate(award.date)}
                          </div>
                          {award.description && (
                            <p className="text-sm text-yellow-800 dark:text-yellow-300">
                              {award.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 媒体报道 */}
              {formData.achievements.recognition.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Newspaper className="w-5 h-5" />
                    媒体报道
                  </h4>
                  <div className="space-y-3">
                    {formData.achievements.recognition.map((recognition, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {recognition.title}
                          </h5>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {recognition.media} • {formatDate(recognition.date)}
                          </div>
                        </div>
                        <a
                          href={recognition.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 团队成员 */}
              {formData.team.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    团队成员
                  </h4>
                  <div className="space-y-4">
                    {formData.team.map((member, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {member.avatar && (
                          <img
                            src={member.avatar}
                            alt={member.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {member.name}
                            </h5>
                            <span className="text-sm text-blue-600 dark:text-blue-400">
                              {member.role}
                            </span>
                          </div>
                          {member.bio && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {member.bio}
                            </p>
                          )}
                          {member.contribution && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              <span className="font-medium">主要贡献:</span> {member.contribution}
                            </p>
                          )}
                          {member.social && (
                            <div className="flex items-center gap-3 text-sm">
                              {member.social.website && (
                                <a href={member.social.website} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  网站
                                </a>
                              )}
                              {member.social.github && (
                                <a href={member.social.github} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  GitHub
                                </a>
                              )}
                              {member.social.linkedin && (
                                <a href={member.social.linkedin} className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                  LinkedIn
                                </a>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 项目标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    项目标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioEditorTestPage;
