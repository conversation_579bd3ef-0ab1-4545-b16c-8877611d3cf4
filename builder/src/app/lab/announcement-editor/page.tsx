/**
 * 公告编辑器测试页面
 * 
 * 用于测试 Announcement 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import announcementConfig from '@/content-schema/Announcement';
import { 
  Megaphone, 
  Edit, 
  Eye, 
  Calendar, 
  Users, 
  AlertTriangle,
  Clock,
  Tag,
  ExternalLink,
  Bell,
  Pin,
  CheckCircle,
  AlertCircle,
  Hash
} from 'lucide-react';

// 公告数据类型定义
interface ActionButton {
  enabled: boolean;
  text: string;
  url: string;
  style: 'primary' | 'secondary' | 'outline';
}

interface Author {
  name: string;
  email: string;
  role: string;
}

interface NotificationSettings {
  sendEmail: boolean;
  sendPush: boolean;
  sendSMS: boolean;
}

interface RelatedLink {
  title: string;
  url: string;
  description?: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
}

interface AnnouncementData {
  title: string;
  content: string;
  priority: 'low' | 'normal' | 'high' | 'urgent' | 'critical';
  type: 'general' | 'maintenance' | 'feature' | 'security' | 'policy' | 'event';
  targetAudience: string[];
  publishDate: string;
  expiryDate?: string;
  isSticky: boolean;
  showBanner: boolean;
  bannerColor: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  actionButton: ActionButton;
  tags: string[];
  author: Author;
  status: 'draft' | 'scheduled' | 'published' | 'archived';
  readTime: number;
  isUrgent: boolean;
  notificationSettings: NotificationSettings;
  relatedLinks: RelatedLink[];
  seo: SEO;
}

// 默认公告数据
const defaultAnnouncementData: AnnouncementData = {
  title: "Important System Maintenance Scheduled",
  content: "# System Maintenance Notice\n\nWe will be performing scheduled maintenance on our systems to improve performance and security.\n\n## Maintenance Details\n\n- **Date**: Sunday, March 15, 2024\n- **Time**: 2:00 AM - 6:00 AM EST\n- **Expected Downtime**: 4 hours\n\n## What to Expect\n\nDuring this maintenance window:\n- All services will be temporarily unavailable\n- No data will be lost\n- All features will be restored after maintenance\n\nWe apologize for any inconvenience and appreciate your patience.",
  priority: "normal",
  type: "maintenance",
  targetAudience: ["all-users"],
  publishDate: new Date().toISOString(),
  isSticky: false,
  showBanner: false,
  bannerColor: "blue",
  actionButton: {
    enabled: false,
    text: "Learn More",
    url: "https://example.com",
    style: "primary"
  },
  tags: ["maintenance", "system", "scheduled"],
  author: {
    name: "System Administrator",
    email: "<EMAIL>",
    role: "Administrator"
  },
  status: "draft",
  readTime: 2,
  isUrgent: false,
  notificationSettings: {
    sendEmail: false,
    sendPush: false,
    sendSMS: false
  },
  relatedLinks: [],
  seo: {
    metaTitle: "Important System Maintenance Scheduled | Company Updates",
    metaDescription: "We will be performing scheduled maintenance on our systems. Learn about the maintenance schedule and what to expect during the downtime."
  }
};

// 优先级配置
const priorityConfig = {
  low: { 
    label: 'Low Priority', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: CheckCircle
  },
  normal: { 
    label: 'Normal Priority', 
    color: 'text-blue-700 bg-blue-50 border-blue-200',
    icon: CheckCircle
  },
  high: { 
    label: 'High Priority', 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    icon: AlertCircle
  },
  urgent: { 
    label: 'Urgent', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: AlertTriangle
  },
  critical: { 
    label: 'Critical', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: AlertTriangle
  }
} as const;

// 类型配置
const typeConfig = {
  general: { label: 'General', icon: '📢' },
  maintenance: { label: 'Maintenance', icon: '🔧' },
  feature: { label: 'Feature Update', icon: '✨' },
  security: { label: 'Security', icon: '🔒' },
  policy: { label: 'Policy', icon: '📋' },
  event: { label: 'Event', icon: '🎉' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: 'Draft', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  scheduled: { label: 'Scheduled', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: 'Published', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: 'Archived', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const AnnouncementEditorTestPage = () => {
  const [formData, setFormData] = useState<AnnouncementData>(defaultAnnouncementData);

  const handleSave = (data: AnnouncementData) => {
    console.log('Saved announcement data:', data);
    setFormData(data);
  };

  const PriorityIcon = priorityConfig[formData.priority].icon;
  const publishDate = new Date(formData.publishDate);
  const expiryDate = formData.expiryDate ? new Date(formData.expiryDate) : null;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Megaphone className="w-8 h-8 text-blue-600" />
            Announcement Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the Announcement content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Announcement Editor
            </h2>
            <BlockEditorShadcn
              config={announcementConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Announcement Preview
            </h2>
            
            <div className="space-y-6">
              {/* 公告头部 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                    {formData.isSticky && <Pin className="w-5 h-5 text-blue-600" />}
                    {formData.isUrgent && <AlertTriangle className="w-5 h-5 text-red-600" />}
                    {formData.title}
                  </h3>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${priorityConfig[formData.priority].color}`}>
                      <PriorityIcon className="w-3 h-3" />
                      {priorityConfig[formData.priority].label}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusConfig[formData.status].color}`}>
                      {statusConfig[formData.status].label}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                  <span className="flex items-center gap-1">
                    <span>{typeConfig[formData.type].icon}</span>
                    {typeConfig[formData.type].label}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {publishDate.toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formData.readTime} min read
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {formData.targetAudience.length} audience{formData.targetAudience.length > 1 ? 's' : ''}
                  </span>
                </div>

                <div className="text-sm text-gray-600 dark:text-gray-300">
                  By {formData.author.name} ({formData.author.role})
                </div>
              </div>

              {/* Banner预览 */}
              {formData.showBanner && (
                <div className={`p-4 rounded-lg border-l-4 ${
                  formData.bannerColor === 'blue' ? 'bg-blue-50 border-blue-400 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' :
                  formData.bannerColor === 'green' ? 'bg-green-50 border-green-400 text-green-800 dark:bg-green-900/20 dark:text-green-300' :
                  formData.bannerColor === 'yellow' ? 'bg-yellow-50 border-yellow-400 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' :
                  formData.bannerColor === 'red' ? 'bg-red-50 border-red-400 text-red-800 dark:bg-red-900/20 dark:text-red-300' :
                  formData.bannerColor === 'purple' ? 'bg-purple-50 border-purple-400 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' :
                  'bg-gray-50 border-gray-400 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Megaphone className="w-5 h-5" />
                      <span className="font-medium">Banner Preview: {formData.title}</span>
                    </div>
                    {formData.actionButton.enabled && (
                      <button className={`px-3 py-1 rounded text-sm font-medium ${
                        formData.actionButton.style === 'primary' ? 'bg-blue-600 text-white' :
                        formData.actionButton.style === 'secondary' ? 'bg-gray-600 text-white' :
                        'border border-current'
                      }`}>
                        {formData.actionButton.text}
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* 公告内容 */}
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown>{formData.content}</ReactMarkdown>
              </div>

              {/* 操作按钮 */}
              {formData.actionButton.enabled && !formData.showBanner && (
                <div>
                  <a
                    href={formData.actionButton.url}
                    className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium ${
                      formData.actionButton.style === 'primary' ? 'bg-blue-600 text-white hover:bg-blue-700' :
                      formData.actionButton.style === 'secondary' ? 'bg-gray-600 text-white hover:bg-gray-700' :
                      'border border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700'
                    }`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {formData.actionButton.text}
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </a>
                </div>
              )}

              {/* 目标受众 */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  Target Audience
                </h4>
                <div className="flex flex-wrap gap-2">
                  {formData.targetAudience.map((audience, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                    >
                      {audience.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  ))}
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 通知设置 */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                  <Bell className="w-4 h-4" />
                  Notification Settings
                </h4>
                <div className="flex items-center gap-4 text-sm">
                  <span className={`flex items-center gap-1 ${formData.notificationSettings.sendEmail ? 'text-green-600' : 'text-gray-400'}`}>
                    <CheckCircle className="w-4 h-4" />
                    Email
                  </span>
                  <span className={`flex items-center gap-1 ${formData.notificationSettings.sendPush ? 'text-green-600' : 'text-gray-400'}`}>
                    <CheckCircle className="w-4 h-4" />
                    Push
                  </span>
                  <span className={`flex items-center gap-1 ${formData.notificationSettings.sendSMS ? 'text-green-600' : 'text-gray-400'}`}>
                    <CheckCircle className="w-4 h-4" />
                    SMS
                  </span>
                </div>
              </div>

              {/* 相关链接 */}
              {formData.relatedLinks.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <ExternalLink className="w-4 h-4" />
                    Related Links
                  </h4>
                  <ul className="space-y-2">
                    {formData.relatedLinks.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.url}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {link.title}
                        </a>
                        {link.description && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {link.description}
                          </p>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 过期时间 */}
              {expiryDate && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-300">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      This announcement will expire on {expiryDate.toLocaleDateString()}
                    </span>
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementEditorTestPage;
