/**
 * 教程编辑器测试页面
 * 
 * 用于测试 Tutorial 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import tutorialConfig from '@/content-schema/Tutorial';
import { 
  BookOpen, 
  Edit, 
  Eye, 
  Clock, 
  User, 
  Star,
  CheckCircle,
  AlertCircle,
  Code,
  Tag,
  Calendar,
  Hash,
  Award,
  Users,
  TrendingUp
} from 'lucide-react';

// 教程数据类型定义
interface Prerequisite {
  title: string;
  description?: string;
  required: boolean;
}

interface Step {
  title: string;
  content: string;
  code?: string;
  language: string;
  estimatedTime: number;
  tips: string[];
}

interface Tool {
  name: string;
  version?: string;
  url?: string;
  description?: string;
}

interface Author {
  name: string;
  bio?: string;
  avatar?: string;
  social?: {
    twitter?: string;
    github?: string;
    linkedin?: string;
  };
}

interface Rating {
  average: number;
  count: number;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface TutorialData {
  title: string;
  slug: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimatedTime: number;
  category: string;
  prerequisites: Prerequisite[];
  steps: Step[];
  tools: Tool[];
  tags: string[];
  author: Author;
  lastUpdated: string;
  isPublished: boolean;
  isFeatured: boolean;
  completionRate: number;
  rating: Rating;
  seo: SEO;
}

// 默认教程数据
const defaultTutorialData: TutorialData = {
  title: "Getting Started with React Hooks",
  slug: "getting-started-react-hooks",
  description: "Learn how to use React Hooks to manage state and side effects in functional components. This comprehensive tutorial covers useState, useEffect, and custom hooks with practical examples.",
  difficulty: "beginner",
  estimatedTime: 30,
  category: "React Development",
  prerequisites: [
    {
      title: "Basic JavaScript knowledge",
      description: "Understanding of ES6+ features, functions, and objects",
      required: true
    },
    {
      title: "React fundamentals",
      description: "Basic understanding of React components and JSX",
      required: true
    }
  ],
  steps: [
    {
      title: "Setting up the project",
      content: "First, let's create a new React project and set up our development environment.",
      code: "npx create-react-app my-hooks-tutorial\ncd my-hooks-tutorial\nnpm start",
      language: "bash",
      estimatedTime: 5,
      tips: ["Make sure you have Node.js installed", "Use the latest version of Create React App"]
    },
    {
      title: "Understanding useState",
      content: "The useState hook allows you to add state to functional components.",
      code: "import React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>\n        Increment\n      </button>\n    </div>\n  );\n}",
      language: "jsx",
      estimatedTime: 10,
      tips: ["useState returns an array with the current state and a setter function", "Always use the setter function to update state"]
    }
  ],
  tools: [
    {
      name: "Node.js",
      version: "18+",
      url: "https://nodejs.org/",
      description: "JavaScript runtime for running React development tools"
    },
    {
      name: "VS Code",
      version: "Latest",
      url: "https://code.visualstudio.com/",
      description: "Recommended code editor with React extensions"
    }
  ],
  tags: ["react", "hooks", "javascript", "frontend"],
  author: {
    name: "John Developer",
    bio: "Senior React Developer with 5+ years of experience building modern web applications.",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    social: {
      twitter: "",
      github: "",
      linkedin: ""
    }
  },
  lastUpdated: new Date().toISOString().split('T')[0],
  isPublished: false,
  isFeatured: false,
  completionRate: 0,
  rating: {
    average: 0,
    count: 0
  },
  seo: {
    metaTitle: "Getting Started with React Hooks - Complete Tutorial",
    metaDescription: "Learn React Hooks from scratch with this comprehensive tutorial. Master useState, useEffect, and custom hooks with practical examples and best practices.",
    keywords: ["react hooks", "useState", "useEffect", "react tutorial", "javascript"]
  }
};

// 难度配置
const difficultyConfig = {
  beginner: { 
    label: 'Beginner', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: '🟢'
  },
  intermediate: { 
    label: 'Intermediate', 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    icon: '🟡'
  },
  advanced: { 
    label: 'Advanced', 
    color: 'text-orange-700 bg-orange-50 border-orange-200',
    icon: '🟠'
  },
  expert: { 
    label: 'Expert', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: '🔴'
  }
} as const;

const TutorialEditorTestPage = () => {
  const [formData, setFormData] = useState<TutorialData>(defaultTutorialData);

  const handleSave = (data: TutorialData) => {
    console.log('Saved tutorial data:', data);
    setFormData(data);
  };

  const totalStepTime = formData.steps.reduce((total, step) => total + step.estimatedTime, 0);
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <BookOpen className="w-8 h-8 text-blue-600" />
            Tutorial Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the Tutorial content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Tutorial Editor
            </h2>
            <BlockEditorShadcn
              config={tutorialConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Tutorial Preview
            </h2>
            
            <div className="space-y-6">
              {/* 教程头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formData.title}
                    {formData.isFeatured && (
                      <Award className="w-6 h-6 text-yellow-500 inline ml-2" />
                    )}
                  </h3>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${difficultyConfig[formData.difficulty].color}`}>
                      <span>{difficultyConfig[formData.difficulty].icon}</span>
                      {difficultyConfig[formData.difficulty].label}
                    </span>
                    {formData.isPublished ? (
                      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800">
                        <CheckCircle className="w-3 h-3" />
                        Published
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        <AlertCircle className="w-3 h-3" />
                        Draft
                      </span>
                    )}
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {formData.description}
                </p>

                <div className="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formData.estimatedTime} minutes
                  </span>
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {formData.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Updated {formData.lastUpdated}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {formData.steps.length} steps
                  </span>
                </div>

                {/* 评分和完成率 */}
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {renderStars(formData.rating.average)}
                    </div>
                    <span className="text-gray-600 dark:text-gray-300">
                      {formData.rating.average.toFixed(1)} ({formData.rating.count} reviews)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-4 h-4 text-green-600" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {formData.completionRate.toFixed(1)}% completion rate
                    </span>
                  </div>
                </div>
              </div>

              {/* 作者信息 */}
              <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                {formData.author.avatar && (
                  <img
                    src={formData.author.avatar}
                    alt={formData.author.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                )}
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {formData.author.name}
                  </h4>
                  {formData.author.bio && (
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.author.bio}
                    </p>
                  )}
                </div>
              </div>

              {/* 前置条件 */}
              {formData.prerequisites.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Prerequisites
                  </h4>
                  <div className="space-y-2">
                    {formData.prerequisites.map((prereq, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className={`w-2 h-2 rounded-full mt-2 ${prereq.required ? 'bg-red-500' : 'bg-blue-500'}`} />
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {prereq.title}
                            {prereq.required && (
                              <span className="text-red-500 text-sm ml-1">*</span>
                            )}
                          </h5>
                          {prereq.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {prereq.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 所需工具 */}
              {formData.tools.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Code className="w-5 h-5" />
                    Required Tools
                  </h4>
                  <div className="grid grid-cols-1 gap-3">
                    {formData.tools.map((tool, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {tool.name}
                            {tool.version && (
                              <span className="text-sm text-gray-500 ml-2">v{tool.version}</span>
                            )}
                          </h5>
                          {tool.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {tool.description}
                            </p>
                          )}
                        </div>
                        {tool.url && (
                          <a
                            href={tool.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                          >
                            Download
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 教程步骤 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  Tutorial Steps ({totalStepTime} min total)
                </h4>
                <div className="space-y-4">
                  {formData.steps.map((step, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-medium text-gray-900 dark:text-white">
                          Step {index + 1}: {step.title}
                        </h5>
                        <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {step.estimatedTime} min
                        </span>
                      </div>

                      <div className="prose prose-sm max-w-none dark:prose-invert mb-3">
                        <ReactMarkdown>{step.content}</ReactMarkdown>
                      </div>

                      {step.code && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Code Example
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                              {step.language}
                            </span>
                          </div>
                          <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                            <code>{step.code}</code>
                          </pre>
                        </div>
                      )}

                      {step.tips.length > 0 && (
                        <div>
                          <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            💡 Tips:
                          </h6>
                          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            {step.tips.map((tip, tipIndex) => (
                              <li key={tipIndex} className="flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {tip}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorialEditorTestPage;
