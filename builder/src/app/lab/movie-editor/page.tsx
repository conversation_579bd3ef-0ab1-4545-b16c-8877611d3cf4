/**
 * 电影编辑器测试页面
 * 
 * 用于测试 Movie 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import movieConfig from '@/content-schema/Movie';
import { 
  Film, 
  Edit, 
  Eye, 
  Star, 
  Clock, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Lightbulb,
  ShoppingCart,
  Timer,
  Thermometer,
  Play,
  Monitor,
  Tablet,
  Laptop,
  Camera,
  Mic
} from 'lucide-react';

// 电影数据类型定义
interface Rating {
  imdb: number;
  douban: number;
  rottenTomatoes: number;
  metacritic: number;
}

interface Director {
  name: string;
  bio: string;
  avatar: string;
}

interface CastMember {
  name: string;
  character: string;
  bio: string;
  avatar: string;
  isLead: boolean;
}

interface Production {
  studio: string[];
  distributor: string[];
  budget: number;
  boxOffice: {
    domestic: number;
    international: number;
    total: number;
  };
}

interface Technical {
  aspectRatio: string;
  soundMix: string[];
  color: string;
  filmingFormat: string[];
}

interface Award {
  name: string;
  category: string;
  year: number;
  result: string;
  organization: string;
}

interface MovieImage {
  url: string;
  caption: string;
  alt: string;
  type: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface MovieData {
  title: string;
  slug: string;
  description: string;
  originalTitle: string;
  genre: string[];
  releaseDate: string;
  duration: number;
  rating: Rating;
  director: Director[];
  cast: CastMember[];
  production: Production;
  technical: Technical;
  awards: Award[];
  images: MovieImage[];
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认电影数据
const defaultMovieData: MovieData = {
  title: "流浪地球2 - 科幻史诗巨制的视觉盛宴",
  slug: "wandering-earth-2-sci-fi-epic",
  description: "《流浪地球2》是一部宏大的科幻史诗电影，讲述了人类面临太阳危机时，通过国际合作建造行星发动机拯救地球的故事。影片以震撼的视觉效果和深刻的人文思考，展现了人类在绝境中的坚韧与希望。",
  originalTitle: "The Wandering Earth II",
  genre: ["sci-fi", "action", "drama"],
  releaseDate: "2023-01-22",
  duration: 173,
  rating: {
    imdb: 7.4,
    douban: 8.3,
    rottenTomatoes: 75,
    metacritic: 68
  },
  director: [
    {
      name: "郭帆",
      bio: "中国著名导演，以科幻电影见长，代表作品包括《流浪地球》系列",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    }
  ],
  cast: [
    {
      name: "吴京",
      character: "刘培强",
      bio: "中国著名演员，动作片明星",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      isLead: true
    },
    {
      name: "刘德华",
      character: "图恒宇",
      bio: "香港著名演员、歌手",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      isLead: true
    },
    {
      name: "李雪健",
      character: "周喆直",
      bio: "中国著名演员，实力派表演艺术家",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      isLead: false
    }
  ],
  production: {
    studio: ["中国电影股份有限公司", "北京文化传媒有限公司"],
    distributor: ["中国电影股份有限公司"],
    budget: 60000,
    boxOffice: {
      domestic: 406800,
      international: 5200,
      total: 412000
    }
  },
  technical: {
    aspectRatio: "2.35:1",
    soundMix: ["Dolby Atmos", "DTS:X", "IMAX Enhanced"],
    color: "color",
    filmingFormat: ["IMAX", "35mm", "Digital"]
  },
  awards: [
    {
      name: "最佳视觉效果",
      category: "技术奖项",
      year: 2023,
      result: "won",
      organization: "中国电影金鸡奖"
    }
  ],
  images: [
    {
      url: "https://images.unsplash.com/photo-1489599511986-c2e8b3b5b6b8?w=800&h=1200&fit=crop",
      caption: "《流浪地球2》官方海报",
      alt: "流浪地球2电影海报",
      type: "poster"
    },
    {
      url: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
      caption: "电影剧照 - 行星发动机场景",
      alt: "流浪地球2剧照",
      type: "still"
    }
  ],
  tags: ["科幻电影", "中国电影", "视觉特效", "太空题材"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "流浪地球2 - 科幻史诗巨制电影详情 | 电影资讯",
    metaDescription: "《流浪地球2》是一部宏大的科幻史诗电影，震撼视觉效果展现人类拯救地球的故事。查看详细剧情、演员阵容、评分和幕后花絮。",
    keywords: ["流浪地球2", "科幻电影", "中国电影", "吴京", "刘德华"]
  }
};

// 电影类型配置
const genreConfig = {
  action: { label: '动作', icon: '🎬', color: 'text-red-600' },
  adventure: { label: '冒险', icon: '🗺️', color: 'text-green-600' },
  animation: { label: '动画', icon: '🎨', color: 'text-purple-600' },
  comedy: { label: '喜剧', icon: '😄', color: 'text-yellow-600' },
  crime: { label: '犯罪', icon: '🔍', color: 'text-gray-600' },
  documentary: { label: '纪录片', icon: '📹', color: 'text-blue-600' },
  drama: { label: '剧情', icon: '🎭', color: 'text-indigo-600' },
  family: { label: '家庭', icon: '👨‍👩‍👧‍👦', color: 'text-pink-600' },
  fantasy: { label: '奇幻', icon: '🧙‍♂️', color: 'text-purple-600' },
  history: { label: '历史', icon: '📚', color: 'text-brown-600' },
  horror: { label: '恐怖', icon: '👻', color: 'text-red-600' },
  music: { label: '音乐', icon: '🎵', color: 'text-green-600' },
  mystery: { label: '悬疑', icon: '🔮', color: 'text-purple-600' },
  romance: { label: '爱情', icon: '💕', color: 'text-pink-600' },
  'sci-fi': { label: '科幻', icon: '🚀', color: 'text-blue-600' },
  thriller: { label: '惊悚', icon: '😱', color: 'text-red-600' },
  war: { label: '战争', icon: '⚔️', color: 'text-gray-600' },
  western: { label: '西部', icon: '🤠', color: 'text-orange-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const MovieEditorTestPage = () => {
  const [formData, setFormData] = useState<MovieData>(defaultMovieData);

  const handleSave = (data: MovieData) => {
    console.log('Saved movie data:', data);
    setFormData(data);
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`;
  };

  const renderStars = (rating: number, maxRating: number = 10) => {
    const stars = Math.round((rating / maxRating) * 5);
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Film className="w-8 h-8 text-blue-600" />
            Movie Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Movie内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              电影编辑器
            </h2>
            <BlockEditorShadcn
              config={movieConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              电影预览
            </h2>
            
            <div className="space-y-6">
              {/* 电影头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {formData.genre.map((g, index) => {
                        const genreInfo = genreConfig[g as keyof typeof genreConfig];
                        return (
                          <span key={index} className={`text-sm ${genreInfo?.color || 'text-gray-600'}`}>
                            {genreInfo?.icon} {genreInfo?.label || g}
                          </span>
                        );
                      })}
                      <span className="text-sm text-gray-500">
                        {formatDuration(formData.duration)}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {formData.title}
                    </h3>
                    {formData.originalTitle && (
                      <h4 className="text-lg text-gray-600 dark:text-gray-300 mb-2">
                        {formData.originalTitle}
                      </h4>
                    )}
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {formatDate(formData.releaseDate)}
                    </div>
                    <div className="text-xs text-gray-500">上映日期</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600 flex items-center justify-center gap-1">
                      <Clock className="w-4 h-4" />
                      {formatDuration(formData.duration)}
                    </div>
                    <div className="text-xs text-gray-500">片长</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 flex items-center justify-center gap-1">
                      <Users className="w-4 h-4" />
                      {formData.director.length}
                    </div>
                    <div className="text-xs text-gray-500">导演</div>
                  </div>
                </div>

                {/* 主要海报 */}
                {formData.images.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.images[0].url}
                      alt={formData.images[0].alt}
                      className="w-full h-64 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.images[0].caption}
                    </p>
                  </div>
                )}
              </div>

              {/* 评分信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  评分信息
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{formData.rating.imdb}</div>
                    <div className="text-xs text-yellow-700 dark:text-yellow-300">IMDb</div>
                    <div className="flex justify-center mt-1">
                      {renderStars(formData.rating.imdb)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{formData.rating.douban}</div>
                    <div className="text-xs text-green-700 dark:text-green-300">豆瓣</div>
                    <div className="flex justify-center mt-1">
                      {renderStars(formData.rating.douban)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="text-lg font-bold text-red-600">{formData.rating.rottenTomatoes}%</div>
                    <div className="text-xs text-red-700 dark:text-red-300">烂番茄</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{formData.rating.metacritic}</div>
                    <div className="text-xs text-blue-700 dark:text-blue-300">Metacritic</div>
                  </div>
                </div>
              </div>

              {/* 导演信息 */}
              {formData.director.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Camera className="w-5 h-5" />
                    导演
                  </h4>
                  <div className="space-y-3">
                    {formData.director.map((director, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <img
                          src={director.avatar}
                          alt={director.name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {director.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                            {director.bio}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 主要演员 */}
              {formData.cast.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    主要演员
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.cast.map((actor, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <img
                          src={actor.avatar}
                          alt={actor.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {actor.name}
                            </h5>
                            {actor.isLead && (
                              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded">
                                主演
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-blue-600 dark:text-blue-400">
                            饰演: {actor.character}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {actor.bio}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 票房信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  票房信息
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="text-xl font-bold text-green-600">
                      {formData.production.boxOffice.domestic.toLocaleString()}万
                    </div>
                    <div className="text-sm text-green-700 dark:text-green-300">国内票房</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">
                      {formData.production.boxOffice.international.toLocaleString()}万
                    </div>
                    <div className="text-sm text-blue-700 dark:text-blue-300">国际票房</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                    <div className="text-xl font-bold text-purple-600">
                      {formData.production.boxOffice.total.toLocaleString()}万
                    </div>
                    <div className="text-sm text-purple-700 dark:text-purple-300">全球总票房</div>
                  </div>
                </div>
                <div className="mt-4 text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    制作预算: {formData.production.budget.toLocaleString()}万元
                  </div>
                </div>
              </div>

              {/* 电影标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    电影标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MovieEditorTestPage;
