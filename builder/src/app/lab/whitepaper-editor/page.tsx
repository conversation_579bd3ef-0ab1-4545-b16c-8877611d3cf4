/**
 * 白皮书编辑器测试页面
 * 
 * 用于测试 Whitepaper 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import whitepaperConfig from '@/content-schema/Whitepaper';
import { 
  FileText, 
  Edit, 
  Eye, 
  Download, 
  User, 
  Building,
  Calendar,
  Clock,
  Tag,
  Hash,
  Award,
  CheckCircle,
  AlertCircle,
  Globe,
  Lock,
  Star,
  Users
} from 'lucide-react';

// 白皮书数据类型定义
interface Author {
  name: string;
  title: string;
  organization: string;
  bio?: string;
  photo?: string;
  email?: string;
  linkedin?: string;
}

interface Publisher {
  name: string;
  logo?: string;
  website?: string;
  description?: string;
}

interface DownloadInfo {
  fileUrl: string;
  fileSize?: string;
  requiresRegistration: boolean;
  downloadCount: number;
}

interface RelatedContent {
  title: string;
  type: 'whitepaper' | 'case-study' | 'blog-post' | 'webinar' | 'video';
  url: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface WhitepaperData {
  title: string;
  slug: string;
  subtitle?: string;
  abstract: string;
  content: string;
  authors: Author[];
  publisher: Publisher;
  category: string;
  tags: string[];
  publishDate: string;
  lastUpdated: string;
  pageCount: number;
  language: string;
  downloadInfo: DownloadInfo;
  isPublished: boolean;
  isFeatured: boolean;
  accessLevel: 'public' | 'registered' | 'premium' | 'enterprise';
  relatedContent: RelatedContent[];
  seo: SEO;
}

// 默认白皮书数据
const defaultWhitepaperData: WhitepaperData = {
  title: "The Future of Artificial Intelligence in Business: A Comprehensive Guide",
  slug: "future-ai-business-guide",
  subtitle: "How AI is transforming industries and what businesses need to know to stay competitive",
  abstract: "This whitepaper explores the current state and future potential of artificial intelligence in business applications. We examine key trends, implementation strategies, challenges, and opportunities across various industries. Based on extensive research and real-world case studies, this guide provides actionable insights for business leaders looking to leverage AI technologies for competitive advantage.",
  content: "# Executive Summary\n\nArtificial Intelligence is no longer a futuristic concept—it's a present reality transforming how businesses operate, compete, and deliver value to customers.\n\n## Introduction\n\nThe rapid advancement of AI technologies has created unprecedented opportunities for businesses across all sectors...",
  authors: [
    {
      name: "Dr. Sarah Chen",
      title: "Chief AI Officer",
      organization: "TechVision Research",
      bio: "Dr. Chen is a leading expert in artificial intelligence with over 15 years of experience in AI research and business applications.",
      photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      email: "<EMAIL>",
      linkedin: "https://linkedin.com/in/sarahchen"
    }
  ],
  publisher: {
    name: "TechVision Research Institute",
    logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop",
    website: "https://techvision.com",
    description: "TechVision Research Institute is a leading technology research organization focused on emerging trends and business applications of cutting-edge technologies."
  },
  category: "Artificial Intelligence",
  tags: ["artificial-intelligence", "business-strategy", "digital-transformation", "technology-trends"],
  publishDate: new Date().toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  pageCount: 25,
  language: "en",
  downloadInfo: {
    fileUrl: "https://example.com/whitepapers/ai-business-guide.pdf",
    fileSize: "2.5 MB",
    requiresRegistration: true,
    downloadCount: 0
  },
  isPublished: false,
  isFeatured: false,
  accessLevel: "registered",
  relatedContent: [],
  seo: {
    metaTitle: "The Future of AI in Business - Free Whitepaper Download",
    metaDescription: "Download our comprehensive whitepaper on AI in business. Learn about implementation strategies, challenges, and future opportunities. Free PDF guide available.",
    keywords: ["AI whitepaper", "artificial intelligence business", "AI implementation guide", "business AI strategy"]
  }
};

// 访问级别配置
const accessLevelConfig = {
  public: { 
    label: 'Public', 
    icon: Globe, 
    color: 'text-green-700 bg-green-50 border-green-200',
    description: 'Anyone can access'
  },
  registered: { 
    label: 'Registered', 
    icon: User, 
    color: 'text-blue-700 bg-blue-50 border-blue-200',
    description: 'Requires registration'
  },
  premium: { 
    label: 'Premium', 
    icon: Star, 
    color: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    description: 'Requires premium account'
  },
  enterprise: { 
    label: 'Enterprise', 
    icon: Building, 
    color: 'text-purple-700 bg-purple-50 border-purple-200',
    description: 'Enterprise customers only'
  }
} as const;

// 语言配置
const languageConfig = {
  en: 'English',
  es: 'Spanish',
  fr: 'French',
  de: 'German',
  zh: 'Chinese',
  ja: 'Japanese',
  ko: 'Korean'
} as const;

const WhitepaperEditorTestPage = () => {
  const [formData, setFormData] = useState<WhitepaperData>(defaultWhitepaperData);

  const handleSave = (data: WhitepaperData) => {
    console.log('Saved whitepaper data:', data);
    setFormData(data);
  };

  const AccessIcon = accessLevelConfig[formData.accessLevel].icon;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <FileText className="w-8 h-8 text-blue-600" />
            Whitepaper Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the Whitepaper content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Whitepaper Editor
            </h2>
            <BlockEditorShadcn
              config={whitepaperConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Whitepaper Preview
            </h2>
            
            <div className="space-y-6">
              {/* 白皮书头部 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                      {formData.isFeatured && (
                        <Award className="w-6 h-6 text-yellow-500 inline ml-2" />
                      )}
                    </h3>
                    {formData.subtitle && (
                      <p className="text-lg text-gray-600 dark:text-gray-300 mb-3">
                        {formData.subtitle}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${accessLevelConfig[formData.accessLevel].color}`}>
                      <AccessIcon className="w-3 h-3" />
                      {accessLevelConfig[formData.accessLevel].label}
                    </span>
                    {formData.isPublished ? (
                      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800">
                        <CheckCircle className="w-3 h-3" />
                        Published
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
                        <AlertCircle className="w-3 h-3" />
                        Draft
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {formData.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <FileText className="w-4 h-4" />
                    {formData.pageCount} pages
                  </span>
                  <span className="flex items-center gap-1">
                    <Globe className="w-4 h-4" />
                    {languageConfig[formData.language as keyof typeof languageConfig]}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formData.publishDate}
                  </span>
                </div>

                {/* 下载信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-1">
                        Download PDF
                      </h4>
                      <p className="text-sm text-blue-700 dark:text-blue-400">
                        {formData.downloadInfo.fileSize} • {formData.downloadInfo.downloadCount} downloads
                        {formData.downloadInfo.requiresRegistration && " • Registration required"}
                      </p>
                    </div>
                    <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      <Download className="w-4 h-4" />
                      Download
                    </button>
                  </div>
                </div>
              </div>

              {/* 摘要 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Abstract
                </h4>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {formData.abstract}
                </p>
              </div>

              {/* 作者信息 */}
              {formData.authors.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Authors
                  </h4>
                  <div className="space-y-3">
                    {formData.authors.map((author, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        {author.photo && (
                          <img
                            src={author.photo}
                            alt={author.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {author.name}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {author.title} at {author.organization}
                          </p>
                          {author.bio && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {author.bio}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {author.email && (
                            <a
                              href={`mailto:${author.email}`}
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                              <User className="w-4 h-4" />
                            </a>
                          )}
                          {author.linkedin && (
                            <a
                              href={author.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                              <Building className="w-4 h-4" />
                            </a>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 发布机构 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  Publisher
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-3">
                    {formData.publisher.logo && (
                      <img
                        src={formData.publisher.logo}
                        alt={formData.publisher.name}
                        className="w-16 h-8 object-contain bg-white rounded"
                      />
                    )}
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {formData.publisher.name}
                      </h5>
                      {formData.publisher.website && (
                        <a
                          href={formData.publisher.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          Visit Website
                        </a>
                      )}
                    </div>
                  </div>
                  {formData.publisher.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.publisher.description}
                    </p>
                  )}
                </div>
              </div>

              {/* 内容预览 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Content Preview
                </h4>
                <div className="prose prose-sm max-w-none dark:prose-invert bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <ReactMarkdown>{formData.content}</ReactMarkdown>
                </div>
              </div>

              {/* 相关内容 */}
              {formData.relatedContent.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Related Content
                  </h4>
                  <div className="space-y-2">
                    {formData.relatedContent.map((content, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {content.title}
                          </h5>
                          <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                            {content.type.replace('-', ' ')}
                          </span>
                        </div>
                        <a
                          href={content.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                        >
                          View
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhitepaperEditorTestPage;
