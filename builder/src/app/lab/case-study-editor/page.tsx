/**
 * 案例研究编辑器测试页面
 * 
 * 用于测试 Case Study 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import caseStudyConfig from '@/content-schema/CaseStudy';
import { 
  FileText, 
  Edit, 
  Eye, 
  Building, 
  Target, 
  Lightbulb,
  TrendingUp,
  Clock,
  Tag,
  Calendar,
  Hash,
  Award,
  CheckCircle,
  AlertCircle,
  Quote,
  BarChart3
} from 'lucide-react';

// 案例研究数据类型定义
interface Client {
  name: string;
  industry: string;
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  location?: string;
  website?: string;
  logo?: string;
}

interface Challenge {
  overview: string;
  painPoints: string[];
  impact?: string;
}

interface Feature {
  name: string;
  description: string;
}

interface Solution {
  approach: string;
  features: Feature[];
  timeline?: string;
  team: string[];
}

interface Metric {
  metric: string;
  before: string;
  after: string;
  improvement: string;
}

interface Testimonial {
  quote: string;
  author: string;
  position: string;
  photo?: string;
}

interface Results {
  metrics: Metric[];
  testimonial?: Testimonial;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface CaseStudyData {
  title: string;
  slug: string;
  summary: string;
  client: Client;
  challenge: Challenge;
  solution: Solution;
  results: Results;
  tags: string[];
  category: string;
  publishDate: string;
  lastUpdated: string;
  isPublished: boolean;
  isFeatured: boolean;
  readTime: number;
  seo: SEO;
}

// 默认案例研究数据
const defaultCaseStudyData: CaseStudyData = {
  title: "How TechCorp Increased Revenue by 300% with Our Platform",
  slug: "techcorp-revenue-increase-case-study",
  summary: "TechCorp, a mid-sized software company, partnered with us to streamline their sales process and improve customer engagement. Within 6 months, they achieved a 300% increase in revenue and reduced customer acquisition costs by 45%.",
  client: {
    name: "TechCorp Solutions",
    industry: "Software Development",
    size: "medium",
    location: "San Francisco, CA",
    website: "https://techcorp.example.com",
    logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop"
  },
  challenge: {
    overview: "TechCorp was struggling with an inefficient sales process that resulted in long sales cycles, poor lead qualification, and high customer acquisition costs. Their existing tools were disconnected, leading to data silos and missed opportunities.",
    painPoints: [
      "Sales cycle was 40% longer than industry average",
      "Lead qualification process was manual and time-consuming",
      "Customer data was scattered across multiple systems",
      "Sales team spent 60% of time on administrative tasks"
    ],
    impact: "These challenges resulted in missed revenue opportunities, frustrated sales teams, and declining customer satisfaction scores."
  },
  solution: {
    approach: "We implemented a comprehensive sales automation platform that integrated with TechCorp's existing systems, streamlined their lead qualification process, and provided real-time analytics and insights.",
    features: [
      {
        name: "Automated Lead Scoring",
        description: "AI-powered lead scoring system that automatically qualifies and prioritizes leads based on behavior and demographics"
      },
      {
        name: "CRM Integration",
        description: "Seamless integration with existing CRM systems to eliminate data silos and improve data accuracy"
      },
      {
        name: "Real-time Analytics",
        description: "Comprehensive dashboard providing real-time insights into sales performance and pipeline health"
      }
    ],
    timeline: "6 months",
    team: ["Project Manager", "Sales Engineer", "Technical Consultant", "Customer Success Manager"]
  },
  results: {
    metrics: [
      {
        metric: "Monthly Revenue",
        before: "$500K",
        after: "$2M",
        improvement: "+300%"
      },
      {
        metric: "Sales Cycle Length",
        before: "120 days",
        after: "75 days",
        improvement: "-37.5%"
      },
      {
        metric: "Lead Conversion Rate",
        before: "12%",
        after: "28%",
        improvement: "+133%"
      }
    ],
    testimonial: {
      quote: "The platform completely transformed our sales process. We've seen unprecedented growth and our team is more productive than ever. The ROI was evident within the first quarter.",
      author: "Sarah Johnson",
      position: "VP of Sales, TechCorp Solutions",
      photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    }
  },
  tags: ["sales-automation", "revenue-growth", "crm-integration", "lead-generation"],
  category: "Sales & Marketing",
  publishDate: new Date().toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  isPublished: false,
  isFeatured: false,
  readTime: 8,
  seo: {
    metaTitle: "TechCorp Case Study: 300% Revenue Increase | Success Stories",
    metaDescription: "Discover how TechCorp achieved a 300% revenue increase and reduced sales cycle time by 37.5% using our sales automation platform. Read the full case study.",
    keywords: ["case study", "sales automation", "revenue growth", "crm integration", "success story"]
  }
};

// 公司规模配置
const companySizeConfig = {
  startup: { label: 'Startup', icon: '🚀', description: '1-10 employees' },
  small: { label: 'Small', icon: '🏢', description: '11-50 employees' },
  medium: { label: 'Medium', icon: '🏬', description: '51-200 employees' },
  large: { label: 'Large', icon: '🏭', description: '201-1000 employees' },
  enterprise: { label: 'Enterprise', icon: '🏛️', description: '1000+ employees' }
} as const;

const CaseStudyEditorTestPage = () => {
  const [formData, setFormData] = useState<CaseStudyData>(defaultCaseStudyData);

  const handleSave = (data: CaseStudyData) => {
    console.log('Saved case study data:', data);
    setFormData(data);
  };

  const sizeConfig = companySizeConfig[formData.client.size];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <FileText className="w-8 h-8 text-blue-600" />
            Case Study Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the Case Study content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Case Study Editor
            </h2>
            <BlockEditorShadcn
              config={caseStudyConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Case Study Preview
            </h2>
            
            <div className="space-y-6">
              {/* 案例研究头部 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formData.title}
                    {formData.isFeatured && (
                      <Award className="w-6 h-6 text-yellow-500 inline ml-2" />
                    )}
                  </h3>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${
                      formData.isPublished 
                        ? 'text-green-700 bg-green-50 border-green-200' 
                        : 'text-gray-700 bg-gray-50 border-gray-200'
                    }`}>
                      {formData.isPublished ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <AlertCircle className="w-3 h-3" />
                      )}
                      {formData.isPublished ? 'Published' : 'Draft'}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {formData.summary}
                </p>

                <div className="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {formData.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formData.readTime} min read
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formData.publishDate}
                  </span>
                </div>
              </div>

              {/* 客户信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  Client Overview
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-3">
                    {formData.client.logo && (
                      <img
                        src={formData.client.logo}
                        alt={formData.client.name}
                        className="w-16 h-8 object-contain bg-white rounded"
                      />
                    )}
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {formData.client.name}
                      </h5>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {formData.client.industry}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Size:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {sizeConfig.icon} {sizeConfig.label} ({sizeConfig.description})
                      </span>
                    </div>
                    {formData.client.location && (
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Location:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formData.client.location}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 挑战与问题 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Challenge
                </h4>
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    {formData.challenge.overview}
                  </p>

                  {formData.challenge.painPoints.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">Key Pain Points:</h5>
                      <ul className="space-y-2">
                        {formData.challenge.painPoints.map((point, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-300">
                            <span className="text-red-500 mt-1">•</span>
                            {point}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {formData.challenge.impact && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                      <p className="text-sm text-red-800 dark:text-red-300">
                        <strong>Business Impact:</strong> {formData.challenge.impact}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* 解决方案 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Lightbulb className="w-5 h-5" />
                  Solution
                </h4>
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    {formData.solution.approach}
                  </p>

                  {formData.solution.features.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-3">Key Features:</h5>
                      <div className="grid grid-cols-1 gap-3">
                        {formData.solution.features.map((feature, index) => (
                          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                            <h6 className="font-medium text-gray-900 dark:text-white mb-1">
                              {feature.name}
                            </h6>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {feature.description}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
                    {formData.solution.timeline && (
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        Timeline: {formData.solution.timeline}
                      </span>
                    )}
                    <span className="flex items-center gap-1">
                      <Building className="w-4 h-4" />
                      Team: {formData.solution.team.length} members
                    </span>
                  </div>
                </div>
              </div>

              {/* 结果与成果 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Results
                </h4>
                <div className="space-y-4">
                  {/* 关键指标 */}
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Key Metrics
                    </h5>
                    <div className="grid grid-cols-1 gap-3">
                      {formData.results.metrics.map((metric, index) => (
                        <div key={index} className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <h6 className="font-medium text-gray-900 dark:text-white">
                              {metric.metric}
                            </h6>
                            <span className="text-lg font-bold text-green-600 dark:text-green-400">
                              {metric.improvement}
                            </span>
                          </div>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-300">
                            <span>Before: <strong>{metric.before}</strong></span>
                            <span>→</span>
                            <span>After: <strong>{metric.after}</strong></span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 客户推荐 */}
                  {formData.results.testimonial && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                        <Quote className="w-4 h-4" />
                        Client Testimonial
                      </h5>
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <blockquote className="text-gray-700 dark:text-gray-300 mb-3 italic">
                          "{formData.results.testimonial.quote}"
                        </blockquote>
                        <div className="flex items-center gap-3">
                          {formData.results.testimonial.photo && (
                            <img
                              src={formData.results.testimonial.photo}
                              alt={formData.results.testimonial.author}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {formData.results.testimonial.author}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-300">
                              {formData.results.testimonial.position}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseStudyEditorTestPage;
