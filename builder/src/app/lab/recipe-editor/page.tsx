/**
 * 食谱编辑器测试页面
 * 
 * 用于测试 Recipe 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import recipeConfig from '@/content-schema/Recipe';
import { 
  ChefHat, 
  Edit, 
  Eye, 
  Clock, 
  Users, 
  Utensils,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  Star,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Lightbulb,
  ShoppingCart,
  Timer,
  Thermometer,
  Play
} from 'lucide-react';

// 食谱数据类型定义
interface Ingredient {
  name: string;
  amount: number;
  unit: string;
  category: string;
  optional: boolean;
  notes?: string;
}

interface Instruction {
  step: number;
  title?: string;
  description: string;
  duration?: number;
  temperature?: string;
  tips?: string;
  image?: string;
}

interface Nutrition {
  calories: number;
  protein: number;
  carbohydrates: number;
  fat: number;
  fiber: number;
  sodium: number;
  sugar: number;
}

interface Equipment {
  name: string;
  essential: boolean;
  alternative?: string;
}

interface Tip {
  category: string;
  title: string;
  description: string;
}

interface RecipeImage {
  url: string;
  caption: string;
  alt: string;
  type: string;
}

interface Video {
  url: string;
  title: string;
  duration: number;
  thumbnail: string;
  platform: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface RecipeData {
  title: string;
  slug: string;
  description: string;
  cuisine: string;
  category: string;
  difficulty: string;
  servings: number;
  prepTime: number;
  cookTime: number;
  totalTime: number;
  ingredients: Ingredient[];
  instructions: Instruction[];
  nutrition: Nutrition;
  equipment: Equipment[];
  tips: Tip[];
  images: RecipeImage[];
  video?: Video;
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认食谱数据
const defaultRecipeData: RecipeData = {
  title: "经典红烧肉 - 传统家常菜的完美做法",
  slug: "classic-braised-pork-belly",
  description: "这道经典红烧肉采用传统做法，肥瘦相间的五花肉经过精心烹制，色泽红亮，肉质软糯，甜而不腻。配以简单的调料和独特的烹饪技巧，让您在家也能做出餐厅级别的美味红烧肉。",
  cuisine: "chinese",
  category: "main-course",
  difficulty: "intermediate",
  servings: 4,
  prepTime: 20,
  cookTime: 60,
  totalTime: 80,
  ingredients: [
    {
      name: "五花肉",
      amount: 500,
      unit: "g",
      category: "meat",
      optional: false,
      notes: "选择肥瘦相间的新鲜五花肉"
    },
    {
      name: "生抽",
      amount: 3,
      unit: "勺",
      category: "sauce",
      optional: false
    },
    {
      name: "老抽",
      amount: 1,
      unit: "勺",
      category: "sauce",
      optional: false,
      notes: "用于上色"
    },
    {
      name: "冰糖",
      amount: 30,
      unit: "g",
      category: "other",
      optional: false
    },
    {
      name: "料酒",
      amount: 2,
      unit: "勺",
      category: "sauce",
      optional: false
    },
    {
      name: "生姜",
      amount: 3,
      unit: "片",
      category: "spice",
      optional: false
    },
    {
      name: "大葱",
      amount: 2,
      unit: "根",
      category: "vegetable",
      optional: false
    }
  ],
  instructions: [
    {
      step: 1,
      title: "准备食材",
      description: "将五花肉洗净切成3cm见方的块状，生姜切片，大葱切段备用。",
      duration: 10,
      tips: "肉块不要切得太小，否则容易散烂"
    },
    {
      step: 2,
      title: "焯水处理",
      description: "锅中加水烧开，放入肉块焯水3-5分钟，撇去浮沫后捞出沥干。",
      duration: 8,
      temperature: "大火",
      tips: "焯水可以去除血水和腥味"
    },
    {
      step: 3,
      title: "炒糖色",
      description: "锅中放少量油，小火炒制冰糖至焦糖色，倒入肉块翻炒上色。",
      duration: 5,
      temperature: "小火",
      tips: "炒糖色时火候要小，避免炒糊"
    },
    {
      step: 4,
      title: "调味炖煮",
      description: "加入生抽、老抽、料酒，放入姜片和葱段，加适量热水没过肉块，大火烧开后转小火炖煮45分钟。",
      duration: 50,
      temperature: "先大火后小火",
      tips: "水要用热水，冷水会让肉质变硬"
    },
    {
      step: 5,
      title: "收汁装盘",
      description: "大火收汁至汤汁浓稠，撒上葱花即可装盘。",
      duration: 5,
      temperature: "大火",
      tips: "收汁时要不断翻炒，避免粘锅"
    }
  ],
  nutrition: {
    calories: 450,
    protein: 25.5,
    carbohydrates: 8.2,
    fat: 35.8,
    fiber: 0.5,
    sodium: 890,
    sugar: 6.8
  },
  equipment: [
    {
      name: "炒锅",
      essential: true,
      alternative: "平底锅或砂锅"
    },
    {
      name: "锅铲",
      essential: true
    },
    {
      name: "菜刀",
      essential: true
    },
    {
      name: "砧板",
      essential: true
    }
  ],
  tips: [
    {
      category: "ingredient",
      title: "选择五花肉",
      description: "选择肥瘦相间、层次分明的五花肉，肉质要新鲜有弹性"
    },
    {
      category: "technique",
      title: "炒糖色技巧",
      description: "炒糖色时要用小火慢炒，糖色呈焦糖色时立即下肉，动作要快"
    },
    {
      category: "timing",
      title: "炖煮时间",
      description: "炖煮时间要充足，至少45分钟，让肉质充分软烂"
    }
  ],
  images: [
    {
      url: "https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop",
      caption: "经典红烧肉成品图",
      alt: "红烧肉最终成品",
      type: "main"
    }
  ],
  tags: ["红烧肉", "家常菜", "中式菜", "下饭菜", "肉类"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "经典红烧肉做法 - 传统家常菜完美制作教程 | 美食食谱",
    metaDescription: "学会这道经典红烧肉的传统做法，肥瘦相间，色泽红亮，肉质软糯。详细步骤图解，新手也能轻松掌握，让您在家做出餐厅级美味。",
    keywords: ["红烧肉做法", "家常菜谱", "中式菜谱", "肉类料理"]
  }
};

// 菜系配置
const cuisineConfig = {
  chinese: { label: '中式', icon: '🥢', color: 'text-red-600' },
  western: { label: '西式', icon: '🍽️', color: 'text-blue-600' },
  japanese: { label: '日式', icon: '🍱', color: 'text-pink-600' },
  korean: { label: '韩式', icon: '🥘', color: 'text-orange-600' },
  thai: { label: '泰式', icon: '🌶️', color: 'text-green-600' },
  indian: { label: '印式', icon: '🍛', color: 'text-yellow-600' },
  italian: { label: '意式', icon: '🍝', color: 'text-purple-600' },
  french: { label: '法式', icon: '🥐', color: 'text-indigo-600' },
  mexican: { label: '墨式', icon: '🌮', color: 'text-teal-600' },
  other: { label: '其他', icon: '🌍', color: 'text-gray-600' }
} as const;

// 难度配置
const difficultyConfig = {
  beginner: { label: '新手', color: 'text-green-700 bg-green-50 border-green-200', icon: '🟢' },
  intermediate: { label: '中级', color: 'text-yellow-700 bg-yellow-50 border-yellow-200', icon: '🟡' },
  advanced: { label: '高级', color: 'text-orange-700 bg-orange-50 border-orange-200', icon: '🟠' },
  expert: { label: '专家', color: 'text-red-700 bg-red-50 border-red-200', icon: '🔴' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const RecipeEditorTestPage = () => {
  const [formData, setFormData] = useState<RecipeData>(defaultRecipeData);

  const handleSave = (data: RecipeData) => {
    console.log('Saved recipe data:', data);
    setFormData(data);
  };

  const cuisineInfo = cuisineConfig[formData.cuisine as keyof typeof cuisineConfig];
  
  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      meat: 'bg-red-100 text-red-700 border-red-200',
      vegetable: 'bg-green-100 text-green-700 border-green-200',
      fruit: 'bg-pink-100 text-pink-700 border-pink-200',
      grain: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      dairy: 'bg-blue-100 text-blue-700 border-blue-200',
      spice: 'bg-orange-100 text-orange-700 border-orange-200',
      sauce: 'bg-purple-100 text-purple-700 border-purple-200',
      oil: 'bg-amber-100 text-amber-700 border-amber-200',
      other: 'bg-gray-100 text-gray-700 border-gray-200'
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <ChefHat className="w-8 h-8 text-blue-600" />
            Recipe Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Recipe内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              食谱编辑器
            </h2>
            <BlockEditorShadcn
              config={recipeConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              食谱预览
            </h2>
            
            <div className="space-y-6">
              {/* 食谱头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${cuisineInfo.color}`}>
                        {cuisineInfo.icon} {cuisineInfo.label}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${difficultyConfig[formData.difficulty as keyof typeof difficultyConfig].color}`}>
                        {difficultyConfig[formData.difficulty as keyof typeof difficultyConfig].icon} {difficultyConfig[formData.difficulty as keyof typeof difficultyConfig].label}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 食谱基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                      <Users className="w-4 h-4" />
                      {formData.servings}
                    </div>
                    <div className="text-xs text-gray-500">份量</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600 flex items-center justify-center gap-1">
                      <Timer className="w-4 h-4" />
                      {formatTime(formData.prepTime)}
                    </div>
                    <div className="text-xs text-gray-500">准备时间</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600 flex items-center justify-center gap-1">
                      <Clock className="w-4 h-4" />
                      {formatTime(formData.cookTime)}
                    </div>
                    <div className="text-xs text-gray-500">烹饪时间</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 flex items-center justify-center gap-1">
                      <Clock className="w-4 h-4" />
                      {formatTime(formData.totalTime)}
                    </div>
                    <div className="text-xs text-gray-500">总时间</div>
                  </div>
                </div>

                {/* 主图 */}
                {formData.images.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.images[0].url}
                      alt={formData.images[0].alt}
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.images[0].caption}
                    </p>
                  </div>
                )}
              </div>

              {/* 食材清单 */}
              {formData.ingredients.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <ShoppingCart className="w-5 h-5" />
                    食材清单
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.ingredients.map((ingredient, index) => (
                      <div key={index} className={`flex items-center justify-between p-3 rounded-lg border ${getCategoryColor(ingredient.category)}`}>
                        <div className="flex-1">
                          <span className="font-medium">
                            {ingredient.name}
                            {ingredient.optional && <span className="text-xs ml-1 opacity-75">(可选)</span>}
                          </span>
                          {ingredient.notes && (
                            <div className="text-xs opacity-75 mt-1">
                              {ingredient.notes}
                            </div>
                          )}
                        </div>
                        <span className="text-sm font-medium ml-2">
                          {ingredient.amount} {ingredient.unit}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 制作步骤 */}
              {formData.instructions.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Utensils className="w-5 h-5" />
                    制作步骤
                  </h4>
                  <div className="space-y-4">
                    {formData.instructions.map((instruction, index) => (
                      <div key={index} className="flex gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                          {instruction.step}
                        </div>
                        <div className="flex-1">
                          {instruction.title && (
                            <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                              {instruction.title}
                            </h5>
                          )}
                          <p className="text-gray-600 dark:text-gray-300 mb-2">
                            {instruction.description}
                          </p>
                          <div className="flex flex-wrap gap-2 text-xs">
                            {instruction.duration && (
                              <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded">
                                <Timer className="w-3 h-3 mr-1" />
                                {instruction.duration}分钟
                              </span>
                            )}
                            {instruction.temperature && (
                              <span className="inline-flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded">
                                <Thermometer className="w-3 h-3 mr-1" />
                                {instruction.temperature}
                              </span>
                            )}
                          </div>
                          {instruction.tips && (
                            <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-sm">
                              <span className="text-yellow-700 dark:text-yellow-300 font-medium">💡 小贴士：</span>
                              <span className="text-yellow-800 dark:text-yellow-400 ml-1">{instruction.tips}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 营养信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  营养信息 (每份)
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="text-lg font-bold text-red-600">{formData.nutrition.calories}</div>
                    <div className="text-xs text-red-700 dark:text-red-300">卡路里</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{formData.nutrition.protein}g</div>
                    <div className="text-xs text-blue-700 dark:text-blue-300">蛋白质</div>
                  </div>
                  <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{formData.nutrition.carbohydrates}g</div>
                    <div className="text-xs text-yellow-700 dark:text-yellow-300">碳水</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{formData.nutrition.fat}g</div>
                    <div className="text-xs text-purple-700 dark:text-purple-300">脂肪</div>
                  </div>
                </div>
              </div>

              {/* 所需厨具 */}
              {formData.equipment.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Utensils className="w-5 h-5" />
                    所需厨具
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.equipment.map((equipment, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex-1">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {equipment.name}
                          </span>
                          {equipment.alternative && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              替代：{equipment.alternative}
                            </div>
                          )}
                        </div>
                        <span className={`text-xs px-2 py-1 rounded ${equipment.essential ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'}`}>
                          {equipment.essential ? '必需' : '可选'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 烹饪技巧 */}
              {formData.tips.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Lightbulb className="w-5 h-5" />
                    烹饪技巧
                  </h4>
                  <div className="space-y-3">
                    {formData.tips.map((tip, index) => (
                      <div key={index} className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-start gap-2 mb-2">
                          <span className="text-lg">
                            {tip.category === 'ingredient' ? '🥬' :
                             tip.category === 'technique' ? '🔥' :
                             tip.category === 'timing' ? '⏰' :
                             tip.category === 'storage' ? '📦' :
                             tip.category === 'serving' ? '🍽️' : '🔄'}
                          </span>
                          <h5 className="font-medium text-blue-900 dark:text-blue-300">
                            {tip.title}
                          </h5>
                        </div>
                        <p className="text-sm text-blue-800 dark:text-blue-400 ml-7">
                          {tip.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 食谱标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    食谱标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipeEditorTestPage;
