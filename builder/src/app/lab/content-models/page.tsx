/**
 * 内容模型导航页面
 * 
 * 统一的内容模型测试导航页面，方便访问和测试所有已实现的内容模型编辑器
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { 
  BookOpen, 
  MessageSquare, 
  Package, 
  Megaphone, 
  GraduationCap,
  FileText,
  ScrollText,
  HelpCircle,
  ArrowRight,
  Zap,
  Calendar,
  Code,
  Users,
  Target,
  Download
} from 'lucide-react';

// 内容模型配置
const contentModels = [
  {
    id: 'blog',
    name: 'Blog Editor',
    description: 'Create and edit blog posts with rich content, author information, and SEO optimization.',
    icon: BookOpen,
    path: '/lab/blog-editor',
    color: 'bg-blue-500',
    features: ['Rich text editing', 'Author profiles', 'SEO settings', 'Reading time estimation'],
    status: 'stable'
  },
  {
    id: 'changelog',
    name: 'Changelog Editor',
    description: 'Document product updates, feature releases, and bug fixes with categorized changes.',
    icon: Zap,
    path: '/lab/changelog-editor',
    color: 'bg-green-500',
    features: ['Change categorization', 'Version tracking', 'Release notes', 'Impact assessment'],
    status: 'stable'
  },
  {
    id: 'documentation',
    name: 'Documentation Editor',
    description: 'Create comprehensive documentation with code examples, prerequisites, and related links.',
    icon: Code,
    path: '/lab/documentation-editor',
    color: 'bg-purple-500',
    features: ['Code highlighting', 'Prerequisites', 'Related docs', 'Difficulty levels'],
    status: 'stable'
  },
  {
    id: 'faq',
    name: 'FAQ Editor',
    description: 'Build frequently asked questions with search keywords, categories, and helpfulness tracking.',
    icon: HelpCircle,
    path: '/lab/faq-editor',
    color: 'bg-yellow-500',
    features: ['Search keywords', 'Priority levels', 'Helpfulness votes', 'Related questions'],
    status: 'new'
  },
  {
    id: 'product',
    name: 'Product Editor',
    description: 'Showcase products with features, pricing, specifications, and image galleries.',
    icon: Package,
    path: '/lab/product-editor',
    color: 'bg-indigo-500',
    features: ['Feature highlights', 'Pricing tiers', 'Image galleries', 'Technical specs'],
    status: 'new'
  },
  {
    id: 'announcement',
    name: 'Announcement Editor',
    description: 'Create important announcements with priority levels, target audiences, and notifications.',
    icon: Megaphone,
    path: '/lab/announcement-editor',
    color: 'bg-red-500',
    features: ['Priority levels', 'Target audiences', 'Banner display', 'Notification settings'],
    status: 'new'
  },
  {
    id: 'tutorial',
    name: 'Tutorial Editor',
    description: 'Build step-by-step tutorials with code examples, prerequisites, and progress tracking.',
    icon: GraduationCap,
    path: '/lab/tutorial-editor',
    color: 'bg-teal-500',
    features: ['Step-by-step guides', 'Code examples', 'Prerequisites', 'Progress tracking'],
    status: 'new'
  },
  {
    id: 'case-study',
    name: 'Case Study Editor',
    description: 'Document success stories with client information, challenges, solutions, and results.',
    icon: Target,
    path: '/lab/case-study-editor',
    color: 'bg-orange-500',
    features: ['Client profiles', 'Challenge analysis', 'Solution details', 'Results metrics'],
    status: 'new'
  },
  {
    id: 'whitepaper',
    name: 'Whitepaper Editor',
    description: 'Create comprehensive whitepapers with author information, abstracts, and download tracking.',
    icon: ScrollText,
    path: '/lab/whitepaper-editor',
    color: 'bg-gray-500',
    features: ['Author profiles', 'Abstract summaries', 'Download tracking', 'Access controls'],
    status: 'new'
  }
];

// 状态配置
const statusConfig = {
  stable: {
    label: 'Stable',
    color: 'bg-green-100 text-green-800 border-green-200',
    description: 'Production ready'
  },
  new: {
    label: 'New',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    description: 'Recently added'
  },
  beta: {
    label: 'Beta',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    description: 'Testing phase'
  }
};

const ContentModelsPage = () => {
  const stableModels = contentModels.filter(model => model.status === 'stable');
  const newModels = contentModels.filter(model => model.status === 'new');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-12 text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Content Model Editors
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Explore and test our comprehensive collection of content model editors. Each editor is designed 
            to handle specific content types with tailored fields, validation, and preview capabilities.
          </p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{contentModels.length}</div>
            <div className="text-gray-600 dark:text-gray-300">Total Content Models</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">{stableModels.length}</div>
            <div className="text-gray-600 dark:text-gray-300">Stable Models</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">{newModels.length}</div>
            <div className="text-gray-600 dark:text-gray-300">New Models</div>
          </div>
        </div>

        {/* 稳定版本模型 */}
        {stableModels.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <Users className="w-6 h-6 text-green-600" />
              Stable Content Models
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {stableModels.map((model) => {
                const Icon = model.icon;
                const status = statusConfig[model.status];
                
                return (
                  <Link key={model.id} href={model.path}>
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer">
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className={`w-12 h-12 ${model.color} rounded-lg flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${status.color}`}>
                            {status.label}
                          </span>
                        </div>
                        
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          {model.name}
                        </h3>
                        
                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                          {model.description}
                        </p>
                        
                        <div className="space-y-2 mb-4">
                          {model.features.slice(0, 2).map((feature, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                              {feature}
                            </div>
                          ))}
                          {model.features.length > 2 && (
                            <div className="text-xs text-gray-400">
                              +{model.features.length - 2} more features
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Try Editor
                          </span>
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        )}

        {/* 新增模型 */}
        {newModels.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <Zap className="w-6 h-6 text-blue-600" />
              New Content Models
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newModels.map((model) => {
                const Icon = model.icon;
                const status = statusConfig[model.status];
                
                return (
                  <Link key={model.id} href={model.path}>
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer">
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className={`w-12 h-12 ${model.color} rounded-lg flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${status.color}`}>
                            {status.label}
                          </span>
                        </div>
                        
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          {model.name}
                        </h3>
                        
                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                          {model.description}
                        </p>
                        
                        <div className="space-y-2 mb-4">
                          {model.features.slice(0, 2).map((feature, index) => (
                            <div key={index} className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                              <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                              {feature}
                            </div>
                          ))}
                          {model.features.length > 2 && (
                            <div className="text-xs text-gray-400">
                              +{model.features.length - 2} more features
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Try Editor
                          </span>
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        )}

        {/* 底部信息 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Need a Custom Content Model?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
            Our content model system is highly extensible. If you need a custom content type 
            that's not covered by the existing models, we can help you create one tailored 
            to your specific requirements.
          </p>
          <div className="flex items-center justify-center gap-4">
            <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Request Custom Model
            </button>
            <button className="px-6 py-3 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              View Documentation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentModelsPage;
