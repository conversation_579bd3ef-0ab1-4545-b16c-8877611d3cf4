/**
 * 软件应用编辑器测试页面
 * 
 * 用于测试 SoftwareApp 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import softwareAppConfig from '@/content-schema/SoftwareApp';
import { 
  Smartphone, 
  Edit, 
  Eye, 
  Star, 
  Download, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Lightbulb,
  ShoppingCart,
  Timer,
  Thermometer,
  Play,
  Monitor,
  Tablet,
  Laptop
} from 'lucide-react';

// 软件应用数据类型定义
interface Developer {
  name: string;
  website?: string;
  email?: string;
  support?: string;
}

interface Pricing {
  model: string;
  price: number;
  currency: string;
  billing: string;
  freeTrial: boolean;
  trialDays: number;
}

interface Feature {
  name: string;
  description: string;
  category: string;
  available: boolean;
}

interface SystemRequirements {
  web?: {
    browsers: string[];
    internetRequired: boolean;
  };
  mobile?: {
    ios: string;
    android: string;
    storage: string;
  };
  desktop?: {
    windows: string;
    macos: string;
    ram: string;
    storage: string;
  };
}

interface Screenshot {
  url: string;
  caption: string;
  alt: string;
  platform: string;
}

interface DownloadLink {
  platform: string;
  url: string;
  store: string;
  version: string;
}

interface Rating {
  average: number;
  count: number;
  distribution: {
    five: number;
    four: number;
    three: number;
    two: number;
    one: number;
  };
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface SoftwareAppData {
  title: string;
  slug: string;
  description: string;
  category: string;
  platform: string[];
  version: string;
  developer: Developer;
  pricing: Pricing;
  features: Feature[];
  systemRequirements: SystemRequirements;
  screenshots: Screenshot[];
  downloadLinks: DownloadLink[];
  rating: Rating;
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认软件应用数据
const defaultSoftwareAppData: SoftwareAppData = {
  title: "TaskMaster Pro - 智能任务管理工具",
  slug: "taskmaster-pro-task-management",
  description: "TaskMaster Pro是一款功能强大的智能任务管理工具，采用AI技术帮助用户高效管理日常工作和项目。支持团队协作、智能提醒、数据分析等功能，让工作更有条理，效率倍增。",
  category: "productivity",
  platform: ["web", "ios", "android"],
  version: "2.1.0",
  developer: {
    name: "TechFlow Studio",
    website: "https://techflow.studio",
    email: "<EMAIL>",
    support: "https://techflow.studio/support"
  },
  pricing: {
    model: "freemium",
    price: 99,
    currency: "CNY",
    billing: "monthly",
    freeTrial: true,
    trialDays: 14
  },
  features: [
    {
      name: "智能任务分配",
      description: "基于AI算法自动分配任务优先级和执行顺序",
      category: "core",
      available: true
    },
    {
      name: "团队协作",
      description: "支持多人实时协作，共享项目进度和文件",
      category: "core",
      available: true
    },
    {
      name: "数据分析报表",
      description: "生成详细的工作效率和项目进度分析报告",
      category: "advanced",
      available: true
    },
    {
      name: "API集成",
      description: "提供开放API，支持与第三方工具集成",
      category: "premium",
      available: true
    }
  ],
  systemRequirements: {
    web: {
      browsers: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"],
      internetRequired: true
    },
    mobile: {
      ios: "iOS 13.0+",
      android: "Android 8.0+",
      storage: "100MB"
    },
    desktop: {
      windows: "Windows 10+",
      macos: "macOS 10.15+",
      ram: "4GB RAM",
      storage: "500MB"
    }
  },
  screenshots: [
    {
      url: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop",
      caption: "TaskMaster Pro 主界面 - 任务管理面板",
      alt: "应用主界面截图",
      platform: "web"
    },
    {
      url: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop",
      caption: "团队协作功能 - 项目看板视图",
      alt: "团队协作界面截图",
      platform: "web"
    }
  ],
  downloadLinks: [
    {
      platform: "web",
      url: "https://taskmaster-pro.com",
      store: "official-website",
      version: "2.1.0"
    },
    {
      platform: "ios",
      url: "https://apps.apple.com/app/taskmaster-pro",
      store: "app-store",
      version: "2.1.0"
    },
    {
      platform: "android",
      url: "https://play.google.com/store/apps/details?id=com.taskmaster.pro",
      store: "google-play",
      version: "2.1.0"
    }
  ],
  rating: {
    average: 4.6,
    count: 1250,
    distribution: {
      five: 850,
      four: 280,
      three: 80,
      two: 25,
      one: 15
    }
  },
  tags: ["任务管理", "效率工具", "团队协作", "AI智能"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "TaskMaster Pro - 智能任务管理工具 | 提升工作效率的AI助手",
    metaDescription: "TaskMaster Pro是功能强大的智能任务管理工具，支持AI智能分配、团队协作、数据分析。免费试用14天，让工作更高效有序。",
    keywords: ["任务管理软件", "效率工具", "团队协作", "AI智能助手"]
  }
};

// 应用分类配置
const categoryConfig = {
  productivity: { label: '效率工具', icon: '📈', color: 'text-blue-600' },
  business: { label: '商务办公', icon: '💼', color: 'text-green-600' },
  education: { label: '教育学习', icon: '📚', color: 'text-purple-600' },
  entertainment: { label: '娱乐休闲', icon: '🎬', color: 'text-pink-600' },
  social: { label: '社交网络', icon: '👥', color: 'text-orange-600' },
  utilities: { label: '实用工具', icon: '🔧', color: 'text-gray-600' },
  health: { label: '健康医疗', icon: '🏥', color: 'text-red-600' },
  finance: { label: '金融理财', icon: '💰', color: 'text-yellow-600' },
  travel: { label: '旅行出行', icon: '✈️', color: 'text-indigo-600' },
  lifestyle: { label: '生活方式', icon: '🌟', color: 'text-teal-600' },
  games: { label: '游戏娱乐', icon: '🎮', color: 'text-purple-600' },
  'developer-tools': { label: '开发工具', icon: '⚙️', color: 'text-gray-600' },
  other: { label: '其他', icon: '📦', color: 'text-gray-600' }
} as const;

// 平台配置
const platformConfig = {
  web: { label: 'Web', icon: Globe, color: 'text-blue-600' },
  ios: { label: 'iOS', icon: Smartphone, color: 'text-gray-600' },
  android: { label: 'Android', icon: Smartphone, color: 'text-green-600' },
  windows: { label: 'Windows', icon: Monitor, color: 'text-blue-600' },
  macos: { label: 'macOS', icon: Laptop, color: 'text-gray-600' },
  linux: { label: 'Linux', icon: Monitor, color: 'text-orange-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const SoftwareAppEditorTestPage = () => {
  const [formData, setFormData] = useState<SoftwareAppData>(defaultSoftwareAppData);

  const handleSave = (data: SoftwareAppData) => {
    console.log('Saved software app data:', data);
    setFormData(data);
  };

  const categoryInfo = categoryConfig[formData.category as keyof typeof categoryConfig];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const renderStars = (rating: number) => {
    const stars = Math.round(rating);
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getPricingLabel = (model: string) => {
    const labels = {
      free: '💚 完全免费',
      freemium: '🆓 免费增值',
      paid: '💰 付费应用',
      subscription: '🔄 订阅制',
      'one-time': '💳 一次性付费'
    };
    return labels[model as keyof typeof labels] || model;
  };

  const getFeatureCategoryColor = (category: string) => {
    const colors = {
      core: 'bg-blue-100 text-blue-700 border-blue-200',
      advanced: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      premium: 'bg-orange-100 text-orange-700 border-orange-200',
      enterprise: 'bg-red-100 text-red-700 border-red-200'
    };
    return colors[category as keyof typeof colors] || colors.core;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Smartphone className="w-8 h-8 text-blue-600" />
            Software App Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试SoftwareApp内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              软件应用编辑器
            </h2>
            <BlockEditorShadcn
              config={softwareAppConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              应用预览
            </h2>
            
            <div className="space-y-6">
              {/* 应用头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${categoryInfo.color}`}>
                        {categoryInfo.icon} {categoryInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        v{formData.version}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 评分和开发者信息 */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {renderStars(formData.rating.average)}
                      </div>
                      <span className="text-lg font-bold text-gray-900 dark:text-white">
                        {formData.rating.average}
                      </span>
                      <span className="text-sm text-gray-500">
                        ({formData.rating.count.toLocaleString()} 评分)
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      开发者: {formData.developer.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {getPricingLabel(formData.pricing.model)}
                    </div>
                  </div>
                </div>

                {/* 支持平台 */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {formData.platform.map((platform, index) => {
                    const PlatformIcon = platformConfig[platform as keyof typeof platformConfig]?.icon || Monitor;
                    const platformInfo = platformConfig[platform as keyof typeof platformConfig];
                    return (
                      <span
                        key={index}
                        className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-gray-100 ${platformInfo?.color || 'text-gray-700'} border border-gray-200`}
                      >
                        <PlatformIcon className="w-4 h-4" />
                        {platformInfo?.label || platform}
                      </span>
                    );
                  })}
                </div>

                {/* 主要截图 */}
                {formData.screenshots.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.screenshots[0].url}
                      alt={formData.screenshots[0].alt}
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.screenshots[0].caption}
                    </p>
                  </div>
                )}
              </div>

              {/* 核心功能 */}
              {formData.features.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    核心功能
                  </h4>
                  <div className="space-y-3">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {feature.name}
                          </h5>
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded border ${getFeatureCategoryColor(feature.category)}`}>
                              {feature.category === 'core' ? '🔵 核心' :
                               feature.category === 'advanced' ? '🟡 高级' :
                               feature.category === 'premium' ? '🟠 付费' : '🔴 企业'}
                            </span>
                            {feature.available && (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 价格信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  价格信息
                </h4>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className="text-lg font-bold text-blue-900 dark:text-blue-300">
                        {getPricingLabel(formData.pricing.model)}
                      </div>
                      {formData.pricing.price > 0 && (
                        <div className="text-2xl font-bold text-blue-600">
                          ¥{formData.pricing.price}
                          <span className="text-sm font-normal text-blue-500">
                            /{formData.pricing.billing === 'monthly' ? '月' :
                              formData.pricing.billing === 'yearly' ? '年' :
                              formData.pricing.billing === 'one-time' ? '一次性' : '使用量'}
                          </span>
                        </div>
                      )}
                    </div>
                    {formData.pricing.freeTrial && (
                      <div className="text-right">
                        <div className="text-sm text-blue-700 dark:text-blue-300">
                          免费试用
                        </div>
                        <div className="text-lg font-bold text-blue-600">
                          {formData.pricing.trialDays} 天
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 下载链接 */}
              {formData.downloadLinks.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Download className="w-5 h-5" />
                    下载应用
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.downloadLinks.map((link, index) => {
                      const PlatformIcon = platformConfig[link.platform as keyof typeof platformConfig]?.icon || Monitor;
                      const platformInfo = platformConfig[link.platform as keyof typeof platformConfig];
                      return (
                        <a
                          key={index}
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <PlatformIcon className={`w-6 h-6 ${platformInfo?.color || 'text-gray-600'}`} />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 dark:text-white">
                              {platformInfo?.label || link.platform}
                            </div>
                            <div className="text-sm text-gray-500">
                              {link.store === 'app-store' ? 'App Store' :
                               link.store === 'google-play' ? 'Google Play' :
                               link.store === 'microsoft-store' ? 'Microsoft Store' :
                               link.store === 'official-website' ? '官方网站' :
                               link.store === 'github' ? 'GitHub' : '其他'}
                              {link.version && ` • v${link.version}`}
                            </div>
                          </div>
                          <Download className="w-4 h-4 text-gray-400" />
                        </a>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 应用标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    应用标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SoftwareAppEditorTestPage;
