/**
 * 职位发布编辑器测试页面
 * 
 * 用于测试 JobPosting 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import jobPostingConfig from '@/content-schema/JobPosting';
import { 
  Briefcase, 
  Edit, 
  Eye, 
  MapPin, 
  DollarSign, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  Building,
  Mail,
  Phone,
  ExternalLink,
  Clock,
  Award,
  GraduationCap,
  Target,
  Heart,
  Globe
} from 'lucide-react';

// 职位数据类型定义
interface Location {
  city: string;
  country: string;
  address?: string;
  allowRemote: boolean;
}

interface Salary {
  currency: string;
  minSalary?: number;
  maxSalary?: number;
  period: string;
  negotiable: boolean;
  showSalary: boolean;
}

interface Skill {
  skill: string;
  level: string;
  required: boolean;
  yearsOfExperience?: number;
}

interface Language {
  language: string;
  level: string;
  required: boolean;
}

interface Requirements {
  education: string;
  experience: {
    minYears: number;
    maxYears: number;
    required: boolean;
  };
  skills: Skill[];
  languages: Language[];
}

interface Benefit {
  category: string;
  title: string;
  description?: string;
}

interface Company {
  name: string;
  logo?: string;
  website?: string;
  industry?: string;
  size: string;
  description?: string;
  culture: string[];
}

interface Application {
  method: string;
  email?: string;
  applicationUrl?: string;
  phone?: string;
  instructions?: string;
  requiredDocuments: string[];
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface JobPostingData {
  title: string;
  slug: string;
  description: string;
  department: string;
  level: string;
  type: string;
  workMode: string;
  location: Location;
  salary?: Salary;
  requirements: Requirements;
  responsibilities: string[];
  benefits: Benefit[];
  company: Company;
  application: Application;
  status: string;
  priority: string;
  openings: number;
  publishDate: string;
  expiryDate: string;
  lastUpdated: string;
  tags: string[];
  seo: SEO;
}

// 默认职位数据
const defaultJobPostingData: JobPostingData = {
  title: "高级前端工程师 - React/TypeScript",
  slug: "senior-frontend-engineer-react-typescript",
  description: "我们正在寻找一位经验丰富的高级前端工程师加入我们的技术团队。你将负责开发和维护我们的Web应用程序，使用React、TypeScript等现代前端技术栈。我们提供灵活的工作环境、有竞争力的薪资待遇，以及良好的职业发展机会。",
  department: "技术部",
  level: "senior",
  type: "full-time",
  workMode: "hybrid",
  location: {
    city: "上海",
    country: "中国",
    address: "上海市浦东新区张江高科技园区",
    allowRemote: true
  },
  salary: {
    currency: "CNY",
    minSalary: 25000,
    maxSalary: 40000,
    period: "monthly",
    negotiable: false,
    showSalary: true
  },
  requirements: {
    education: "bachelor",
    experience: {
      minYears: 3,
      maxYears: 8,
      required: true
    },
    skills: [
      {
        skill: "React",
        level: "advanced",
        required: true,
        yearsOfExperience: 3
      },
      {
        skill: "TypeScript",
        level: "intermediate",
        required: true,
        yearsOfExperience: 2
      },
      {
        skill: "JavaScript",
        level: "advanced",
        required: true,
        yearsOfExperience: 5
      }
    ],
    languages: [
      {
        language: "chinese",
        level: "native",
        required: true
      },
      {
        language: "english",
        level: "business",
        required: false
      }
    ]
  },
  responsibilities: [
    "负责前端产品的架构设计和技术选型",
    "开发和维护高质量的Web应用程序",
    "与产品经理、设计师和后端工程师协作",
    "优化前端性能，提升用户体验",
    "参与代码审查，确保代码质量",
    "指导初级工程师，分享技术经验"
  ],
  benefits: [
    {
      category: "insurance",
      title: "五险一金",
      description: "完善的社会保险和住房公积金"
    },
    {
      category: "vacation",
      title: "带薪年假",
      description: "15天带薪年假，另有病假、婚假等"
    },
    {
      category: "bonus",
      title: "年终奖金",
      description: "根据个人和公司业绩发放年终奖"
    },
    {
      category: "training",
      title: "培训机会",
      description: "技术培训、会议参与、学习津贴"
    }
  ],
  company: {
    name: "TechCorp 科技有限公司",
    logo: "https://example.com/company-logo.png",
    website: "https://techcorp.com",
    industry: "互联网/软件",
    size: "medium",
    description: "TechCorp是一家专注于前沿技术的创新公司，致力于为客户提供优质的软件解决方案。我们拥有年轻有活力的团队，开放包容的企业文化，以及广阔的发展空间。",
    culture: ["创新", "协作", "成长", "开放"]
  },
  application: {
    method: "email",
    email: "<EMAIL>",
    instructions: "请发送简历至邮箱，邮件标题请注明应聘职位。我们会在3个工作日内回复。",
    requiredDocuments: ["个人简历", "作品集", "学历证明"]
  },
  status: "draft",
  priority: "normal",
  openings: 1,
  publishDate: new Date().toISOString().split('T')[0],
  expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  tags: ["前端开发", "React", "TypeScript", "远程工作"],
  seo: {
    metaTitle: "高级前端工程师招聘 - React/TypeScript | TechCorp",
    metaDescription: "TechCorp招聘高级前端工程师，要求React、TypeScript经验，提供有竞争力薪资和完善福利。支持远程工作，欢迎投递简历。",
    keywords: ["前端工程师招聘", "React开发", "TypeScript", "远程工作"]
  }
};

// 职位级别配置
const levelConfig = {
  intern: { label: '实习生', icon: '🎓', color: 'text-blue-600' },
  junior: { label: '初级', icon: '🌱', color: 'text-green-600' },
  mid: { label: '中级', icon: '🌿', color: 'text-yellow-600' },
  senior: { label: '高级', icon: '🌳', color: 'text-orange-600' },
  lead: { label: '技术负责人', icon: '👑', color: 'text-purple-600' },
  principal: { label: '首席', icon: '🏆', color: 'text-red-600' },
  director: { label: '总监', icon: '🎯', color: 'text-indigo-600' }
} as const;

// 工作类型配置
const typeConfig = {
  'full-time': { label: '全职', icon: '💼', color: 'text-blue-600' },
  'part-time': { label: '兼职', icon: '⏰', color: 'text-green-600' },
  contract: { label: '合同工', icon: '📝', color: 'text-purple-600' },
  freelance: { label: '自由职业', icon: '🆓', color: 'text-orange-600' },
  internship: { label: '实习', icon: '🎓', color: 'text-pink-600' }
} as const;

// 工作模式配置
const workModeConfig = {
  remote: { label: '远程工作', icon: Globe, color: 'text-blue-600' },
  onsite: { label: '现场办公', icon: Building, color: 'text-green-600' },
  hybrid: { label: '混合模式', icon: Users, color: 'text-purple-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  active: { label: '招聘中', color: 'text-green-700 bg-green-50 border-green-200' },
  paused: { label: '暂停', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' },
  filled: { label: '已招满', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  cancelled: { label: '已取消', color: 'text-red-700 bg-red-50 border-red-200' }
} as const;

// 优先级配置
const priorityConfig = {
  low: { label: '低优先级', color: 'text-green-600' },
  normal: { label: '普通', color: 'text-blue-600' },
  high: { label: '高优先级', color: 'text-orange-600' },
  urgent: { label: '紧急', color: 'text-red-600' }
} as const;

const JobPostingEditorTestPage = () => {
  const [formData, setFormData] = useState<JobPostingData>(defaultJobPostingData);

  const handleSave = (data: JobPostingData) => {
    console.log('Saved job posting data:', data);
    setFormData(data);
  };

  const levelInfo = levelConfig[formData.level as keyof typeof levelConfig];
  const typeInfo = typeConfig[formData.type as keyof typeof typeConfig];
  const WorkModeIcon = workModeConfig[formData.workMode as keyof typeof workModeConfig].icon;
  
  const formatSalary = () => {
    if (!formData.salary || !formData.salary.showSalary) return '薪资面议';
    
    const { minSalary, maxSalary, currency, period } = formData.salary;
    const currencySymbol = currency === 'CNY' ? '¥' : currency === 'USD' ? '$' : '€';
    const periodText = period === 'monthly' ? '/月' : period === 'yearly' ? '/年' : '/小时';
    
    if (minSalary && maxSalary) {
      return `${currencySymbol}${minSalary.toLocaleString()} - ${currencySymbol}${maxSalary.toLocaleString()}${periodText}`;
    } else if (minSalary) {
      return `${currencySymbol}${minSalary.toLocaleString()}+${periodText}`;
    }
    return '薪资面议';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Briefcase className="w-8 h-8 text-blue-600" />
            Job Posting Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试JobPosting内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              职位编辑器
            </h2>
            <BlockEditorShadcn
              config={jobPostingConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              职位预览
            </h2>
            
            <div className="space-y-6">
              {/* 职位头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${levelInfo.color}`}>
                        {levelInfo.icon} {levelInfo.label}
                      </span>
                      <span className={`text-sm ${typeInfo.color}`}>
                        {typeInfo.icon} {typeInfo.label}
                      </span>
                      <span className={`text-sm ${workModeConfig[formData.workMode as keyof typeof workModeConfig].color}`}>
                        <WorkModeIcon className="w-4 h-4 inline mr-1" />
                        {workModeConfig[formData.workMode as keyof typeof workModeConfig].label}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${priorityConfig[formData.priority as keyof typeof priorityConfig].color}`}>
                      {priorityConfig[formData.priority as keyof typeof priorityConfig].label}
                    </span>
                  </div>
                </div>

                {/* 职位基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{formatSalary()}</div>
                    <div className="text-xs text-gray-500">薪资待遇</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{formData.location.city}</div>
                    <div className="text-xs text-gray-500">工作地点</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">{formData.requirements.experience.minYears}-{formData.requirements.experience.maxYears}年</div>
                    <div className="text-xs text-gray-500">工作经验</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">{formData.openings}</div>
                    <div className="text-xs text-gray-500">招聘人数</div>
                  </div>
                </div>

                {/* 公司信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center gap-4">
                    {formData.company.logo && (
                      <img
                        src={formData.company.logo}
                        alt={formData.company.name}
                        className="w-16 h-8 object-contain bg-white rounded"
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-1">
                        {formData.company.name}
                      </h4>
                      <div className="text-sm text-blue-700 dark:text-blue-400">
                        {formData.company.industry} • {formData.department}
                      </div>
                    </div>
                    <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      立即申请
                    </button>
                  </div>
                </div>
              </div>

              {/* 工作职责 */}
              {formData.responsibilities.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    工作职责
                  </h4>
                  <ul className="space-y-2">
                    {formData.responsibilities.map((responsibility, index) => (
                      <li key={index} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        {responsibility}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 任职要求 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <GraduationCap className="w-5 h-5" />
                  任职要求
                </h4>
                <div className="space-y-4">
                  {/* 学历要求 */}
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <GraduationCap className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">学历要求</div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {formData.requirements.education === 'bachelor' ? '本科' :
                         formData.requirements.education === 'master' ? '硕士' :
                         formData.requirements.education === 'phd' ? '博士' :
                         formData.requirements.education === 'associate' ? '大专' :
                         formData.requirements.education === 'high-school' ? '高中' : '不限'}
                      </div>
                    </div>
                  </div>

                  {/* 工作经验 */}
                  <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Clock className="w-5 h-5 text-green-600" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">工作经验</div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {formData.requirements.experience.minYears}-{formData.requirements.experience.maxYears}年相关工作经验
                      </div>
                    </div>
                  </div>

                  {/* 技能要求 */}
                  {formData.requirements.skills.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">技能要求</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {formData.requirements.skills.map((skill, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {skill.skill}
                                {skill.required && <span className="text-red-500 ml-1">*</span>}
                              </span>
                              <div className="text-xs text-gray-500">
                                {skill.level === 'basic' ? '基础' :
                                 skill.level === 'intermediate' ? '中级' :
                                 skill.level === 'advanced' ? '高级' : '专家'}
                                {skill.yearsOfExperience && ` • ${skill.yearsOfExperience}年经验`}
                              </div>
                            </div>
                            <span className={`text-xs px-2 py-1 rounded ${skill.level === 'expert' ? 'bg-red-100 text-red-700' :
                              skill.level === 'advanced' ? 'bg-orange-100 text-orange-700' :
                              skill.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' : 'bg-green-100 text-green-700'}`}>
                              {skill.level === 'basic' ? '基础' :
                               skill.level === 'intermediate' ? '中级' :
                               skill.level === 'advanced' ? '高级' : '专家'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 语言要求 */}
                  {formData.requirements.languages.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">语言要求</h5>
                      <div className="flex flex-wrap gap-2">
                        {formData.requirements.languages.map((lang, index) => (
                          <span
                            key={index}
                            className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${lang.required ? 'bg-blue-100 text-blue-700 border border-blue-200' : 'bg-gray-100 text-gray-700 border border-gray-200'}`}
                          >
                            {lang.language === 'chinese' ? '中文' :
                             lang.language === 'english' ? '英语' :
                             lang.language === 'japanese' ? '日语' :
                             lang.language === 'korean' ? '韩语' : lang.language}
                            {lang.required && <span className="text-red-500 ml-1">*</span>}
                            <span className="ml-1 text-xs">
                              ({lang.level === 'native' ? '母语' :
                                lang.level === 'fluent' ? '流利' :
                                lang.level === 'business' ? '商务' :
                                lang.level === 'conversational' ? '日常' : '基础'})
                            </span>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 福利待遇 */}
              {formData.benefits.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Heart className="w-5 h-5" />
                    福利待遇
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <span className="text-lg">
                          {benefit.category === 'insurance' ? '🛡️' :
                           benefit.category === 'vacation' ? '🏖️' :
                           benefit.category === 'bonus' ? '💰' :
                           benefit.category === 'stock' ? '📈' :
                           benefit.category === 'training' ? '📚' :
                           benefit.category === 'equipment' ? '💻' :
                           benefit.category === 'wellness' ? '🏃' : '🎁'}
                        </span>
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {benefit.title}
                          </h5>
                          {benefit.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {benefit.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 公司详情 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  关于公司
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-3">
                    {formData.company.logo && (
                      <img
                        src={formData.company.logo}
                        alt={formData.company.name}
                        className="w-16 h-8 object-contain bg-white rounded"
                      />
                    )}
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {formData.company.name}
                      </h5>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {formData.company.industry} •
                        {formData.company.size === 'startup' ? ' 初创公司' :
                         formData.company.size === 'small' ? ' 小型公司' :
                         formData.company.size === 'medium' ? ' 中型公司' :
                         formData.company.size === 'large' ? ' 大型公司' : ' 企业集团'}
                      </div>
                      {formData.company.website && (
                        <a
                          href={formData.company.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          访问官网
                        </a>
                      )}
                    </div>
                  </div>

                  {formData.company.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {formData.company.description}
                    </p>
                  )}

                  {formData.company.culture.length > 0 && (
                    <div>
                      <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">企业文化</h6>
                      <div className="flex flex-wrap gap-2">
                        {formData.company.culture.map((culture, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                          >
                            {culture}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 申请方式 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  申请方式
                </h4>
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-green-700 dark:text-green-300 font-medium">申请方式:</span>
                      <span className="ml-2 text-green-800 dark:text-green-200">
                        {formData.application.method === 'email' ? '邮箱申请' :
                         formData.application.method === 'form' ? '在线表单' :
                         formData.application.method === 'external-link' ? '外部链接' : '电话联系'}
                      </span>
                    </div>
                    {formData.application.email && (
                      <div>
                        <span className="text-green-700 dark:text-green-300 font-medium">联系邮箱:</span>
                        <a
                          href={`mailto:${formData.application.email}`}
                          className="ml-2 text-green-800 dark:text-green-200 hover:underline"
                        >
                          {formData.application.email}
                        </a>
                      </div>
                    )}
                    {formData.application.phone && (
                      <div>
                        <span className="text-green-700 dark:text-green-300 font-medium">联系电话:</span>
                        <span className="ml-2 text-green-800 dark:text-green-200">
                          {formData.application.phone}
                        </span>
                      </div>
                    )}
                  </div>

                  {formData.application.instructions && (
                    <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-800">
                      <p className="text-sm text-green-700 dark:text-green-300">
                        {formData.application.instructions}
                      </p>
                    </div>
                  )}

                  {formData.application.requiredDocuments.length > 0 && (
                    <div className="mt-3">
                      <h6 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">所需材料</h6>
                      <div className="flex flex-wrap gap-2">
                        {formData.application.requiredDocuments.map((doc, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-700 border border-green-200 dark:bg-green-900/40 dark:text-green-300 dark:border-green-700"
                          >
                            {doc}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 职位信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 dark:text-white mb-2">职位信息</h5>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">发布日期:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {formatDate(formData.publishDate)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">截止日期:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {formatDate(formData.expiryDate)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">招聘人数:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {formData.openings} 人
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">最后更新:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {formatDate(formData.lastUpdated)}
                    </span>
                  </div>
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    职位标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobPostingEditorTestPage;
