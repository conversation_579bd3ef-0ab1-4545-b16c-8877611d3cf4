/**
 * 视频编辑器测试页面
 * 
 * 用于测试 Video 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import videoConfig from '@/content-schema/Video';
import { 
  Play, 
  Edit, 
  Eye, 
  Clock, 
  User, 
  Star,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  BarChart3,
  DollarSign,
  Globe,
  Lock,
  Users,
  ThumbsUp,
  MessageCircle,
  Share2,
  Award
} from 'lucide-react';

// 视频数据类型定义
interface Subtitle {
  language: string;
  url: string;
  label: string;
}

interface Chapter {
  title: string;
  startTime: number;
  endTime?: number;
  description?: string;
}

interface Quality {
  resolution: string;
  bitrate: number;
  frameRate: number;
  audioQuality: string;
}

interface Creator {
  name: string;
  avatar?: string;
  bio?: string;
  social?: {
    website?: string;
    youtube?: string;
    bilibili?: string;
    github?: string;
  };
}

interface Series {
  seriesId?: string;
  seriesTitle?: string;
  episodeNumber?: number;
  totalEpisodes?: number;
}

interface Accessibility {
  hasClosedCaptions: boolean;
  hasAudioDescription: boolean;
  hasSignLanguage: boolean;
  transcriptAvailable: boolean;
}

interface Engagement {
  viewCount: number;
  likeCount: number;
  dislikeCount: number;
  commentCount: number;
  shareCount: number;
  averageWatchTime: number;
  completionRate: number;
}

interface Monetization {
  isPaid: boolean;
  price: number;
  currency: string;
  allowAds: boolean;
  sponsorship?: {
    hasSponsor: boolean;
    sponsorName?: string;
    sponsorUrl?: string;
  };
}

interface Rating {
  average: number;
  count: number;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface VideoData {
  title: string;
  slug: string;
  description: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number;
  type: 'educational' | 'tutorial' | 'promotional' | 'documentary' | 'entertainment' | 'webinar' | 'demo';
  category: string;
  tags: string[];
  language: string;
  subtitles: Subtitle[];
  chapters: Chapter[];
  transcript?: string;
  quality?: Quality;
  creator: Creator;
  series?: Series;
  accessibility?: Accessibility;
  engagement?: Engagement;
  monetization?: Monetization;
  status: 'draft' | 'processing' | 'published' | 'private' | 'unlisted' | 'archived';
  visibility: 'public' | 'unlisted' | 'private' | 'members-only';
  publishDate: string;
  lastUpdated: string;
  expiryDate?: string;
  rating?: Rating;
  seo: SEO;
}

// 默认视频数据
const defaultVideoData: VideoData = {
  title: "JavaScript高级技巧：深入理解闭包和作用域",
  slug: "javascript-closures-and-scope",
  description: "深入探讨JavaScript中的闭包和作用域概念，通过实际案例和代码演示，帮助你彻底理解这些重要的编程概念。本视频适合有一定JavaScript基础的开发者，将帮助你提升代码质量和编程思维。",
  videoUrl: "https://example.com/videos/javascript-closures-scope.mp4",
  thumbnailUrl: "https://example.com/thumbnails/javascript-closures-scope.jpg",
  duration: 1800,
  type: "tutorial",
  category: "编程教程",
  tags: ["JavaScript", "闭包", "作用域", "前端开发"],
  language: "zh-CN",
  subtitles: [
    {
      language: "zh-CN",
      url: "https://example.com/subtitles/javascript-closures-zh.vtt",
      label: "中文"
    }
  ],
  chapters: [
    {
      title: "什么是闭包",
      startTime: 0,
      endTime: 300,
      description: "介绍闭包的基本概念和定义"
    },
    {
      title: "作用域链详解",
      startTime: 300,
      endTime: 900,
      description: "深入理解JavaScript的作用域链机制"
    },
    {
      title: "实际应用案例",
      startTime: 900,
      endTime: 1500,
      description: "通过实际代码案例演示闭包的应用"
    },
    {
      title: "常见问题和陷阱",
      startTime: 1500,
      endTime: 1800,
      description: "分析闭包使用中的常见问题和解决方案"
    }
  ],
  transcript: "# JavaScript闭包和作用域详解\n\n## 开场白\n大家好，欢迎来到今天的JavaScript高级技巧课程。今天我们要深入探讨两个非常重要的概念：闭包和作用域。\n\n## 什么是闭包？\n闭包是JavaScript中一个强大而又容易被误解的概念。简单来说，闭包就是函数和其词法环境的组合...\n\n## 作用域链\n在理解闭包之前，我们需要先理解作用域链的概念...\n\n## 实际应用\n让我们通过一些实际的代码例子来看看闭包是如何工作的...\n\n## 总结\n今天我们学习了闭包和作用域的核心概念，希望这些知识能帮助你写出更好的JavaScript代码。",
  quality: {
    resolution: "1080p",
    bitrate: 5000,
    frameRate: 30,
    audioQuality: "high"
  },
  creator: {
    name: "李老师",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    bio: "资深前端工程师，拥有10年Web开发经验，专注于JavaScript技术栈和现代前端框架。",
    social: {
      website: "https://example.com",
      github: "https://github.com/teacher-li"
    }
  },
  series: {
    seriesId: "javascript-advanced-series",
    seriesTitle: "JavaScript高级技巧系列",
    episodeNumber: 3,
    totalEpisodes: 10
  },
  accessibility: {
    hasClosedCaptions: true,
    hasAudioDescription: false,
    hasSignLanguage: false,
    transcriptAvailable: true
  },
  engagement: {
    viewCount: 0,
    likeCount: 0,
    dislikeCount: 0,
    commentCount: 0,
    shareCount: 0,
    averageWatchTime: 0,
    completionRate: 0
  },
  monetization: {
    isPaid: false,
    price: 0,
    currency: "CNY",
    allowAds: true,
    sponsorship: {
      hasSponsor: false
    }
  },
  status: "draft",
  visibility: "public",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  rating: {
    average: 0,
    count: 0
  },
  seo: {
    metaTitle: "JavaScript闭包和作用域详解 - 高级编程技巧视频教程",
    metaDescription: "深入学习JavaScript闭包和作用域概念，通过实际案例和代码演示，提升你的编程技能。适合有基础的前端开发者观看。",
    keywords: ["JavaScript视频", "闭包教程", "作用域详解", "前端开发"]
  }
};

// 视频类型配置
const typeConfig = {
  educational: { label: '教育内容', icon: '📚', color: 'text-blue-600' },
  tutorial: { label: '教程指南', icon: '🎯', color: 'text-green-600' },
  promotional: { label: '宣传推广', icon: '📢', color: 'text-purple-600' },
  documentary: { label: '纪录片', icon: '🎬', color: 'text-orange-600' },
  entertainment: { label: '娱乐内容', icon: '🎭', color: 'text-pink-600' },
  webinar: { label: '网络研讨会', icon: '💼', color: 'text-indigo-600' },
  demo: { label: '产品演示', icon: '🔧', color: 'text-gray-600' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  processing: { label: '处理中', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  private: { label: '私有', color: 'text-red-700 bg-red-50 border-red-200' },
  unlisted: { label: '不公开列出', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

// 可见性配置
const visibilityConfig = {
  public: { label: '公开', icon: Globe, color: 'text-green-600' },
  unlisted: { label: '不公开列出', icon: Hash, color: 'text-blue-600' },
  private: { label: '私有', icon: Lock, color: 'text-red-600' },
  'members-only': { label: '仅会员', icon: Users, color: 'text-purple-600' }
} as const;

const VideoEditorTestPage = () => {
  const [formData, setFormData] = useState<VideoData>(defaultVideoData);

  const handleSave = (data: VideoData) => {
    console.log('Saved video data:', data);
    setFormData(data);
  };

  const typeInfo = typeConfig[formData.type];
  const VisibilityIcon = visibilityConfig[formData.visibility].icon;
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Play className="w-8 h-8 text-blue-600" />
            Video Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Video内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              视频编辑器
            </h2>
            <BlockEditorShadcn
              config={videoConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              视频预览
            </h2>
            
            <div className="space-y-6">
              {/* 视频头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                {/* 视频缩略图 */}
                {formData.thumbnailUrl && (
                  <div className="mb-4">
                    <img
                      src={formData.thumbnailUrl}
                      alt={formData.title}
                      className="w-full rounded-lg shadow-md"
                    />
                  </div>
                )}

                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${typeInfo.color}`}>
                        {typeInfo.icon} {typeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDuration(formData.duration)}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status].color}`}>
                      {statusConfig[formData.status].label}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${visibilityConfig[formData.visibility].color}`}>
                      <VisibilityIcon className="w-3 h-3" />
                      {visibilityConfig[formData.visibility].label}
                    </span>
                  </div>
                </div>

                {/* 视频统计信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">{formData.engagement?.viewCount || 0}</div>
                    <div className="text-xs text-gray-500">观看次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">{formData.engagement?.likeCount || 0}</div>
                    <div className="text-xs text-gray-500">点赞数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">{formData.engagement?.commentCount || 0}</div>
                    <div className="text-xs text-gray-500">评论数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">{formData.engagement?.shareCount || 0}</div>
                    <div className="text-xs text-gray-500">分享次数</div>
                  </div>
                </div>
              </div>

              {/* 创作者信息 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <User className="w-5 h-5" />
                  创作者
                </h4>
                <div className="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  {formData.creator.avatar && (
                    <img
                      src={formData.creator.avatar}
                      alt={formData.creator.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900 dark:text-white">
                      {formData.creator.name}
                    </h5>
                    {formData.creator.bio && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {formData.creator.bio}
                      </p>
                    )}
                    {formData.creator.social && (
                      <div className="flex items-center gap-3 mt-2">
                        {formData.creator.social.website && (
                          <a href={formData.creator.social.website} className="text-blue-600 hover:text-blue-800 text-sm">
                            网站
                          </a>
                        )}
                        {formData.creator.social.github && (
                          <a href={formData.creator.social.github} className="text-blue-600 hover:text-blue-800 text-sm">
                            GitHub
                          </a>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 系列信息 */}
              {formData.series && formData.series.seriesTitle && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    视频系列
                  </h4>
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h5 className="font-medium text-blue-900 dark:text-blue-300 mb-2">
                      {formData.series.seriesTitle}
                    </h5>
                    <div className="text-sm text-blue-700 dark:text-blue-400">
                      第 {formData.series.episodeNumber} 集，共 {formData.series.totalEpisodes} 集
                    </div>
                  </div>
                </div>
              )}

              {/* 视频章节 */}
              {formData.chapters.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    视频章节
                  </h4>
                  <div className="space-y-2">
                    {formData.chapters.map((chapter, index) => (
                      <div key={index} className="flex items-center justify-between py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {chapter.title}
                          </h5>
                          {chapter.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {chapter.description}
                            </p>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 ml-4">
                          {formatDuration(chapter.startTime)}
                          {chapter.endTime && ` - ${formatDuration(chapter.endTime)}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 视频质量信息 */}
              {formData.quality && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    视频质量
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">{formData.quality.resolution}</div>
                      <div className="text-xs text-gray-500">分辨率</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-green-600">{formData.quality.bitrate}k</div>
                      <div className="text-xs text-gray-500">比特率</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">{formData.quality.frameRate}fps</div>
                      <div className="text-xs text-gray-500">帧率</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-lg font-bold text-orange-600">
                        {formData.quality.audioQuality === 'high' ? '高音质' :
                         formData.quality.audioQuality === 'lossless' ? '无损' : '标准'}
                      </div>
                      <div className="text-xs text-gray-500">音频</div>
                    </div>
                  </div>
                </div>
              )}

              {/* 无障碍功能 */}
              {formData.accessibility && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    无障碍功能
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className={`flex items-center gap-2 p-3 rounded-lg ${formData.accessibility.hasClosedCaptions ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-gray-50 text-gray-500 border border-gray-200'}`}>
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">字幕</span>
                    </div>
                    <div className={`flex items-center gap-2 p-3 rounded-lg ${formData.accessibility.transcriptAvailable ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-gray-50 text-gray-500 border border-gray-200'}`}>
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">文稿</span>
                    </div>
                    <div className={`flex items-center gap-2 p-3 rounded-lg ${formData.accessibility.hasAudioDescription ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-gray-50 text-gray-500 border border-gray-200'}`}>
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">音频描述</span>
                    </div>
                    <div className={`flex items-center gap-2 p-3 rounded-lg ${formData.accessibility.hasSignLanguage ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-gray-50 text-gray-500 border border-gray-200'}`}>
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">手语翻译</span>
                    </div>
                  </div>
                </div>
              )}

              {/* 变现设置 */}
              {formData.monetization && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    变现设置
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">付费视频:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formData.monetization.isPaid ? `是 (¥${formData.monetization.price})` : '否'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">允许广告:</span>
                        <span className="ml-2 text-gray-900 dark:text-white">
                          {formData.monetization.allowAds ? '是' : '否'}
                        </span>
                      </div>
                      {formData.monetization.sponsorship?.hasSponsor && (
                        <div className="col-span-2">
                          <span className="text-gray-500 dark:text-gray-400">赞助商:</span>
                          <span className="ml-2 text-gray-900 dark:text-white">
                            {formData.monetization.sponsorship.sponsorName}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* 互动数据 */}
              {formData.engagement && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    互动数据
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <ThumbsUp className="w-5 h-5 text-green-600" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{formData.engagement.likeCount}</div>
                        <div className="text-xs text-gray-500">点赞</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <MessageCircle className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{formData.engagement.commentCount}</div>
                        <div className="text-xs text-gray-500">评论</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <Share2 className="w-5 h-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{formData.engagement.shareCount}</div>
                        <div className="text-xs text-gray-500">分享</div>
                      </div>
                    </div>
                  </div>

                  {formData.engagement.completionRate > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-blue-700 dark:text-blue-300">完成率</span>
                        <span className="font-medium text-blue-900 dark:text-blue-200">
                          {formData.engagement.completionRate.toFixed(1)}%
                        </span>
                      </div>
                      <div className="mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${formData.engagement.completionRate}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 评分信息 */}
              {formData.rating && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-2">视频评分</h5>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center">
                      {renderStars(formData.rating.average)}
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.rating.average.toFixed(1)} ({formData.rating.count} 评价)
                    </span>
                  </div>
                </div>
              )}

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoEditorTestPage;
