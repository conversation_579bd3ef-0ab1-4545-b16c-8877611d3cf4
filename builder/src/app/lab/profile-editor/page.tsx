/**
 * 个人资料编辑器测试页面
 * 
 * 用于测试 Profile 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import profileConfig from '@/content-schema/Profile';
import { 
  User, 
  Edit, 
  Eye, 
  Star, 
  Clock, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  Globe,
  Lock,
  Image,
  Video,
  BarChart3,
  Lightbulb,
  ShoppingCart,
  Timer,
  Thermometer,
  Play,
  Monitor,
  Tablet,
  Laptop,
  Camera,
  Mic,
  MapPin,
  Mail,
  Phone,
  ExternalLink,
  Github,
  Linkedin,
  Twitter,
  MessageCircle,
  BookOpen,
  Youtube,
  Instagram,
  Trophy,
  GraduationCap,
  Briefcase,
  Code,
  Heart,
  Languages
} from 'lucide-react';

// 个人资料数据类型定义
interface PersonalInfo {
  fullName: string;
  displayName: string;
  title: string;
  avatar: string;
  location: string;
  email: string;
  phone?: string;
  website?: string;
  birthDate?: string;
  nationality: string;
}

interface Skill {
  name: string;
  category: string;
  level: string;
  years: number;
  description: string;
}

interface Experience {
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  location: string;
  description: string;
  achievements: string[];
  technologies: string[];
}

interface Education {
  institution: string;
  degree: string;
  major: string;
  startDate: string;
  endDate: string;
  gpa?: number;
  description: string;
  achievements: string[];
}

interface Project {
  name: string;
  description: string;
  role: string;
  startDate: string;
  endDate?: string;
  status: string;
  url?: string;
  github?: string;
  technologies: string[];
  highlights: string[];
}

interface Social {
  linkedin?: string;
  github?: string;
  twitter?: string;
  weibo?: string;
  wechat?: string;
  blog?: string;
  youtube?: string;
  instagram?: string;
}

interface Achievement {
  title: string;
  description: string;
  date: string;
  organization: string;
  category: string;
  url?: string;
}

interface Language {
  language: string;
  level: string;
  description: string;
}

interface Interest {
  name: string;
  description: string;
  category: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface ProfileData {
  title: string;
  slug: string;
  description: string;
  personalInfo: PersonalInfo;
  bio: string;
  skills: Skill[];
  experience: Experience[];
  education: Education[];
  projects: Project[];
  social: Social;
  achievements: Achievement[];
  languages: Language[];
  interests: Interest[];
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认个人资料数据
const defaultProfileData: ProfileData = {
  title: "张三 - 全栈开发工程师的个人主页",
  slug: "zhang-san-fullstack-developer",
  description: "我是张三，一名拥有5年经验的全栈开发工程师，专注于React、Node.js和云原生技术。热爱开源，致力于用技术创造价值，帮助团队构建高质量的产品。",
  personalInfo: {
    fullName: "张三",
    displayName: "Zhang San",
    title: "全栈开发工程师",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
    location: "北京，中国",
    email: "<EMAIL>",
    phone: "+86 138-0000-0000",
    website: "https://zhangsan.dev",
    nationality: "中国"
  },
  bio: "我是一名充满激情的全栈开发工程师，拥有5年的软件开发经验。专精于现代Web技术栈，包括React、Vue.js、Node.js、Python等。在我的职业生涯中，我参与了多个大型项目的开发，从初创公司的MVP产品到企业级应用系统。我相信技术的力量能够改变世界，致力于编写高质量、可维护的代码，并热衷于学习新技术和分享知识。",
  skills: [
    {
      name: "JavaScript",
      category: "programming",
      level: "expert",
      years: 5,
      description: "精通ES6+语法，熟悉异步编程和函数式编程"
    },
    {
      name: "React",
      category: "framework",
      level: "advanced",
      years: 4,
      description: "熟练使用React Hooks、Context API和状态管理"
    },
    {
      name: "Node.js",
      category: "programming",
      level: "advanced",
      years: 3,
      description: "擅长构建RESTful API和微服务架构"
    },
    {
      name: "团队协作",
      category: "soft-skills",
      level: "advanced",
      years: 5,
      description: "具备良好的沟通能力和团队合作精神"
    }
  ],
  experience: [
    {
      company: "TechFlow科技有限公司",
      position: "高级全栈开发工程师",
      startDate: "2022-03-01",
      current: true,
      location: "北京",
      description: "负责公司核心产品的前后端开发，参与系统架构设计和技术选型。",
      achievements: [
        "主导开发了新一代SaaS平台，用户量增长300%",
        "优化系统性能，页面加载速度提升50%",
        "建立了完善的CI/CD流程，部署效率提升80%"
      ],
      technologies: ["React", "Node.js", "TypeScript", "AWS", "Docker"]
    },
    {
      company: "创新软件公司",
      position: "前端开发工程师",
      startDate: "2020-06-01",
      endDate: "2022-02-28",
      current: false,
      location: "上海",
      description: "专注于前端开发，负责多个Web应用的开发和维护。",
      achievements: [
        "开发了公司官网和管理后台",
        "引入了现代化的前端工程化工具"
      ],
      technologies: ["Vue.js", "JavaScript", "Webpack", "Sass"]
    }
  ],
  education: [
    {
      institution: "北京理工大学",
      degree: "bachelor",
      major: "计算机科学与技术",
      startDate: "2016-09-01",
      endDate: "2020-06-30",
      gpa: 3.7,
      description: "主修计算机科学与技术，专注于软件工程和算法设计",
      achievements: [
        "获得校级优秀学生奖学金",
        "参与ACM程序设计竞赛获得省级二等奖"
      ]
    }
  ],
  projects: [
    {
      name: "智能任务管理系统",
      description: "基于AI的智能任务管理平台，帮助团队提高工作效率",
      role: "全栈开发工程师",
      startDate: "2023-01-01",
      endDate: "2023-06-30",
      status: "completed",
      url: "https://taskmaster.example.com",
      github: "https://github.com/zhangsan/taskmaster",
      technologies: ["React", "Node.js", "MongoDB", "OpenAI API"],
      highlights: [
        "集成了GPT-4 API实现智能任务分配",
        "支持实时协作和消息推送",
        "用户活跃度提升40%"
      ]
    }
  ],
  social: {
    linkedin: "https://linkedin.com/in/zhangsan",
    github: "https://github.com/zhangsan",
    twitter: "https://twitter.com/zhangsan_dev",
    blog: "https://zhangsan.dev/blog"
  },
  achievements: [
    {
      title: "年度最佳员工",
      description: "因在项目开发中的突出表现获得公司年度最佳员工奖",
      date: "2023-12-01",
      organization: "TechFlow科技有限公司",
      category: "award"
    }
  ],
  languages: [
    {
      language: "中文",
      level: "native",
      description: "母语"
    },
    {
      language: "英语",
      level: "fluent",
      description: "流利的听说读写能力，可进行技术交流"
    }
  ],
  interests: [
    {
      name: "开源贡献",
      description: "热衷于参与开源项目，贡献代码和文档",
      category: "technology"
    },
    {
      name: "摄影",
      description: "喜欢用镜头记录生活中的美好瞬间",
      category: "arts"
    }
  ],
  tags: ["全栈开发", "React专家", "Node.js", "开源贡献者"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "张三 - 全栈开发工程师 | 个人主页",
    metaDescription: "张三是一名经验丰富的全栈开发工程师，专注于React、Node.js和云原生技术。查看详细简历、项目作品和技能专长。",
    keywords: ["全栈开发工程师", "React开发", "Node.js", "个人简历"]
  }
};

// 技能等级配置
const skillLevelConfig = {
  beginner: { label: '初学者', color: 'text-green-600', icon: '🟢' },
  intermediate: { label: '中级', color: 'text-yellow-600', icon: '🟡' },
  advanced: { label: '高级', color: 'text-orange-600', icon: '🟠' },
  expert: { label: '专家', color: 'text-red-600', icon: '🔴' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const ProfileEditorTestPage = () => {
  const [formData, setFormData] = useState<ProfileData>(defaultProfileData);

  const handleSave = (data: ProfileData) => {
    console.log('Saved profile data:', data);
    setFormData(data);
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getSkillLevelInfo = (level: string) => {
    return skillLevelConfig[level as keyof typeof skillLevelConfig] || skillLevelConfig.intermediate;
  };

  const getSocialIcon = (platform: string) => {
    const icons = {
      linkedin: Linkedin,
      github: Github,
      twitter: Twitter,
      weibo: MessageCircle,
      wechat: MessageCircle,
      blog: BookOpen,
      youtube: Youtube,
      instagram: Instagram
    };
    return icons[platform as keyof typeof icons] || ExternalLink;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <User className="w-8 h-8 text-blue-600" />
            Profile Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Profile内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              个人资料编辑器
            </h2>
            <BlockEditorShadcn
              config={profileConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              个人资料预览
            </h2>
            
            <div className="space-y-6">
              {/* 个人基本信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start gap-6 mb-4">
                  <img
                    src={formData.personalInfo.avatar}
                    alt={formData.personalInfo.fullName}
                    className="w-24 h-24 rounded-full object-cover border-4 border-blue-100"
                  />
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {formData.personalInfo.fullName}
                    </h3>
                    {formData.personalInfo.displayName && (
                      <h4 className="text-lg text-gray-600 dark:text-gray-300 mb-2">
                        {formData.personalInfo.displayName}
                      </h4>
                    )}
                    <p className="text-blue-600 dark:text-blue-400 font-medium mb-3">
                      {formData.personalInfo.title}
                    </p>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-300">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {formData.personalInfo.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {formData.personalInfo.email}
                      </div>
                      {formData.personalInfo.phone && (
                        <div className="flex items-center gap-1">
                          <Phone className="w-4 h-4" />
                          {formData.personalInfo.phone}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 个人简介 */}
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {formData.bio}
                </p>

                {/* 社交媒体链接 */}
                <div className="flex flex-wrap gap-3">
                  {Object.entries(formData.social).map(([platform, url]) => {
                    if (!url) return null;
                    const IconComponent = getSocialIcon(platform);
                    return (
                      <a
                        key={platform}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        <IconComponent className="w-4 h-4" />
                        {platform}
                      </a>
                    );
                  })}
                </div>
              </div>

              {/* 技能专长 */}
              {formData.skills.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Code className="w-5 h-5" />
                    技能专长
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.skills.map((skill, index) => {
                      const levelInfo = getSkillLevelInfo(skill.level);
                      return (
                        <div key={index} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {skill.name}
                            </h5>
                            <span className={`text-xs px-2 py-1 rounded ${levelInfo.color} bg-opacity-10`}>
                              {levelInfo.icon} {levelInfo.label}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {skill.description}
                          </div>
                          <div className="text-xs text-gray-500">
                            {skill.years} 年经验
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 工作经历 */}
              {formData.experience.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Briefcase className="w-5 h-5" />
                    工作经历
                  </h4>
                  <div className="space-y-4">
                    {formData.experience.map((exp, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {exp.position}
                            </h5>
                            <div className="text-blue-600 dark:text-blue-400 font-medium">
                              {exp.company}
                            </div>
                            <div className="text-sm text-gray-500 flex items-center gap-2">
                              <span>{formatDate(exp.startDate)}</span>
                              <span>-</span>
                              <span>{exp.current ? '至今' : (exp.endDate ? formatDate(exp.endDate) : '')}</span>
                              <span>•</span>
                              <span>{exp.location}</span>
                            </div>
                          </div>
                          {exp.current && (
                            <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded">
                              当前工作
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                          {exp.description}
                        </p>
                        {exp.achievements.length > 0 && (
                          <div className="mb-3">
                            <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">主要成就</h6>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              {exp.achievements.map((achievement, idx) => (
                                <li key={idx} className="flex items-start gap-2">
                                  <span className="text-green-500 mt-1">•</span>
                                  {achievement}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {exp.technologies.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {exp.technologies.map((tech, idx) => (
                              <span
                                key={idx}
                                className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border border-blue-200 rounded"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 教育背景 */}
              {formData.education.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <GraduationCap className="w-5 h-5" />
                    教育背景
                  </h4>
                  <div className="space-y-4">
                    {formData.education.map((edu, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {edu.institution}
                            </h5>
                            <div className="text-blue-600 dark:text-blue-400">
                              {edu.degree === 'bachelor' ? '本科' :
                               edu.degree === 'master' ? '硕士' :
                               edu.degree === 'phd' ? '博士' : edu.degree} • {edu.major}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatDate(edu.startDate)} - {formatDate(edu.endDate)}
                            </div>
                          </div>
                          {edu.gpa && (
                            <span className="text-sm text-gray-600">
                              GPA: {edu.gpa}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                          {edu.description}
                        </p>
                        {edu.achievements.length > 0 && (
                          <div>
                            <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">学术成就</h6>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              {edu.achievements.map((achievement, idx) => (
                                <li key={idx} className="flex items-start gap-2">
                                  <span className="text-yellow-500 mt-1">★</span>
                                  {achievement}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 项目作品 */}
              {formData.projects.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    项目作品
                  </h4>
                  <div className="space-y-4">
                    {formData.projects.map((project, index) => (
                      <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {project.name}
                            </h5>
                            <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                              {project.role} • {formatDate(project.startDate)} - {project.endDate ? formatDate(project.endDate) : '进行中'}
                            </div>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded ${
                            project.status === 'completed' ? 'bg-green-100 text-green-700' :
                            project.status === 'in-progress' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {project.status === 'completed' ? '已完成' :
                             project.status === 'in-progress' ? '进行中' :
                             project.status === 'planning' ? '规划中' : project.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                          {project.description}
                        </p>
                        {project.highlights.length > 0 && (
                          <div className="mb-3">
                            <h6 className="text-sm font-medium text-gray-900 dark:text-white mb-2">项目亮点</h6>
                            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                              {project.highlights.map((highlight, idx) => (
                                <li key={idx} className="flex items-start gap-2">
                                  <span className="text-blue-500 mt-1">▶</span>
                                  {highlight}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        <div className="flex items-center gap-4 mb-3">
                          {project.url && (
                            <a
                              href={project.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
                            >
                              <ExternalLink className="w-3 h-3" />
                              查看项目
                            </a>
                          )}
                          {project.github && (
                            <a
                              href={project.github}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                            >
                              <Github className="w-3 h-3" />
                              源代码
                            </a>
                          )}
                        </div>
                        {project.technologies.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {project.technologies.map((tech, idx) => (
                              <span
                                key={idx}
                                className="text-xs px-2 py-1 bg-purple-50 text-purple-700 border border-purple-200 rounded"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 语言能力 */}
              {formData.languages.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Languages className="w-5 h-5" />
                    语言能力
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.languages.map((lang, index) => (
                      <div key={index} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {lang.language}
                          </h5>
                          <span className={`text-xs px-2 py-1 rounded ${
                            lang.level === 'native' ? 'bg-green-100 text-green-700' :
                            lang.level === 'fluent' ? 'bg-blue-100 text-blue-700' :
                            lang.level === 'conversational' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {lang.level === 'native' ? '🌟 母语' :
                             lang.level === 'fluent' ? '💬 流利' :
                             lang.level === 'conversational' ? '🗣️ 对话' : '📖 基础'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {lang.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 兴趣爱好 */}
              {formData.interests.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Heart className="w-5 h-5" />
                    兴趣爱好
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {formData.interests.map((interest, index) => (
                      <div key={index} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                          {interest.category === 'technology' ? '💻' :
                           interest.category === 'sports' ? '⚽' :
                           interest.category === 'arts' ? '🎨' :
                           interest.category === 'music' ? '🎵' :
                           interest.category === 'reading' ? '📚' :
                           interest.category === 'travel' ? '✈️' :
                           interest.category === 'gaming' ? '🎮' :
                           interest.category === 'cooking' ? '🍳' : '📦'} {interest.name}
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {interest.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 个人标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    个人标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileEditorTestPage;
