/**
 * 页面模版编辑器测试页面
 * 
 * 用于测试 PageTemplate 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import pageTemplateConfig from '@/content-schema/PageTemplate';
import { 
  FileText, 
  Edit, 
  Eye, 
  Star, 
  Clock, 
  Users,
  CheckCircle,
  AlertCircle,
  Hash,
  Calendar,
  DollarSign,
  Award,
  Target,
  User,
  Monitor,
  Tablet,
  Smartphone,
  Code,
  Palette,
  Zap,
  Shield,
  Settings,
  Download,
  ExternalLink,
  Play,
  Image,
  Layout,
  Layers,
  Package,
  Grid,
  Columns,
  Square
} from 'lucide-react';

// 页面模版数据类型定义
interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

interface Section {
  name: string;
  type: string;
  description: string;
  position: number;
  required: boolean;
  customizable: boolean;
}

interface Feature {
  name: string;
  description: string;
  category: string;
  included: boolean;
}

interface Performance {
  loadTime: string;
  lighthouseScore: number;
  mobileOptimized: boolean;
}

interface Technical {
  framework: string;
  cssFramework: string;
  responsive: boolean;
  browserSupport: string[];
  performance: Performance;
}

interface Customization {
  colorCustomizable: boolean;
  layoutCustomizable: boolean;
  contentEditable: boolean;
  sectionReorderable: boolean;
  customCSS: boolean;
}

interface Pricing {
  type: string;
  price: number;
  currency: string;
  license: string;
}

interface Demo {
  liveUrl?: string;
  previewImages?: string[];
}

interface TemplateImage {
  url: string;
  caption: string;
  alt: string;
  type: string;
  isPrimary: boolean;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface PageTemplateData {
  title: string;
  slug: string;
  description: string;
  pageType: string;
  category: string;
  layoutType: string;
  designStyle: string;
  sections: Section[];
  colorScheme: ColorScheme;
  features: Feature[];
  technical: Technical;
  customization: Customization;
  pricing: Pricing;
  demo?: Demo;
  images: TemplateImage[];
  tags: string[];
  status: string;
  publishDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认页面模版数据
const defaultPageTemplateData: PageTemplateData = {
  title: "现代企业首页模版 - 专业商务展示页面",
  slug: "modern-business-homepage-template",
  description: "这是一个现代化的企业首页模版，包含英雄区域、特性展示、服务介绍、客户评价等核心模块。采用响应式设计，支持多种布局变化和自定义选项。",
  pageType: "home",
  category: "business",
  layoutType: "hero-sections",
  designStyle: "modern",
  sections: [
    {
      name: "英雄区域",
      type: "hero",
      description: "页面顶部的主要展示区域，包含标题、副标题和行动按钮",
      position: 1,
      required: true,
      customizable: true
    },
    {
      name: "特性展示",
      type: "features",
      description: "展示产品或服务的核心特性和优势",
      position: 2,
      required: true,
      customizable: true
    },
    {
      name: "服务介绍",
      type: "services",
      description: "详细介绍提供的服务项目",
      position: 3,
      required: false,
      customizable: true
    },
    {
      name: "客户评价",
      type: "testimonials",
      description: "展示客户反馈和推荐",
      position: 4,
      required: false,
      customizable: true
    }
  ],
  colorScheme: {
    primary: "#3B82F6",
    secondary: "#10B981",
    accent: "#F59E0B",
    background: "#FFFFFF",
    text: "#1F2937"
  },
  features: [
    {
      name: "响应式设计",
      description: "自适应各种设备屏幕尺寸",
      category: "responsive",
      included: true
    },
    {
      name: "模块化结构",
      description: "可灵活组合和调整的模块化设计",
      category: "customization",
      included: true
    },
    {
      name: "SEO友好",
      description: "优化的HTML结构和元数据",
      category: "seo",
      included: true
    }
  ],
  technical: {
    framework: "react",
    cssFramework: "tailwind",
    responsive: true,
    browserSupport: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"],
    performance: {
      loadTime: "< 2秒",
      lighthouseScore: 95,
      mobileOptimized: true
    }
  },
  customization: {
    colorCustomizable: true,
    layoutCustomizable: true,
    contentEditable: true,
    sectionReorderable: true,
    customCSS: false
  },
  pricing: {
    type: "premium",
    price: 99,
    currency: "CNY",
    license: "single-use"
  },
  demo: {
    liveUrl: "https://demo.example.com/homepage",
    previewImages: [
      "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
      "https://images.unsplash.com/photo-**********-87deedd944c3?w=400&h=300&fit=crop"
    ]
  },
  images: [
    {
      url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=800&fit=crop",
      caption: "现代企业首页模版 - 桌面端完整展示",
      alt: "页面模版桌面端截图",
      type: "desktop",
      isPrimary: true
    }
  ],
  tags: ["首页", "企业", "现代", "响应式", "模块化"],
  status: "draft",
  publishDate: new Date().toISOString(),
  lastUpdated: new Date().toISOString(),
  seo: {
    metaTitle: "现代企业首页模版 - 专业商务展示页面 | 页面模版",
    metaDescription: "专业的企业首页模版，包含英雄区域、特性展示、服务介绍等核心模块，响应式设计，支持自定义。",
    keywords: ["首页模版", "企业页面", "响应式设计", "页面模版"]
  }
};

// 页面类型配置
const pageTypeConfig = {
  home: { label: '首页', icon: '🏠', color: 'text-blue-600' },
  about: { label: '关于页面', icon: 'ℹ️', color: 'text-green-600' },
  services: { label: '服务页面', icon: '🛠️', color: 'text-purple-600' },
  portfolio: { label: '作品集页面', icon: '🎨', color: 'text-pink-600' },
  blog: { label: '博客页面', icon: '📝', color: 'text-orange-600' },
  contact: { label: '联系页面', icon: '📞', color: 'text-cyan-600' },
  pricing: { label: '价格页面', icon: '💰', color: 'text-yellow-600' },
  team: { label: '团队页面', icon: '👥', color: 'text-indigo-600' },
  testimonials: { label: '推荐页面', icon: '💬', color: 'text-emerald-600' },
  faq: { label: '常见问题页面', icon: '❓', color: 'text-red-600' },
  privacy: { label: '隐私政策页面', icon: '🔒', color: 'text-gray-600' },
  terms: { label: '服务条款页面', icon: '📋', color: 'text-slate-600' },
  landing: { label: '落地页', icon: '🚀', color: 'text-violet-600' },
  product: { label: '产品页面', icon: '📦', color: 'text-teal-600' },
  other: { label: '其他页面', icon: '📄', color: 'text-gray-600' }
} as const;

// 布局类型配置
const layoutTypeConfig = {
  'single-column': { label: '单列布局', icon: '📄' },
  'two-column': { label: '双列布局', icon: '📰' },
  'three-column': { label: '三列布局', icon: '📊' },
  'grid': { label: '网格布局', icon: '⚏' },
  'masonry': { label: '瀑布流布局', icon: '🧱' },
  'hero-sections': { label: '英雄模块布局', icon: '🎯' },
  'full-width': { label: '全宽布局', icon: '📐' },
  'sidebar': { label: '侧边栏布局', icon: '📋' },
  'other': { label: '其他布局', icon: '📦' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  review: { label: '审核中', color: 'text-blue-700 bg-blue-50 border-blue-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const PageTemplateEditorTestPage = () => {
  const [formData, setFormData] = useState<PageTemplateData>(defaultPageTemplateData);

  const handleSave = (data: PageTemplateData) => {
    console.log('Saved page template data:', data);
    setFormData(data);
  };

  const pageTypeInfo = pageTypeConfig[formData.pageType as keyof typeof pageTypeConfig];
  const layoutTypeInfo = layoutTypeConfig[formData.layoutType as keyof typeof layoutTypeConfig];
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getSectionIcon = (type: string) => {
    const icons = {
      hero: Target,
      features: Star,
      services: Settings,
      testimonials: Users,
      team: User,
      pricing: DollarSign,
      cta: Zap,
      contact: ExternalLink,
      gallery: Image,
      blog: FileText,
      stats: Award,
      faq: AlertCircle,
      newsletter: ExternalLink,
      footer: Square,
      other: Package
    };
    return icons[type as keyof typeof icons] || Package;
  };

  const getFrameworkLabel = (framework: string) => {
    const labels = {
      react: '⚛️ React',
      vue: '💚 Vue.js',
      angular: '🅰️ Angular',
      'html-css': '🌐 HTML/CSS',
      wordpress: '📝 WordPress',
      nextjs: '▲ Next.js',
      nuxtjs: '💚 Nuxt.js',
      gatsby: '🚀 Gatsby',
      other: '📦 其他'
    };
    return labels[framework as keyof typeof labels] || framework;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <FileText className="w-8 h-8 text-blue-600" />
            Page Template Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试PageTemplate内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              页面模版编辑器
            </h2>
            <BlockEditorShadcn
              config={pageTemplateConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              页面模版预览
            </h2>
            
            <div className="space-y-6">
              {/* 模版头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${pageTypeInfo.color}`}>
                        {pageTypeInfo.icon} {pageTypeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        {layoutTypeInfo.icon} {layoutTypeInfo.label}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status as keyof typeof statusConfig].color}`}>
                      {statusConfig[formData.status as keyof typeof statusConfig].label}
                    </span>
                  </div>
                </div>

                {/* 基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                      <DollarSign className="w-4 h-4" />
                      ¥{formData.pricing.price}
                    </div>
                    <div className="text-xs text-gray-500">{formData.pricing.type === 'free' ? '免费' : '付费'}</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-green-600 flex items-center justify-center gap-1">
                      <Layers className="w-4 h-4" />
                      {formData.sections.length}
                    </div>
                    <div className="text-xs text-gray-500">页面模块</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-orange-600 flex items-center justify-center gap-1">
                      <Code className="w-4 h-4" />
                      {getFrameworkLabel(formData.technical.framework).split(' ')[1]}
                    </div>
                    <div className="text-xs text-gray-500">技术框架</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 flex items-center justify-center gap-1">
                      <Zap className="w-4 h-4" />
                      {formData.technical.performance.lighthouseScore}
                    </div>
                    <div className="text-xs text-gray-500">性能评分</div>
                  </div>
                </div>

                {/* 主要图片 */}
                {formData.images.length > 0 && (
                  <div className="mb-4">
                    <img
                      src={formData.images[0].url}
                      alt={formData.images[0].alt}
                      className="w-full h-48 object-cover rounded-lg shadow-md"
                    />
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 text-center">
                      {formData.images[0].caption}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageTemplateEditorTestPage;
