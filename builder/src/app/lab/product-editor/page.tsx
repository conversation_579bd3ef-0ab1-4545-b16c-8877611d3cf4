/**
 * 产品编辑器测试页面
 * 
 * 用于测试 Product 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import productConfig from '@/content-schema/Product';
import { 
  Package, 
  Edit, 
  Eye, 
  Star, 
  DollarSign, 
  Calendar, 
  Tag, 
  Image as ImageIcon,
  Settings,
  CheckCircle,
  Clock,
  AlertCircle,
  Rocket,
  Hash
} from 'lucide-react';

// 产品数据类型定义
interface Feature {
  name: string;
  description: string;
  icon: string;
}

interface Pricing {
  currency: string;
  basePrice: number;
  billingCycle: string;
  discountPrice?: number;
  hasFreeTrial: boolean;
  trialDays?: number;
}

interface ProductImage {
  url: string;
  alt: string;
  caption?: string;
  isPrimary: boolean;
}

interface Specification {
  name: string;
  value: string;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface ProductData {
  name: string;
  slug: string;
  shortDescription: string;
  description: string;
  category: string;
  features: Feature[];
  pricing: Pricing;
  images: ProductImage[];
  specifications: Specification[];
  tags: string[];
  status: 'draft' | 'active' | 'discontinued' | 'coming-soon';
  launchDate: string;
  lastUpdated: string;
  seo: SEO;
}

// 默认产品数据
const defaultProductData: ProductData = {
  name: "Premium Analytics Dashboard",
  slug: "premium-analytics-dashboard",
  shortDescription: "Powerful analytics dashboard with real-time insights and customizable reports for data-driven decision making.",
  description: "# Premium Analytics Dashboard\n\nOur Premium Analytics Dashboard provides comprehensive insights into your business performance with real-time data visualization, customizable reports, and advanced analytics features.\n\n## Key Benefits\n\n- **Real-time Data**: Get instant insights as your data updates\n- **Custom Reports**: Create tailored reports for your specific needs\n- **Advanced Analytics**: Leverage machine learning for predictive insights\n- **Team Collaboration**: Share insights and collaborate with your team",
  category: "Analytics & Reporting",
  features: [
    {
      name: "Real-time Analytics",
      description: "Monitor your key metrics in real-time with live data updates and instant notifications.",
      icon: "Activity"
    },
    {
      name: "Custom Dashboards",
      description: "Create personalized dashboards with drag-and-drop widgets tailored to your needs.",
      icon: "Layout"
    },
    {
      name: "Advanced Reporting",
      description: "Generate comprehensive reports with automated scheduling and export options.",
      icon: "FileText"
    }
  ],
  pricing: {
    currency: "USD",
    basePrice: 99.99,
    billingCycle: "monthly",
    discountPrice: 79.99,
    hasFreeTrial: true,
    trialDays: 14
  },
  images: [
    {
      url: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600",
      alt: "Analytics dashboard showing various charts and metrics",
      caption: "Main dashboard view with real-time analytics",
      isPrimary: true
    }
  ],
  specifications: [
    {
      name: "Data Sources",
      value: "50+ integrations"
    },
    {
      name: "Data Retention",
      value: "2 years"
    },
    {
      name: "API Rate Limit",
      value: "10,000 requests/hour"
    },
    {
      name: "Export Formats",
      value: "PDF, Excel, CSV, JSON"
    }
  ],
  tags: ["analytics", "dashboard", "reporting", "business-intelligence"],
  status: "active",
  launchDate: new Date().toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  seo: {
    metaTitle: "Premium Analytics Dashboard - Advanced Business Intelligence Tool",
    metaDescription: "Transform your data into actionable insights with our Premium Analytics Dashboard. Real-time analytics, custom reports, and advanced features.",
    keywords: ["analytics dashboard", "business intelligence", "data visualization", "reporting tool"]
  }
};

// 状态配置
const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'text-gray-700 bg-gray-50 border-gray-200',
    icon: Edit
  },
  active: { 
    label: 'Active', 
    color: 'text-green-700 bg-green-50 border-green-200',
    icon: CheckCircle
  },
  discontinued: { 
    label: 'Discontinued', 
    color: 'text-red-700 bg-red-50 border-red-200',
    icon: AlertCircle
  },
  'coming-soon': { 
    label: 'Coming Soon', 
    color: 'text-blue-700 bg-blue-50 border-blue-200',
    icon: Rocket
  }
} as const;

const ProductEditorTestPage = () => {
  const [formData, setFormData] = useState<ProductData>(defaultProductData);

  const handleSave = (data: ProductData) => {
    console.log('Saved product data:', data);
    setFormData(data);
  };

  const StatusIcon = statusConfig[formData.status].icon;
  const primaryImage = formData.images.find(img => img.isPrimary) || formData.images[0];
  const hasDiscount = formData.pricing.discountPrice && formData.pricing.discountPrice < formData.pricing.basePrice;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Package className="w-8 h-8 text-blue-600" />
            Product Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the Product content model form functionality and preview the rendered output.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              Product Editor
            </h2>
            <BlockEditorShadcn
              config={productConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Product Preview
            </h2>
            
            <div className="space-y-6">
              {/* 产品头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                {/* 主图 */}
                {primaryImage && (
                  <div className="mb-4">
                    <img
                      src={primaryImage.url}
                      alt={primaryImage.alt}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    {primaryImage.caption && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        {primaryImage.caption}
                      </p>
                    )}
                  </div>
                )}

                {/* 产品标题和状态 */}
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formData.name}
                  </h3>
                  <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${statusConfig[formData.status].color}`}>
                    <StatusIcon className="w-4 h-4" />
                    {statusConfig[formData.status].label}
                  </span>
                </div>

                {/* 短描述 */}
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {formData.shortDescription}
                </p>

                {/* 价格信息 */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-green-600" />
                    <div className="flex items-center gap-2">
                      {hasDiscount && (
                        <span className="text-lg font-bold text-green-600">
                          {formData.pricing.currency} {formData.pricing.discountPrice}
                        </span>
                      )}
                      <span className={`text-lg ${hasDiscount ? 'line-through text-gray-500' : 'font-bold text-green-600'}`}>
                        {formData.pricing.currency} {formData.pricing.basePrice}
                      </span>
                      <span className="text-sm text-gray-500">
                        /{formData.pricing.billingCycle}
                      </span>
                    </div>
                  </div>
                  {formData.pricing.hasFreeTrial && (
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800">
                      {formData.pricing.trialDays} days free trial
                    </span>
                  )}
                </div>

                {/* 元信息 */}
                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {formData.category}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Launched {formData.launchDate}
                  </span>
                </div>
              </div>

              {/* 产品描述 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Description
                </h4>
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown>{formData.description}</ReactMarkdown>
                </div>
              </div>

              {/* 主要特性 */}
              {formData.features.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    Key Features
                  </h4>
                  <div className="space-y-3">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                          {feature.name}
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 技术规格 */}
              {formData.specifications.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Specifications
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {formData.specifications.map((spec, index) => (
                      <div key={index} className="flex justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {spec.name}
                        </span>
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {spec.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 产品标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 其他图片 */}
              {formData.images.length > 1 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <ImageIcon className="w-4 h-4" />
                    Additional Images
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {formData.images.filter(img => !img.isPrimary).map((image, index) => (
                      <div key={index}>
                        <img
                          src={image.url}
                          alt={image.alt}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        {image.caption && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {image.caption}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO Preview
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductEditorTestPage;
