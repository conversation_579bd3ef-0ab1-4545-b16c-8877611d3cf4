/**
 * 课时编辑器测试页面
 * 
 * 用于测试 Lesson 内容模型的表单功能
 */

'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import lessonConfig from '@/content-schema/Lesson';
import { 
  PlayCircle, 
  Edit, 
  Eye, 
  Clock, 
  BookOpen, 
  CheckCircle,
  AlertCircle,
  Target,
  FileText,
  Hash,
  Star,
  Users,
  Calendar,
  BarChart3,
  Award
} from 'lucide-react';

// 课时数据类型定义
interface Resource {
  title: string;
  type: 'video' | 'document' | 'code' | 'link' | 'download';
  url: string;
  description?: string;
  duration?: number;
  isRequired: boolean;
}

interface Exercise {
  question: string;
  type: 'multiple-choice' | 'true-false' | 'coding' | 'short-answer';
  options?: string[];
  correctAnswer: string;
  explanation?: string;
  points: number;
}

interface Subtitle {
  language: string;
  url: string;
}

interface Chapter {
  title: string;
  startTime: number;
  endTime?: number;
}

interface VideoInfo {
  videoUrl?: string;
  thumbnailUrl?: string;
  subtitles: Subtitle[];
  chapters: Chapter[];
}

interface CompletionCriteria {
  watchPercentage: number;
  exerciseScore: number;
  requireAllResources: boolean;
}

interface Rating {
  average: number;
  count: number;
}

interface SEO {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

interface LessonData {
  title: string;
  slug: string;
  description: string;
  courseId: string;
  moduleId: string;
  order: number;
  type: 'video' | 'text' | 'quiz' | 'assignment' | 'live' | 'interactive';
  content: string;
  duration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  learningObjectives: string[];
  prerequisites: string[];
  resources: Resource[];
  exercises: Exercise[];
  videoInfo?: VideoInfo;
  completionCriteria?: CompletionCriteria;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  isFree: boolean;
  publishDate: string;
  lastUpdated: string;
  viewCount: number;
  completionRate: number;
  rating: Rating;
  seo: SEO;
}

// 默认课时数据
const defaultLessonData: LessonData = {
  title: "React组件基础：创建你的第一个组件",
  slug: "react-components-basics",
  description: "在这节课中，你将学习React组件的基本概念，了解函数组件和类组件的区别，并动手创建你的第一个React组件。通过实际编码练习，掌握组件的创建、使用和基本属性传递。",
  courseId: "react-fullstack-development",
  moduleId: "react-basics",
  order: 1,
  type: "video",
  content: "# React组件基础\n\n## 什么是React组件？\n\nReact组件是构建用户界面的基本单元。每个组件都是一个独立的、可复用的代码片段，它接收输入（称为props）并返回描述UI外观的React元素。\n\n## 函数组件 vs 类组件\n\n### 函数组件\n```jsx\nfunction Welcome(props) {\n  return <h1>Hello, {props.name}!</h1>;\n}\n```\n\n### 类组件\n```jsx\nclass Welcome extends React.Component {\n  render() {\n    return <h1>Hello, {this.props.name}!</h1>;\n  }\n}\n```\n\n## 实践练习\n\n现在让我们创建一个简单的用户卡片组件：\n\n```jsx\nfunction UserCard({ name, email, avatar }) {\n  return (\n    <div className=\"user-card\">\n      <img src={avatar} alt={name} />\n      <h3>{name}</h3>\n      <p>{email}</p>\n    </div>\n  );\n}\n```\n\n## 小结\n\n在这节课中，我们学习了：\n- React组件的基本概念\n- 函数组件和类组件的语法\n- 如何创建和使用组件\n- Props的基本用法\n\n下一节课，我们将深入学习组件的状态管理。",
  duration: 25,
  difficulty: "beginner",
  learningObjectives: [
    "理解React组件的基本概念和作用",
    "掌握函数组件和类组件的语法差异",
    "能够创建简单的React组件",
    "学会使用props传递数据"
  ],
  prerequisites: [
    "JavaScript ES6基础语法",
    "HTML和CSS基础知识"
  ],
  resources: [
    {
      title: "React官方文档 - 组件",
      type: "link",
      url: "https://react.dev/learn/your-first-component",
      description: "React官方文档中关于组件的详细说明",
      isRequired: false
    },
    {
      title: "课时练习代码",
      type: "code",
      url: "https://github.com/example/react-components-exercise",
      description: "本课时的练习代码和示例",
      isRequired: true
    }
  ],
  exercises: [
    {
      question: "以下哪个是创建React函数组件的正确语法？",
      type: "multiple-choice",
      options: [
        "function MyComponent() { return <div>Hello</div>; }",
        "const MyComponent = () => { return <div>Hello</div>; }",
        "class MyComponent extends Component { render() { return <div>Hello</div>; } }",
        "以上都正确"
      ],
      correctAnswer: "以上都正确",
      explanation: "React支持多种创建组件的方式，包括函数声明、箭头函数和类组件。",
      points: 10
    }
  ],
  videoInfo: {
    videoUrl: "https://example.com/videos/react-components-basics.mp4",
    thumbnailUrl: "https://example.com/thumbnails/react-components-basics.jpg",
    subtitles: [
      {
        language: "zh-CN",
        url: "https://example.com/subtitles/react-components-basics-zh.vtt"
      }
    ],
    chapters: [
      {
        title: "课程介绍",
        startTime: 0,
        endTime: 120
      },
      {
        title: "组件基础概念",
        startTime: 120,
        endTime: 600
      },
      {
        title: "实践练习",
        startTime: 600,
        endTime: 1200
      }
    ]
  },
  completionCriteria: {
    watchPercentage: 80,
    exerciseScore: 70,
    requireAllResources: true
  },
  tags: ["React", "组件", "基础", "前端"],
  status: "draft",
  isFree: false,
  publishDate: new Date().toISOString().split('T')[0],
  lastUpdated: new Date().toISOString().split('T')[0],
  viewCount: 0,
  completionRate: 0,
  rating: {
    average: 0,
    count: 0
  },
  seo: {
    metaTitle: "React组件基础：创建你的第一个组件 - 在线课程",
    metaDescription: "学习React组件的基本概念，掌握函数组件和类组件的创建方法。通过实际练习，快速入门React组件开发。",
    keywords: ["React组件", "函数组件", "类组件", "前端开发"]
  }
};

// 课时类型配置
const typeConfig = {
  video: { label: '视频课程', icon: '📹', color: 'text-blue-600' },
  text: { label: '图文教程', icon: '📄', color: 'text-green-600' },
  quiz: { label: '测验练习', icon: '📝', color: 'text-purple-600' },
  assignment: { label: '作业任务', icon: '📋', color: 'text-orange-600' },
  live: { label: '直播课程', icon: '🎥', color: 'text-red-600' },
  interactive: { label: '互动练习', icon: '🎮', color: 'text-pink-600' }
} as const;

// 难度级别配置
const difficultyConfig = {
  beginner: { label: '初级', color: 'text-green-700 bg-green-50 border-green-200', icon: '🟢' },
  intermediate: { label: '中级', color: 'text-yellow-700 bg-yellow-50 border-yellow-200', icon: '🟡' },
  advanced: { label: '高级', color: 'text-red-700 bg-red-50 border-red-200', icon: '🟠' }
} as const;

// 状态配置
const statusConfig = {
  draft: { label: '草稿', color: 'text-gray-700 bg-gray-50 border-gray-200' },
  published: { label: '已发布', color: 'text-green-700 bg-green-50 border-green-200' },
  archived: { label: '已归档', color: 'text-gray-700 bg-gray-50 border-gray-200' }
} as const;

const LessonEditorTestPage = () => {
  const [formData, setFormData] = useState<LessonData>(defaultLessonData);

  const handleSave = (data: LessonData) => {
    console.log('Saved lesson data:', data);
    setFormData(data);
  };

  const typeInfo = typeConfig[formData.type];
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <PlayCircle className="w-8 h-8 text-blue-600" />
            Lesson Editor Test
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            测试Lesson内容模型的表单功能和预览效果。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 编辑器面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Edit className="w-5 h-5" />
              课时编辑器
            </h2>
            <BlockEditorShadcn
              config={lessonConfig}
              initialData={formData}
              onSave={handleSave}
            />
          </div>

          {/* 预览面板 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              课时预览
            </h2>
            
            <div className="space-y-6">
              {/* 课时头部信息 */}
              <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`text-sm ${typeInfo.color}`}>
                        {typeInfo.icon} {typeInfo.label}
                      </span>
                      <span className="text-sm text-gray-500">
                        第 {formData.order} 课时
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {formData.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      {formData.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${difficultyConfig[formData.difficulty].color}`}>
                      <span>{difficultyConfig[formData.difficulty].icon}</span>
                      {difficultyConfig[formData.difficulty].label}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${statusConfig[formData.status].color}`}>
                      {statusConfig[formData.status].label}
                    </span>
                    {formData.isFree && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                        免费试听
                      </span>
                    )}
                  </div>
                </div>

                {/* 课时统计信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">{formData.duration}</div>
                    <div className="text-xs text-gray-500">分钟</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">{formData.viewCount}</div>
                    <div className="text-xs text-gray-500">观看次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">{formData.completionRate.toFixed(1)}%</div>
                    <div className="text-xs text-gray-500">完成率</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">{formData.exercises.length}</div>
                    <div className="text-xs text-gray-500">练习题</div>
                  </div>
                </div>

                {/* 课程和模块信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                  <div className="text-sm text-blue-800 dark:text-blue-300">
                    <span className="font-medium">课程:</span> {formData.courseId} 
                    <span className="mx-2">•</span>
                    <span className="font-medium">模块:</span> {formData.moduleId}
                  </div>
                </div>
              </div>

              {/* 学习目标 */}
              {formData.learningObjectives.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    学习目标
                  </h4>
                  <ul className="space-y-2">
                    {formData.learningObjectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2 text-gray-600 dark:text-gray-300">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        {objective}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 前置知识 */}
              {formData.prerequisites.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    前置知识
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.prerequisites.map((prereq, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm bg-gray-100 text-gray-700 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"
                      >
                        {prereq}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 课时内容预览 */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  课时内容
                </h4>
                <div className="prose prose-sm max-w-none dark:prose-invert bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <ReactMarkdown>{formData.content}</ReactMarkdown>
                </div>
              </div>

              {/* 学习资源 */}
              {formData.resources.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    学习资源
                  </h4>
                  <div className="space-y-3">
                    {formData.resources.map((resource, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">
                            {resource.type === 'video' ? '📹' :
                             resource.type === 'document' ? '📄' :
                             resource.type === 'code' ? '💻' :
                             resource.type === 'link' ? '🔗' : '📥'}
                          </span>
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white">
                              {resource.title}
                              {resource.isRequired && (
                                <span className="text-red-500 text-sm ml-1">*</span>
                              )}
                            </h5>
                            {resource.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {resource.description}
                              </p>
                            )}
                            {resource.duration && (
                              <span className="text-xs text-gray-500">
                                {resource.duration} 分钟
                              </span>
                            )}
                          </div>
                        </div>
                        <a
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                        >
                          查看
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 练习题 */}
              {formData.exercises.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    练习题 ({formData.exercises.length} 题)
                  </h4>
                  <div className="space-y-4">
                    {formData.exercises.map((exercise, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            题目 {index + 1}: {exercise.question}
                          </h5>
                          <span className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
                            {exercise.points} 分
                          </span>
                        </div>

                        {exercise.options && exercise.options.length > 0 && (
                          <div className="space-y-2 mb-3">
                            {exercise.options.map((option, optionIndex) => (
                              <div key={optionIndex} className="flex items-center gap-2">
                                <span className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-sm">
                                  {String.fromCharCode(65 + optionIndex)}
                                </span>
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                  {option}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded p-3">
                          <div className="text-sm">
                            <span className="font-medium text-green-800 dark:text-green-300">正确答案:</span>
                            <span className="ml-2 text-green-700 dark:text-green-400">{exercise.correctAnswer}</span>
                          </div>
                          {exercise.explanation && (
                            <div className="text-sm text-green-600 dark:text-green-400 mt-1">
                              <span className="font-medium">解释:</span> {exercise.explanation}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 视频信息 */}
              {formData.videoInfo && formData.type === 'video' && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <PlayCircle className="w-5 h-5" />
                    视频信息
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    {formData.videoInfo.thumbnailUrl && (
                      <div className="mb-3">
                        <img
                          src={formData.videoInfo.thumbnailUrl}
                          alt="视频缩略图"
                          className="w-full max-w-md rounded-lg"
                        />
                      </div>
                    )}

                    {formData.videoInfo.chapters.length > 0 && (
                      <div>
                        <h5 className="font-medium text-gray-900 dark:text-white mb-2">视频章节</h5>
                        <div className="space-y-2">
                          {formData.videoInfo.chapters.map((chapter, index) => (
                            <div key={index} className="flex items-center justify-between py-2 px-3 bg-white dark:bg-gray-600 rounded">
                              <span className="text-sm text-gray-900 dark:text-white">
                                {chapter.title}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {Math.floor(chapter.startTime / 60)}:{(chapter.startTime % 60).toString().padStart(2, '0')}
                                {chapter.endTime && ` - ${Math.floor(chapter.endTime / 60)}:${(chapter.endTime % 60).toString().padStart(2, '0')}`}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 完成条件 */}
              {formData.completionCriteria && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    完成条件
                  </h4>
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {formData.completionCriteria.watchPercentage}%
                        </div>
                        <div className="text-blue-700 dark:text-blue-300">观看完成度</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {formData.completionCriteria.exerciseScore}分
                        </div>
                        <div className="text-blue-700 dark:text-blue-300">练习最低分</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {formData.completionCriteria.requireAllResources ? '是' : '否'}
                        </div>
                        <div className="text-blue-700 dark:text-blue-300">查看必需资源</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 评分信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 dark:text-white mb-2">课时评分</h5>
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex items-center">
                    {renderStars(formData.rating.average)}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {formData.rating.average.toFixed(1)} ({formData.rating.count} 评价)
                  </span>
                </div>
              </div>

              {/* 标签 */}
              {formData.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-1">
                    <Hash className="w-4 h-4" />
                    标签
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* SEO信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  SEO预览
                </h4>
                <div className="space-y-2">
                  <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                    {formData.seo.metaTitle}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-xs">
                    {formData.seo.metaDescription}
                  </div>
                  {formData.seo.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.seo.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LessonEditorTestPage;
