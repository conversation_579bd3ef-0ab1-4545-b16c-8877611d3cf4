import { BlockFormConfig } from '../types';

const eventConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "活动标题",
        minLength: 5,
        maxLength: 150,
        default: "2024年前端技术大会：探索Web开发的未来"
      },
      slug: {
        type: "string",
        title: "活动标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "frontend-tech-conference-2024"
      },
      description: {
        type: "string",
        title: "活动描述",
        minLength: 50,
        maxLength: 2000,
        default: "加入我们的年度前端技术大会，与行业专家和开发者一起探讨最新的Web开发技术趋势。本次大会将涵盖React、Vue、Angular等主流框架的最新发展，以及AI在前端开发中的应用。无论你是初学者还是资深开发者，都能在这里找到有价值的内容和灵感。"
      },
      type: {
        type: "string",
        title: "活动类型",
        enum: ["conference", "workshop", "webinar", "meetup", "training", "networking", "exhibition", "competition"],
        default: "conference"
      },
      category: {
        type: "string",
        title: "活动分类",
        minLength: 2,
        maxLength: 50,
        default: "技术会议"
      },
      tags: {
        type: "array",
        title: "活动标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["前端开发", "技术大会", "Web技术", "React"],
        maxItems: 15,
        minItems: 1
      },
      startDate: {
        type: "string",
        title: "开始日期",
        format: "date-time",
        default: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      endDate: {
        type: "string",
        title: "结束日期",
        format: "date-time",
        default: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000).toISOString()
      },
      timezone: {
        type: "string",
        title: "时区",
        enum: ["Asia/Shanghai", "America/New_York", "Europe/London", "Asia/Tokyo", "Australia/Sydney"],
        default: "Asia/Shanghai"
      },
      location: {
        type: "object",
        title: "活动地点",
        properties: {
          type: {
            type: "string",
            title: "地点类型",
            enum: ["online", "offline", "hybrid"],
            default: "hybrid"
          },
          venue: {
            type: "string",
            title: "场地名称",
            maxLength: 200,
            default: "上海国际会议中心"
          },
          address: {
            type: "string",
            title: "详细地址",
            maxLength: 300,
            default: "上海市浦东新区滨江大道2727号"
          },
          city: {
            type: "string",
            title: "城市",
            maxLength: 50,
            default: "上海"
          },
          country: {
            type: "string",
            title: "国家",
            maxLength: 50,
            default: "中国"
          },
          coordinates: {
            type: "object",
            title: "地理坐标",
            properties: {
              latitude: {
                type: "number",
                title: "纬度",
                minimum: -90,
                maximum: 90,
                default: 31.2304
              },
              longitude: {
                type: "number",
                title: "经度",
                minimum: -180,
                maximum: 180,
                default: 121.4737
              }
            }
          },
          onlineUrl: {
            type: "string",
            title: "在线会议链接",
            format: "uri",
            default: "https://zoom.us/j/123456789"
          },
          onlinePlatform: {
            type: "string",
            title: "在线平台",
            enum: ["zoom", "teams", "webex", "google-meet", "tencent-meeting", "custom"],
            default: "zoom"
          }
        },
        required: ["type"]
      },
      capacity: {
        type: "object",
        title: "容量设置",
        properties: {
          maxAttendees: {
            type: "integer",
            title: "最大参会人数",
            minimum: 1,
            maximum: 100000,
            default: 500
          },
          currentAttendees: {
            type: "integer",
            title: "当前报名人数",
            minimum: 0,
            default: 0
          },
          waitlistEnabled: {
            type: "boolean",
            title: "启用候补名单",
            default: true
          },
          waitlistCount: {
            type: "integer",
            title: "候补人数",
            minimum: 0,
            default: 0
          }
        },
        required: ["maxAttendees"]
      },
      registration: {
        type: "object",
        title: "报名设置",
        properties: {
          isRequired: {
            type: "boolean",
            title: "需要报名",
            default: true
          },
          registrationUrl: {
            type: "string",
            title: "报名链接",
            format: "uri",
            default: "https://example.com/register/frontend-conference-2024"
          },
          registrationDeadline: {
            type: "string",
            title: "报名截止时间",
            format: "date-time"
          },
          approvalRequired: {
            type: "boolean",
            title: "需要审核",
            default: false
          },
          fee: {
            type: "object",
            title: "费用信息",
            properties: {
              isFree: {
                type: "boolean",
                title: "免费活动",
                default: false
              },
              currency: {
                type: "string",
                title: "货币",
                enum: ["CNY", "USD", "EUR"],
                default: "CNY"
              },
              earlyBirdPrice: {
                type: "number",
                title: "早鸟价格",
                minimum: 0,
                default: 299
              },
              regularPrice: {
                type: "number",
                title: "正常价格",
                minimum: 0,
                default: 399
              },
              studentPrice: {
                type: "number",
                title: "学生价格",
                minimum: 0,
                default: 199
              },
              earlyBirdDeadline: {
                type: "string",
                title: "早鸟截止时间",
                format: "date-time"
              }
            }
          }
        }
      },
      agenda: {
        type: "array",
        title: "活动议程",
        items: {
          type: "object",
          properties: {
            time: {
              type: "string",
              title: "时间",
              default: "09:00"
            },
            duration: {
              type: "integer",
              title: "时长(分钟)",
              minimum: 5,
              maximum: 480,
              default: 60
            },
            title: {
              type: "string",
              title: "议程标题",
              minLength: 5,
              maxLength: 200
            },
            description: {
              type: "string",
              title: "议程描述",
              maxLength: 500
            },
            type: {
              type: "string",
              title: "议程类型",
              enum: ["keynote", "session", "workshop", "panel", "break", "networking", "lunch"],
              default: "session"
            },
            speaker: {
              type: "string",
              title: "演讲者",
              maxLength: 100
            },
            location: {
              type: "string",
              title: "地点/房间",
              maxLength: 100
            },
            isBreak: {
              type: "boolean",
              title: "休息时间",
              default: false
            }
          },
          required: ["time", "title", "type"]
        },
        default: [
          {
            time: "09:00",
            duration: 30,
            title: "签到和早餐",
            description: "参会者签到，享用早餐，网络交流",
            type: "networking",
            location: "大厅",
            isBreak: true
          },
          {
            time: "09:30",
            duration: 60,
            title: "开幕主题演讲：前端技术的未来趋势",
            description: "探讨前端技术的发展方向和未来机遇",
            type: "keynote",
            speaker: "张三 - 前端技术专家",
            location: "主会场"
          },
          {
            time: "10:30",
            duration: 15,
            title: "茶歇",
            type: "break",
            location: "休息区",
            isBreak: true
          }
        ],
        maxItems: 50,
        minItems: 1
      },
      speakers: {
        type: "array",
        title: "演讲者",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "姓名",
              minLength: 2,
              maxLength: 50
            },
            title: {
              type: "string",
              title: "职位头衔",
              maxLength: 100
            },
            company: {
              type: "string",
              title: "公司/组织",
              maxLength: 100
            },
            bio: {
              type: "string",
              title: "个人简介",
              maxLength: 1000
            },
            avatar: {
              type: "string",
              title: "头像URL",
              format: "uri"
            },
            social: {
              type: "object",
              title: "社交媒体",
              properties: {
                website: {
                  type: "string",
                  title: "个人网站",
                  format: "uri"
                },
                linkedin: {
                  type: "string",
                  title: "LinkedIn",
                  format: "uri"
                },
                twitter: {
                  type: "string",
                  title: "Twitter",
                  format: "uri"
                },
                github: {
                  type: "string",
                  title: "GitHub",
                  format: "uri"
                }
              }
            },
            topics: {
              type: "array",
              title: "演讲主题",
              items: {
                type: "string",
                minLength: 5,
                maxLength: 200
              },
              maxItems: 5
            }
          },
          required: ["name", "title"]
        },
        default: [
          {
            name: "张三",
            title: "前端技术专家",
            company: "TechCorp",
            bio: "拥有10年前端开发经验，React核心贡献者，专注于现代Web技术和性能优化。",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            social: {
              website: "https://zhangsan.dev",
              github: "https://github.com/zhangsan",
              twitter: "https://twitter.com/zhangsan"
            },
            topics: ["React最新特性", "前端性能优化"]
          }
        ],
        maxItems: 50,
        minItems: 0
      },
      sponsors: {
        type: "array",
        title: "赞助商",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "赞助商名称",
              minLength: 2,
              maxLength: 100
            },
            logo: {
              type: "string",
              title: "Logo URL",
              format: "uri"
            },
            website: {
              type: "string",
              title: "官网",
              format: "uri"
            },
            level: {
              type: "string",
              title: "赞助级别",
              enum: ["platinum", "gold", "silver", "bronze", "partner"],
              default: "bronze"
            },
            description: {
              type: "string",
              title: "公司描述",
              maxLength: 500
            }
          },
          required: ["name", "level"]
        },
        default: [],
        maxItems: 20,
        minItems: 0
      },
      organizer: {
        type: "object",
        title: "主办方信息",
        properties: {
          name: {
            type: "string",
            title: "主办方名称",
            minLength: 2,
            maxLength: 100,
            default: "前端开发者社区"
          },
          logo: {
            type: "string",
            title: "主办方Logo",
            format: "uri",
            default: "https://example.com/organizer-logo.png"
          },
          website: {
            type: "string",
            title: "主办方网站",
            format: "uri",
            default: "https://frontend-community.org"
          },
          contact: {
            type: "object",
            title: "联系方式",
            properties: {
              email: {
                type: "string",
                title: "邮箱",
                format: "email",
                default: "<EMAIL>"
              },
              phone: {
                type: "string",
                title: "电话",
                maxLength: 20,
                default: "+86 138 0013 8000"
              }
            }
          }
        },
        required: ["name"]
      },
      status: {
        type: "string",
        title: "活动状态",
        enum: ["draft", "published", "registration-open", "registration-closed", "ongoing", "completed", "cancelled"],
        default: "draft"
      },
      visibility: {
        type: "string",
        title: "可见性",
        enum: ["public", "private", "invite-only"],
        default: "public"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "2024年前端技术大会 - 探索Web开发的未来 | 技术会议"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "参加2024年前端技术大会，与行业专家探讨React、Vue、AI等最新Web技术。线上线下同步进行，立即报名参与。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["前端大会", "技术会议", "Web开发", "React会议"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "type", "category", "startDate", "endDate", "location", "capacity", "agenda", "organizer"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "type", "category", "tags",
      "startDate", "endDate", "timezone", "location", "capacity", "registration",
      "agenda", "speakers", "sponsors", "organizer", "status", "visibility",
      "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入活动标题...",
        help: "吸引人的活动标题，突出核心价值"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：frontend-tech-conference-2024",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 8,
        placeholder: "详细描述活动内容、目标受众和预期收获...",
        help: "详细的活动介绍，有助于吸引参会者"
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "conference", label: "🎤 会议" },
          { value: "workshop", label: "🛠️ 工作坊" },
          { value: "webinar", label: "💻 网络研讨会" },
          { value: "meetup", label: "🤝 聚会" },
          { value: "training", label: "📚 培训" },
          { value: "networking", label: "🌐 社交活动" },
          { value: "exhibition", label: "🏛️ 展览" },
          { value: "competition", label: "🏆 竞赛" }
        ]
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：技术会议、产品发布、培训课程",
        help: "活动所属的主要分类"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },
    
    startDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "活动开始日期和时间"
      }
    },
    
    endDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "活动结束日期和时间"
      }
    },
    
    timezone: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "Asia/Shanghai", label: "北京时间 (UTC+8)" },
          { value: "America/New_York", label: "纽约时间 (UTC-5/-4)" },
          { value: "Europe/London", label: "伦敦时间 (UTC+0/+1)" },
          { value: "Asia/Tokyo", label: "东京时间 (UTC+9)" },
          { value: "Australia/Sydney", label: "悉尼时间 (UTC+10/+11)" }
        ]
      }
    },

    location: {
      "ui:order": ["type", "venue", "address", "city", "country", "coordinates", "onlineUrl", "onlinePlatform"],

      type: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "online", label: "🌐 线上活动" },
            { value: "offline", label: "🏢 线下活动" },
            { value: "hybrid", label: "🔄 线上线下混合" }
          ]
        }
      },

      venue: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "场地名称..."
        }
      },

      address: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "详细地址..."
        }
      },

      city: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "城市..."
        }
      },

      country: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "国家..."
        }
      },

      coordinates: {
        "ui:order": ["latitude", "longitude"],

        latitude: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.000001,
            help: "纬度坐标"
          }
        },

        longitude: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.000001,
            help: "经度坐标"
          }
        }
      },

      onlineUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "在线会议链接..."
        }
      },

      onlinePlatform: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "zoom", label: "Zoom" },
            { value: "teams", label: "Microsoft Teams" },
            { value: "webex", label: "Cisco Webex" },
            { value: "google-meet", label: "Google Meet" },
            { value: "tencent-meeting", label: "腾讯会议" },
            { value: "custom", label: "自定义平台" }
          ]
        }
      }
    },

    capacity: {
      "ui:order": ["maxAttendees", "currentAttendees", "waitlistEnabled", "waitlistCount"],

      maxAttendees: {
        "ui:widget": "updown",
        "ui:options": {
          help: "活动最大容纳人数"
        }
      },

      currentAttendees: {
        "ui:widget": "updown",
        "ui:options": {
          help: "当前已报名人数"
        }
      },

      waitlistEnabled: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否启用候补名单"
        }
      },

      waitlistCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "候补名单人数"
        }
      }
    },

    registration: {
      "ui:order": ["isRequired", "registrationUrl", "registrationDeadline", "approvalRequired", "fee"],

      isRequired: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否需要提前报名"
        }
      },

      registrationUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "报名页面链接..."
        }
      },

      registrationDeadline: {
        "ui:widget": "datetime",
        "ui:options": {
          help: "报名截止时间"
        }
      },

      approvalRequired: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否需要审核报名"
        }
      },

      fee: {
        "ui:order": ["isFree", "currency", "earlyBirdPrice", "regularPrice", "studentPrice", "earlyBirdDeadline"],

        isFree: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为免费活动"
          }
        },

        currency: {
          "ui:widget": "select"
        },

        earlyBirdPrice: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.01,
            help: "早鸟优惠价格"
          }
        },

        regularPrice: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.01,
            help: "正常票价"
          }
        },

        studentPrice: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.01,
            help: "学生优惠价格"
          }
        },

        earlyBirdDeadline: {
          "ui:widget": "datetime",
          "ui:options": {
            help: "早鸟优惠截止时间"
          }
        }
      }
    },

    agenda: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加议程"
      },
      items: {
        "ui:order": ["time", "duration", "title", "description", "type", "speaker", "location", "isBreak"],

        time: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：09:00"
          }
        },

        duration: {
          "ui:widget": "updown",
          "ui:options": {
            help: "时长（分钟）"
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "议程标题..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "议程描述..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "keynote", label: "🎤 主题演讲" },
              { value: "session", label: "📋 分会场" },
              { value: "workshop", label: "🛠️ 工作坊" },
              { value: "panel", label: "💬 圆桌讨论" },
              { value: "break", label: "☕ 休息" },
              { value: "networking", label: "🤝 社交" },
              { value: "lunch", label: "🍽️ 午餐" }
            ]
          }
        },

        speaker: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演讲者姓名..."
          }
        },

        location: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "地点/房间..."
          }
        },

        isBreak: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为休息时间"
          }
        }
      }
    },

    speakers: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加演讲者"
      },
      items: {
        "ui:order": ["name", "title", "company", "bio", "avatar", "social", "topics"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演讲者姓名..."
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "职位头衔..."
          }
        },

        company: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "公司/组织..."
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 4,
            placeholder: "个人简介..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "头像图片URL..."
          }
        },

        social: {
          "ui:order": ["website", "linkedin", "twitter", "github"],

          website: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "个人网站..."
            }
          },

          linkedin: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "LinkedIn个人资料..."
            }
          },

          twitter: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Twitter账号..."
            }
          },

          github: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "GitHub个人资料..."
            }
          }
        },

        topics: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加演讲主题"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "演讲主题..."
            }
          }
        }
      }
    },

    sponsors: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加赞助商"
      },
      items: {
        "ui:order": ["name", "logo", "website", "level", "description"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "赞助商名称..."
          }
        },

        logo: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Logo图片URL..."
          }
        },

        website: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "官方网站..."
          }
        },

        level: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "platinum", label: "🥇 白金赞助商" },
              { value: "gold", label: "🥇 金牌赞助商" },
              { value: "silver", label: "🥈 银牌赞助商" },
              { value: "bronze", label: "🥉 铜牌赞助商" },
              { value: "partner", label: "🤝 合作伙伴" }
            ]
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "公司描述..."
          }
        }
      }
    },

    organizer: {
      "ui:order": ["name", "logo", "website", "contact"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "主办方名称..."
        }
      },

      logo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "主办方Logo URL..."
        }
      },

      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "主办方网站..."
        }
      },

      contact: {
        "ui:order": ["email", "phone"],

        email: {
          "ui:widget": "email",
          "ui:options": {
            placeholder: "联系邮箱..."
          }
        },

        phone: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "联系电话..."
          }
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "published", label: "✅ 已发布" },
          { value: "registration-open", label: "📝 报名开放" },
          { value: "registration-closed", label: "🔒 报名截止" },
          { value: "ongoing", label: "🎯 进行中" },
          { value: "completed", label: "✅ 已完成" },
          { value: "cancelled", label: "❌ 已取消" }
        ]
      }
    },

    visibility: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "public", label: "🌍 公开" },
          { value: "private", label: "🔒 私有" },
          { value: "invite-only", label: "📧 仅邀请" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "活动信息发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default eventConfig;
