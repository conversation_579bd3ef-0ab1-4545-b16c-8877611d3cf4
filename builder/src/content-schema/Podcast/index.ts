import { BlockFormConfig } from '../types';

const podcastConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "播客标题",
        minLength: 5,
        maxLength: 200,
        default: "AI技术前沿：深度学习在自然语言处理中的应用"
      },
      slug: {
        type: "string",
        title: "播客标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "ai-tech-frontier-nlp-deep-learning"
      },
      description: {
        type: "string",
        title: "播客描述",
        minLength: 50,
        maxLength: 2000,
        default: "本期播客深入探讨人工智能技术的最新发展，重点分析深度学习在自然语言处理领域的突破性应用。我们将讨论GPT、BERT等模型的技术原理，以及它们在实际业务中的应用案例和未来发展趋势。"
      },
      episodeNumber: {
        type: "integer",
        title: "集数",
        minimum: 1,
        maximum: 10000,
        default: 1
      },
      season: {
        type: "integer",
        title: "季数",
        minimum: 1,
        maximum: 100,
        default: 1
      },
      seriesInfo: {
        type: "object",
        title: "系列信息",
        properties: {
          seriesTitle: {
            type: "string",
            title: "系列标题",
            maxLength: 150,
            default: "AI技术前沿"
          },
          seriesDescription: {
            type: "string",
            title: "系列描述",
            maxLength: 1000,
            default: "探索人工智能技术的最新发展和应用趋势"
          },
          totalEpisodes: {
            type: "integer",
            title: "系列总集数",
            minimum: 1,
            default: 10
          }
        }
      },
      audioFile: {
        type: "object",
        title: "音频文件",
        properties: {
          url: {
            type: "string",
            title: "音频文件URL",
            format: "uri",
            default: "https://example.com/podcasts/ai-tech-frontier-ep1.mp3"
          },
          format: {
            type: "string",
            title: "音频格式",
            enum: ["mp3", "wav", "aac", "ogg", "m4a"],
            default: "mp3"
          },
          duration: {
            type: "integer",
            title: "时长(秒)",
            minimum: 10,
            maximum: 18000,
            default: 2400
          },
          fileSize: {
            type: "integer",
            title: "文件大小(MB)",
            minimum: 1,
            maximum: 1000,
            default: 45
          },
          quality: {
            type: "string",
            title: "音质",
            enum: ["low", "medium", "high", "lossless"],
            default: "high"
          },
          bitrate: {
            type: "integer",
            title: "比特率(kbps)",
            minimum: 64,
            maximum: 320,
            default: 128
          }
        },
        required: ["url", "format", "duration"]
      },
      transcript: {
        type: "object",
        title: "转录文本",
        properties: {
          fullText: {
            type: "string",
            title: "完整转录",
            default: "欢迎收听AI技术前沿播客。我是主播张三，今天我们要深入探讨深度学习在自然语言处理中的应用...\n\n[00:30] 首先，让我们回顾一下自然语言处理的发展历程...\n\n[02:15] 深度学习的引入彻底改变了NLP领域..."
          },
          timestampedSegments: {
            type: "array",
            title: "时间戳分段",
            items: {
              type: "object",
              properties: {
                startTime: {
                  type: "integer",
                  title: "开始时间(秒)",
                  minimum: 0
                },
                endTime: {
                  type: "integer",
                  title: "结束时间(秒)",
                  minimum: 0
                },
                text: {
                  type: "string",
                  title: "文本内容",
                  maxLength: 1000
                },
                speaker: {
                  type: "string",
                  title: "说话人",
                  maxLength: 50
                },
                confidence: {
                  type: "number",
                  title: "识别置信度",
                  minimum: 0,
                  maximum: 1
                }
              },
              required: ["startTime", "endTime", "text"]
            },
            default: [
              {
                startTime: 0,
                endTime: 30,
                text: "欢迎收听AI技术前沿播客。我是主播张三，今天我们要深入探讨深度学习在自然语言处理中的应用。",
                speaker: "张三",
                confidence: 0.95
              },
              {
                startTime: 30,
                endTime: 135,
                text: "首先，让我们回顾一下自然语言处理的发展历程。从早期的规则基础方法，到统计机器学习，再到现在的深度学习时代。",
                speaker: "张三",
                confidence: 0.92
              }
            ],
            maxItems: 1000,
            minItems: 0
          },
          language: {
            type: "string",
            title: "转录语言",
            enum: ["zh-CN", "en", "ja", "ko", "es", "fr", "de"],
            default: "zh-CN"
          },
          accuracy: {
            type: "number",
            title: "转录准确度",
            minimum: 0,
            maximum: 1,
            default: 0.95
          }
        }
      },
      chapters: {
        type: "array",
        title: "章节标记",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "章节标题",
              minLength: 2,
              maxLength: 150
            },
            startTime: {
              type: "integer",
              title: "开始时间(秒)",
              minimum: 0
            },
            endTime: {
              type: "integer",
              title: "结束时间(秒)",
              minimum: 0
            },
            description: {
              type: "string",
              title: "章节描述",
              maxLength: 500
            },
            keywords: {
              type: "array",
              title: "关键词",
              items: {
                type: "string",
                minLength: 2,
                maxLength: 30
              },
              maxItems: 10
            }
          },
          required: ["title", "startTime"]
        },
        default: [
          {
            title: "开场介绍",
            startTime: 0,
            endTime: 120,
            description: "播客介绍和今日话题概述",
            keywords: ["介绍", "AI", "深度学习"]
          },
          {
            title: "NLP发展历程",
            startTime: 120,
            endTime: 600,
            description: "回顾自然语言处理技术的发展历程",
            keywords: ["NLP", "历史", "发展"]
          },
          {
            title: "深度学习应用",
            startTime: 600,
            endTime: 1800,
            description: "深度学习在NLP中的具体应用案例",
            keywords: ["深度学习", "应用", "案例"]
          },
          {
            title: "未来展望",
            startTime: 1800,
            endTime: 2400,
            description: "技术发展趋势和未来展望",
            keywords: ["未来", "趋势", "展望"]
          }
        ],
        maxItems: 50,
        minItems: 0
      },
      hosts: {
        type: "array",
        title: "主播信息",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "主播姓名",
              minLength: 2,
              maxLength: 50
            },
            role: {
              type: "string",
              title: "角色",
              enum: ["host", "co-host", "guest"],
              default: "host"
            },
            bio: {
              type: "string",
              title: "个人简介",
              maxLength: 500
            },
            avatar: {
              type: "string",
              title: "头像URL",
              format: "uri"
            },
            social: {
              type: "object",
              title: "社交媒体",
              properties: {
                website: {
                  type: "string",
                  title: "个人网站",
                  format: "uri"
                },
                twitter: {
                  type: "string",
                  title: "Twitter",
                  format: "uri"
                },
                linkedin: {
                  type: "string",
                  title: "LinkedIn",
                  format: "uri"
                },
                email: {
                  type: "string",
                  title: "邮箱",
                  format: "email"
                }
              }
            }
          },
          required: ["name", "role"]
        },
        default: [
          {
            name: "张三",
            role: "host",
            bio: "AI技术专家，拥有10年机器学习研发经验，专注于自然语言处理和深度学习技术。",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            social: {
              website: "https://zhangsan.ai",
              twitter: "https://twitter.com/zhangsan_ai",
              email: "<EMAIL>"
            }
          }
        ],
        maxItems: 10,
        minItems: 1
      },
      category: {
        type: "string",
        title: "播客分类",
        enum: ["technology", "business", "education", "entertainment", "news", "health", "science", "arts"],
        default: "technology"
      },
      tags: {
        type: "array",
        title: "标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["AI", "深度学习", "NLP", "技术", "人工智能"],
        maxItems: 15,
        minItems: 1
      },
      language: {
        type: "string",
        title: "播客语言",
        enum: ["zh-CN", "en", "ja", "ko", "es", "fr", "de"],
        default: "zh-CN"
      },
      explicit: {
        type: "boolean",
        title: "包含敏感内容",
        default: false
      },
      copyright: {
        type: "string",
        title: "版权信息",
        maxLength: 200,
        default: "© 2024 AI技术前沿播客. 保留所有权利。"
      },
      subscription: {
        type: "object",
        title: "订阅信息",
        properties: {
          rssUrl: {
            type: "string",
            title: "RSS订阅链接",
            format: "uri",
            default: "https://example.com/podcast/ai-tech-frontier/rss"
          },
          applePodcasts: {
            type: "string",
            title: "Apple Podcasts链接",
            format: "uri"
          },
          spotify: {
            type: "string",
            title: "Spotify链接",
            format: "uri"
          },
          googlePodcasts: {
            type: "string",
            title: "Google Podcasts链接",
            format: "uri"
          },
          subscriberCount: {
            type: "integer",
            title: "订阅人数",
            minimum: 0,
            default: 0
          }
        }
      },
      analytics: {
        type: "object",
        title: "播放统计",
        properties: {
          totalPlays: {
            type: "integer",
            title: "总播放次数",
            minimum: 0,
            default: 0
          },
          uniqueListeners: {
            type: "integer",
            title: "独立听众数",
            minimum: 0,
            default: 0
          },
          averageListenTime: {
            type: "integer",
            title: "平均收听时长(秒)",
            minimum: 0,
            default: 0
          },
          completionRate: {
            type: "number",
            title: "完成率(%)",
            minimum: 0,
            maximum: 100,
            default: 0
          },
          downloads: {
            type: "integer",
            title: "下载次数",
            minimum: 0,
            default: 0
          },
          shares: {
            type: "integer",
            title: "分享次数",
            minimum: 0,
            default: 0
          },
          ratings: {
            type: "object",
            title: "评分统计",
            properties: {
              average: {
                type: "number",
                title: "平均评分",
                minimum: 0,
                maximum: 5,
                default: 0
              },
              count: {
                type: "integer",
                title: "评分人数",
                minimum: 0,
                default: 0
              }
            }
          }
        }
      },
      monetization: {
        type: "object",
        title: "变现设置",
        properties: {
          isPremium: {
            type: "boolean",
            title: "付费内容",
            default: false
          },
          price: {
            type: "number",
            title: "价格",
            minimum: 0,
            default: 0
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          sponsorship: {
            type: "object",
            title: "赞助信息",
            properties: {
              hasSponsor: {
                type: "boolean",
                title: "有赞助商",
                default: false
              },
              sponsorName: {
                type: "string",
                title: "赞助商名称",
                maxLength: 100
              },
              sponsorUrl: {
                type: "string",
                title: "赞助商链接",
                format: "uri"
              },
              sponsorMessage: {
                type: "string",
                title: "赞助商信息",
                maxLength: 500
              }
            }
          },
          donations: {
            type: "object",
            title: "打赏设置",
            properties: {
              enabled: {
                type: "boolean",
                title: "启用打赏",
                default: false
              },
              platforms: {
                type: "array",
                title: "打赏平台",
                items: {
                  type: "object",
                  properties: {
                    platform: {
                      type: "string",
                      title: "平台名称",
                      enum: ["paypal", "alipay", "wechat", "patreon", "buymeacoffee"],
                      default: "alipay"
                    },
                    url: {
                      type: "string",
                      title: "打赏链接",
                      format: "uri"
                    }
                  },
                  required: ["platform", "url"]
                },
                maxItems: 5
              }
            }
          }
        }
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "scheduled", "published", "private", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "AI技术前沿：深度学习在自然语言处理中的应用 | 播客"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "深入探讨人工智能技术的最新发展，分析深度学习在自然语言处理领域的突破性应用。专业技术播客，值得收听。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["AI播客", "深度学习", "自然语言处理", "技术播客"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "episodeNumber", "audioFile", "hosts", "category"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "episodeNumber", "season", "seriesInfo",
      "audioFile", "transcript", "chapters", "hosts", "category", "tags",
      "language", "explicit", "copyright", "subscription", "analytics",
      "monetization", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入播客标题...",
        help: "吸引听众的播客标题，突出核心内容"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：ai-tech-frontier-episode-1",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "详细描述播客内容、讨论话题和预期收获...",
        help: "详细的播客介绍，有助于SEO和听众理解"
      }
    },
    
    episodeNumber: {
      "ui:widget": "updown",
      "ui:options": {
        help: "当前集数"
      }
    },
    
    season: {
      "ui:widget": "updown",
      "ui:options": {
        help: "当前季数"
      }
    },

    seriesInfo: {
      "ui:order": ["seriesTitle", "seriesDescription", "totalEpisodes"],

      seriesTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "播客系列标题..."
        }
      },

      seriesDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "系列描述..."
        }
      },

      totalEpisodes: {
        "ui:widget": "updown",
        "ui:options": {
          help: "计划的总集数"
        }
      }
    },

    audioFile: {
      "ui:order": ["url", "format", "duration", "fileSize", "quality", "bitrate"],

      url: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "音频文件URL..."
        }
      },

      format: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "mp3", label: "MP3" },
            { value: "wav", label: "WAV" },
            { value: "aac", label: "AAC" },
            { value: "ogg", label: "OGG" },
            { value: "m4a", label: "M4A" }
          ]
        }
      },

      duration: {
        "ui:widget": "updown",
        "ui:options": {
          help: "音频时长（秒）"
        }
      },

      fileSize: {
        "ui:widget": "updown",
        "ui:options": {
          help: "文件大小（MB）"
        }
      },

      quality: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "low", label: "低音质" },
            { value: "medium", label: "中等音质" },
            { value: "high", label: "高音质" },
            { value: "lossless", label: "无损音质" }
          ]
        }
      },

      bitrate: {
        "ui:widget": "updown",
        "ui:options": {
          help: "音频比特率（kbps）"
        }
      }
    },

    transcript: {
      "ui:order": ["fullText", "timestampedSegments", "language", "accuracy"],

      fullText: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 10,
          placeholder: "完整的转录文本...",
          help: "播客的完整文字转录"
        }
      },

      timestampedSegments: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加时间戳分段"
        },
        items: {
          "ui:order": ["startTime", "endTime", "text", "speaker", "confidence"],

          startTime: {
            "ui:widget": "updown",
            "ui:options": {
              help: "开始时间（秒）"
            }
          },

          endTime: {
            "ui:widget": "updown",
            "ui:options": {
              help: "结束时间（秒）"
            }
          },

          text: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 3,
              placeholder: "这段时间的文本内容..."
            }
          },

          speaker: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "说话人姓名..."
            }
          },

          confidence: {
            "ui:widget": "updown",
            "ui:options": {
              step: 0.01,
              help: "AI识别置信度（0-1）"
            }
          }
        }
      },

      language: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "zh-CN", label: "中文" },
            { value: "en", label: "English" },
            { value: "ja", label: "日本語" },
            { value: "ko", label: "한국어" },
            { value: "es", label: "Español" },
            { value: "fr", label: "Français" },
            { value: "de", label: "Deutsch" }
          ]
        }
      },

      accuracy: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "转录准确度（0-1）"
        }
      }
    },

    chapters: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加章节"
      },
      items: {
        "ui:order": ["title", "startTime", "endTime", "description", "keywords"],

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "章节标题..."
          }
        },

        startTime: {
          "ui:widget": "updown",
          "ui:options": {
            help: "开始时间（秒）"
          }
        },

        endTime: {
          "ui:widget": "updown",
          "ui:options": {
            help: "结束时间（秒）"
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "章节描述..."
          }
        },

        keywords: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加关键词"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "关键词..."
            }
          }
        }
      }
    },

    hosts: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加主播"
      },
      items: {
        "ui:order": ["name", "role", "bio", "avatar", "social"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "主播姓名..."
          }
        },

        role: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "host", label: "主播" },
              { value: "co-host", label: "联合主播" },
              { value: "guest", label: "嘉宾" }
            ]
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "个人简介..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "头像图片URL..."
          }
        },

        social: {
          "ui:order": ["website", "twitter", "linkedin", "email"],

          website: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "个人网站..."
            }
          },

          twitter: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Twitter链接..."
            }
          },

          linkedin: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "LinkedIn链接..."
            }
          },

          email: {
            "ui:widget": "email",
            "ui:options": {
              placeholder: "联系邮箱..."
            }
          }
        }
      }
    },

    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "technology", label: "🔬 科技" },
          { value: "business", label: "💼 商业" },
          { value: "education", label: "📚 教育" },
          { value: "entertainment", label: "🎭 娱乐" },
          { value: "news", label: "📰 新闻" },
          { value: "health", label: "🏥 健康" },
          { value: "science", label: "🧪 科学" },
          { value: "arts", label: "🎨 艺术" }
        ]
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    language: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "zh-CN", label: "中文" },
          { value: "en", label: "English" },
          { value: "ja", label: "日本語" },
          { value: "ko", label: "한국어" },
          { value: "es", label: "Español" },
          { value: "fr", label: "Français" },
          { value: "de", label: "Deutsch" }
        ]
      }
    },

    explicit: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "是否包含敏感或成人内容"
      }
    },

    copyright: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "版权信息...",
        help: "播客的版权声明"
      }
    },

    subscription: {
      "ui:order": ["rssUrl", "applePodcasts", "spotify", "googlePodcasts", "subscriberCount"],

      rssUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "RSS订阅链接..."
        }
      },

      applePodcasts: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Apple Podcasts链接..."
        }
      },

      spotify: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Spotify链接..."
        }
      },

      googlePodcasts: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Google Podcasts链接..."
        }
      },

      subscriberCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "当前订阅人数"
        }
      }
    },

    analytics: {
      "ui:order": ["totalPlays", "uniqueListeners", "averageListenTime", "completionRate", "downloads", "shares", "ratings"],

      totalPlays: {
        "ui:widget": "updown",
        "ui:options": {
          help: "总播放次数"
        }
      },

      uniqueListeners: {
        "ui:widget": "updown",
        "ui:options": {
          help: "独立听众数量"
        }
      },

      averageListenTime: {
        "ui:widget": "updown",
        "ui:options": {
          help: "平均收听时长（秒）"
        }
      },

      completionRate: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "完成率百分比"
        }
      },

      downloads: {
        "ui:widget": "updown",
        "ui:options": {
          help: "下载次数"
        }
      },

      shares: {
        "ui:widget": "updown",
        "ui:options": {
          help: "分享次数"
        }
      },

      ratings: {
        "ui:order": ["average", "count"],

        average: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.1,
            help: "平均评分（0-5分）"
          }
        },

        count: {
          "ui:widget": "updown",
          "ui:options": {
            help: "评分人数"
          }
        }
      }
    },

    monetization: {
      "ui:order": ["isPremium", "price", "currency", "sponsorship", "donations"],

      isPremium: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否为付费内容"
        }
      },

      price: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "内容价格"
        }
      },

      currency: {
        "ui:widget": "select"
      },

      sponsorship: {
        "ui:order": ["hasSponsor", "sponsorName", "sponsorUrl", "sponsorMessage"],

        hasSponsor: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否有赞助商"
          }
        },

        sponsorName: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "赞助商名称..."
          }
        },

        sponsorUrl: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "赞助商网站..."
          }
        },

        sponsorMessage: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "赞助商信息..."
          }
        }
      },

      donations: {
        "ui:order": ["enabled", "platforms"],

        enabled: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否启用打赏功能"
          }
        },

        platforms: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加打赏平台"
          },
          items: {
            "ui:order": ["platform", "url"],

            platform: {
              "ui:widget": "select",
              "ui:options": {
                enumOptions: [
                  { value: "paypal", label: "PayPal" },
                  { value: "alipay", label: "支付宝" },
                  { value: "wechat", label: "微信支付" },
                  { value: "patreon", label: "Patreon" },
                  { value: "buymeacoffee", label: "Buy Me a Coffee" }
                ]
              }
            },

            url: {
              "ui:widget": "text",
              "ui:options": {
                placeholder: "打赏链接..."
              }
            }
          }
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "scheduled", label: "⏰ 定时发布" },
          { value: "published", label: "✅ 已发布" },
          { value: "private", label: "🔒 私有" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "播客发布日期和时间"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期和时间"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default podcastConfig;
