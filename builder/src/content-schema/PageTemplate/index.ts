import { BlockFormConfig } from '../types';

const pageTemplateConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "页面模版标题",
        minLength: 5,
        maxLength: 100,
        default: "现代企业首页模版 - 专业商务展示页面"
      },
      slug: {
        type: "string",
        title: "页面模版标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "modern-business-homepage-template"
      },
      description: {
        type: "string",
        title: "页面模版描述",
        minLength: 50,
        maxLength: 1000,
        default: "这是一个现代化的企业首页模版，包含英雄区域、特性展示、服务介绍、客户评价等核心模块。采用响应式设计，支持多种布局变化和自定义选项。"
      },
      pageType: {
        type: "string",
        title: "页面类型",
        enum: ["home", "about", "services", "portfolio", "blog", "contact", "pricing", "team", "testimonials", "faq", "privacy", "terms", "landing", "product", "other"],
        default: "home"
      },
      category: {
        type: "string",
        title: "模版分类",
        enum: ["business", "portfolio", "ecommerce", "blog", "landing", "saas", "agency", "restaurant", "education", "healthcare", "nonprofit", "personal", "other"],
        default: "business"
      },
      layoutType: {
        type: "string",
        title: "布局类型",
        enum: ["single-column", "two-column", "three-column", "grid", "masonry", "hero-sections", "full-width", "sidebar", "other"],
        default: "hero-sections"
      },
      designStyle: {
        type: "string",
        title: "设计风格",
        enum: ["modern", "minimalist", "corporate", "creative", "elegant", "bold", "classic", "trendy"],
        default: "modern"
      },
      sections: {
        type: "array",
        title: "页面模块",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "模块名称",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "模块类型",
              enum: ["hero", "features", "services", "testimonials", "team", "pricing", "cta", "contact", "gallery", "blog", "stats", "faq", "newsletter", "footer", "other"],
              default: "other"
            },
            description: {
              type: "string",
              title: "模块描述",
              maxLength: 300
            },
            position: {
              type: "integer",
              title: "显示顺序",
              minimum: 1,
              default: 1
            },
            required: {
              type: "boolean",
              title: "是否必需",
              default: true
            },
            customizable: {
              type: "boolean",
              title: "是否可定制",
              default: true
            }
          },
          required: ["name", "type", "description", "position"]
        },
        default: [
          {
            name: "英雄区域",
            type: "hero",
            description: "页面顶部的主要展示区域，包含标题、副标题和行动按钮",
            position: 1,
            required: true,
            customizable: true
          },
          {
            name: "特性展示",
            type: "features",
            description: "展示产品或服务的核心特性和优势",
            position: 2,
            required: true,
            customizable: true
          },
          {
            name: "服务介绍",
            type: "services",
            description: "详细介绍提供的服务项目",
            position: 3,
            required: false,
            customizable: true
          },
          {
            name: "客户评价",
            type: "testimonials",
            description: "展示客户反馈和推荐",
            position: 4,
            required: false,
            customizable: true
          }
        ],
        maxItems: 20,
        minItems: 1
      },
      colorScheme: {
        type: "object",
        title: "配色方案",
        properties: {
          primary: {
            type: "string",
            title: "主色调",
            default: "#3B82F6"
          },
          secondary: {
            type: "string",
            title: "辅助色",
            default: "#10B981"
          },
          accent: {
            type: "string",
            title: "强调色",
            default: "#F59E0B"
          },
          background: {
            type: "string",
            title: "背景色",
            default: "#FFFFFF"
          },
          text: {
            type: "string",
            title: "文字色",
            default: "#1F2937"
          }
        }
      },
      features: {
        type: "array",
        title: "模版特性",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "特性名称",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "特性描述",
              maxLength: 300
            },
            category: {
              type: "string",
              title: "特性类别",
              enum: ["design", "functionality", "performance", "seo", "accessibility", "customization", "responsive"],
              default: "design"
            },
            included: {
              type: "boolean",
              title: "是否包含",
              default: true
            }
          },
          required: ["name", "description", "category"]
        },
        default: [
          {
            name: "响应式设计",
            description: "自适应各种设备屏幕尺寸",
            category: "responsive",
            included: true
          },
          {
            name: "模块化结构",
            description: "可灵活组合和调整的模块化设计",
            category: "customization",
            included: true
          },
          {
            name: "SEO友好",
            description: "优化的HTML结构和元数据",
            category: "seo",
            included: true
          }
        ],
        maxItems: 15,
        minItems: 0
      },
      technical: {
        type: "object",
        title: "技术规格",
        properties: {
          framework: {
            type: "string",
            title: "技术框架",
            enum: ["react", "vue", "angular", "html-css", "wordpress", "nextjs", "nuxtjs", "gatsby", "other"],
            default: "react"
          },
          cssFramework: {
            type: "string",
            title: "CSS框架",
            enum: ["tailwind", "bootstrap", "bulma", "foundation", "custom", "other"],
            default: "tailwind"
          },
          responsive: {
            type: "boolean",
            title: "响应式设计",
            default: true
          },
          browserSupport: {
            type: "array",
            title: "浏览器支持",
            items: {
              type: "string"
            },
            default: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"],
            maxItems: 10
          },
          performance: {
            type: "object",
            title: "性能指标",
            properties: {
              loadTime: {
                type: "string",
                title: "加载时间",
                default: "< 2秒"
              },
              lighthouseScore: {
                type: "integer",
                title: "Lighthouse评分",
                minimum: 0,
                maximum: 100,
                default: 95
              },
              mobileOptimized: {
                type: "boolean",
                title: "移动端优化",
                default: true
              }
            }
          }
        },
        required: ["framework", "cssFramework", "responsive"]
      },
      customization: {
        type: "object",
        title: "定制选项",
        properties: {
          colorCustomizable: {
            type: "boolean",
            title: "支持颜色定制",
            default: true
          },
          layoutCustomizable: {
            type: "boolean",
            title: "支持布局定制",
            default: true
          },
          contentEditable: {
            type: "boolean",
            title: "支持内容编辑",
            default: true
          },
          sectionReorderable: {
            type: "boolean",
            title: "支持模块重排",
            default: true
          },
          customCSS: {
            type: "boolean",
            title: "支持自定义CSS",
            default: false
          }
        }
      },
      pricing: {
        type: "object",
        title: "价格信息",
        properties: {
          type: {
            type: "string",
            title: "价格类型",
            enum: ["free", "premium", "subscription", "custom"],
            default: "premium"
          },
          price: {
            type: "number",
            title: "价格",
            minimum: 0,
            default: 99
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          license: {
            type: "string",
            title: "许可类型",
            enum: ["single-use", "multi-use", "commercial", "extended"],
            default: "single-use"
          }
        },
        required: ["type", "price", "currency", "license"]
      },
      demo: {
        type: "object",
        title: "演示信息",
        properties: {
          liveUrl: {
            type: "string",
            title: "在线演示",
            format: "uri"
          },
          previewImages: {
            type: "array",
            title: "预览图片",
            items: {
              type: "string",
              format: "uri"
            },
            maxItems: 10
          }
        }
      },
      images: {
        type: "array",
        title: "模版截图",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "图片类型",
              enum: ["desktop", "tablet", "mobile", "section", "full-page"],
              default: "desktop"
            },
            isPrimary: {
              type: "boolean",
              title: "是否为主图",
              default: false
            }
          },
          required: ["url", "caption", "type"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=800&fit=crop",
            caption: "现代企业首页模版 - 桌面端完整展示",
            alt: "页面模版桌面端截图",
            type: "desktop",
            isPrimary: true
          }
        ],
        maxItems: 15,
        minItems: 0
      }
    },
    required: ["title", "slug", "description", "pageType", "category", "layoutType", "designStyle", "sections", "technical", "pricing"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "pageType", "category", "layoutType", "designStyle",
      "sections", "colorScheme", "features", "technical", "customization", "pricing", "demo", "images",
      "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入页面模版标题...",
        help: "清晰描述页面模版的用途和特色"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：modern-homepage-template",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "详细描述页面模版的功能、布局和适用场景...",
        help: "详细的模版介绍，帮助用户了解模版特点"
      }
    },

    pageType: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "home", label: "🏠 首页" },
          { value: "about", label: "ℹ️ 关于页面" },
          { value: "services", label: "🛠️ 服务页面" },
          { value: "portfolio", label: "🎨 作品集页面" },
          { value: "blog", label: "📝 博客页面" },
          { value: "contact", label: "📞 联系页面" },
          { value: "pricing", label: "💰 价格页面" },
          { value: "team", label: "👥 团队页面" },
          { value: "testimonials", label: "💬 推荐页面" },
          { value: "faq", label: "❓ 常见问题页面" },
          { value: "privacy", label: "🔒 隐私政策页面" },
          { value: "terms", label: "📋 服务条款页面" },
          { value: "landing", label: "🚀 落地页" },
          { value: "product", label: "📦 产品页面" },
          { value: "other", label: "📄 其他页面" }
        ]
      }
    },

    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "business", label: "💼 商务企业" },
          { value: "portfolio", label: "🎨 作品集" },
          { value: "ecommerce", label: "🛒 电商" },
          { value: "blog", label: "📝 博客" },
          { value: "landing", label: "🚀 落地页" },
          { value: "saas", label: "☁️ SaaS" },
          { value: "agency", label: "🏢 代理机构" },
          { value: "restaurant", label: "🍽️ 餐饮" },
          { value: "education", label: "🎓 教育" },
          { value: "healthcare", label: "🏥 医疗" },
          { value: "nonprofit", label: "🤝 非营利" },
          { value: "personal", label: "👤 个人" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },

    layoutType: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "single-column", label: "📄 单列布局" },
          { value: "two-column", label: "📰 双列布局" },
          { value: "three-column", label: "📊 三列布局" },
          { value: "grid", label: "⚏ 网格布局" },
          { value: "masonry", label: "🧱 瀑布流布局" },
          { value: "hero-sections", label: "🎯 英雄模块布局" },
          { value: "full-width", label: "📐 全宽布局" },
          { value: "sidebar", label: "📋 侧边栏布局" },
          { value: "other", label: "📦 其他布局" }
        ]
      }
    },

    designStyle: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "modern", label: "🔥 现代风格" },
          { value: "minimalist", label: "✨ 极简风格" },
          { value: "corporate", label: "🏢 企业风格" },
          { value: "creative", label: "🎨 创意风格" },
          { value: "elegant", label: "💎 优雅风格" },
          { value: "bold", label: "⚡ 大胆风格" },
          { value: "classic", label: "🎩 经典风格" },
          { value: "trendy", label: "🌟 时尚风格" }
        ]
      }
    },

    sections: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加页面模块"
      },
      items: {
        "ui:order": ["name", "type", "description", "position", "required", "customizable"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "模块名称..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "hero", label: "🎯 英雄区域" },
              { value: "features", label: "⭐ 特性展示" },
              { value: "services", label: "🛠️ 服务介绍" },
              { value: "testimonials", label: "💬 客户评价" },
              { value: "team", label: "👥 团队介绍" },
              { value: "pricing", label: "💰 价格表" },
              { value: "cta", label: "📢 行动号召" },
              { value: "contact", label: "📞 联系表单" },
              { value: "gallery", label: "🖼️ 图片画廊" },
              { value: "blog", label: "📝 博客列表" },
              { value: "stats", label: "📊 数据统计" },
              { value: "faq", label: "❓ 常见问题" },
              { value: "newsletter", label: "📧 邮件订阅" },
              { value: "footer", label: "🦶 页脚" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "模块功能描述..."
          }
        },

        position: {
          "ui:widget": "updown",
          "ui:options": {
            help: "模块在页面中的显示顺序"
          }
        },

        required: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该模块是否为页面必需组件"
          }
        },

        customizable: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该模块是否支持用户定制"
          }
        }
      }
    },

    colorScheme: {
      "ui:order": ["primary", "secondary", "accent", "background", "text"],

      primary: {
        "ui:widget": "color",
        "ui:options": {
          help: "页面主色调"
        }
      },

      secondary: {
        "ui:widget": "color",
        "ui:options": {
          help: "辅助色调"
        }
      },

      accent: {
        "ui:widget": "color",
        "ui:options": {
          help: "强调色调"
        }
      },

      background: {
        "ui:widget": "color",
        "ui:options": {
          help: "背景颜色"
        }
      },

      text: {
        "ui:widget": "color",
        "ui:options": {
          help: "文字颜色"
        }
      }
    },

    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加特性"
      },
      items: {
        "ui:order": ["name", "description", "category", "included"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "特性名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "特性描述..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "design", label: "🎨 设计" },
              { value: "functionality", label: "⚙️ 功能" },
              { value: "performance", label: "⚡ 性能" },
              { value: "seo", label: "🔍 SEO" },
              { value: "accessibility", label: "♿ 无障碍" },
              { value: "customization", label: "🛠️ 定制" },
              { value: "responsive", label: "📱 响应式" }
            ]
          }
        },

        included: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该特性是否包含在模版中"
          }
        }
      }
    },

    technical: {
      "ui:order": ["framework", "cssFramework", "responsive", "browserSupport", "performance"],

      framework: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "react", label: "⚛️ React" },
            { value: "vue", label: "💚 Vue.js" },
            { value: "angular", label: "🅰️ Angular" },
            { value: "html-css", label: "🌐 HTML/CSS" },
            { value: "wordpress", label: "📝 WordPress" },
            { value: "nextjs", label: "▲ Next.js" },
            { value: "nuxtjs", label: "💚 Nuxt.js" },
            { value: "gatsby", label: "🚀 Gatsby" },
            { value: "other", label: "📦 其他" }
          ]
        }
      },

      cssFramework: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "tailwind", label: "🎨 Tailwind CSS" },
            { value: "bootstrap", label: "🅱️ Bootstrap" },
            { value: "bulma", label: "💪 Bulma" },
            { value: "foundation", label: "🏗️ Foundation" },
            { value: "custom", label: "🛠️ 自定义" },
            { value: "other", label: "📦 其他" }
          ]
        }
      },

      responsive: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否支持响应式设计"
        }
      },

      browserSupport: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加浏览器支持"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "浏览器版本..."
          }
        }
      },

      performance: {
        "ui:order": ["loadTime", "lighthouseScore", "mobileOptimized"],

        loadTime: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：< 2秒",
            help: "页面加载时间"
          }
        },

        lighthouseScore: {
          "ui:widget": "updown",
          "ui:options": {
            help: "Lighthouse性能评分（0-100）"
          }
        },

        mobileOptimized: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否针对移动端优化"
          }
        }
      }
    },

    customization: {
      "ui:order": ["colorCustomizable", "layoutCustomizable", "contentEditable", "sectionReorderable", "customCSS"],

      colorCustomizable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "用户是否可以自定义颜色"
        }
      },

      layoutCustomizable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "用户是否可以调整布局"
        }
      },

      contentEditable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "用户是否可以编辑内容"
        }
      },

      sectionReorderable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "用户是否可以重新排列模块"
        }
      },

      customCSS: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否支持自定义CSS代码"
        }
      }
    },

    pricing: {
      "ui:order": ["type", "price", "currency", "license"],

      type: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "free", label: "🆓 免费" },
            { value: "premium", label: "💎 付费" },
            { value: "subscription", label: "🔄 订阅" },
            { value: "custom", label: "🛠️ 定制" }
          ]
        }
      },

      price: {
        "ui:widget": "updown",
        "ui:options": {
          help: "页面模版价格"
        }
      },

      currency: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "CNY", label: "¥ 人民币" },
            { value: "USD", label: "$ 美元" },
            { value: "EUR", label: "€ 欧元" }
          ]
        }
      },

      license: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "single-use", label: "🏠 单次使用" },
            { value: "multi-use", label: "🏢 多次使用" },
            { value: "commercial", label: "💼 商业许可" },
            { value: "extended", label: "🚀 扩展许可" }
          ]
        }
      }
    },

    demo: {
      "ui:order": ["liveUrl", "previewImages"],

      liveUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://demo.example.com",
          help: "在线演示地址"
        }
      },

      previewImages: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加预览图片"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        }
      }
    },

    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加截图"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "type", "isPrimary"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "desktop", label: "🖥️ 桌面端" },
              { value: "tablet", label: "📱 平板端" },
              { value: "mobile", label: "📱 移动端" },
              { value: "section", label: "📋 页面片段" },
              { value: "full-page", label: "📄 完整页面" }
            ]
          }
        },

        isPrimary: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为主要展示图片"
          }
        }
      }
    }
  }
};

export default pageTemplateConfig;
