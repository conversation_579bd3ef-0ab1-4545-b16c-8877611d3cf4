import { BlockFormConfig } from '../types';

const faqConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      question: {
        type: "string",
        title: "Question",
        minLength: 5,
        maxLength: 200,
        default: "How do I get started with your platform?"
      },
      answer: {
        type: "string",
        title: "Answer",
        minLength: 10,
        default: "Getting started is easy! Simply sign up for an account, complete the onboarding process, and you'll be ready to use all our features. Our step-by-step guide will walk you through the initial setup."
      },
      category: {
        type: "string",
        title: "Category",
        minLength: 2,
        maxLength: 50,
        default: "Getting Started"
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["setup", "onboarding", "beginner"],
        maxItems: 10,
        minItems: 0
      },
      priority: {
        type: "string",
        title: "Priority",
        enum: ["low", "medium", "high", "critical"],
        default: "medium"
      },
      searchKeywords: {
        type: "array",
        title: "Search Keywords",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 50
        },
        default: ["start", "begin", "setup", "account", "registration"],
        maxItems: 15,
        minItems: 0
      },
      relatedQuestions: {
        type: "array",
        title: "Related Questions",
        items: {
          type: "object",
          properties: {
            question: {
              type: "string",
              title: "Related Question",
              minLength: 5,
              maxLength: 200
            },
            link: {
              type: "string",
              title: "Link",
              format: "uri",
              default: "#"
            }
          },
          required: ["question"]
        },
        default: [
          {
            question: "What features are included in the free plan?",
            link: "#free-plan"
          },
          {
            question: "How do I upgrade my account?",
            link: "#upgrade"
          }
        ],
        maxItems: 5,
        minItems: 0
      },
      difficulty: {
        type: "string",
        title: "Difficulty Level",
        enum: ["beginner", "intermediate", "advanced"],
        default: "beginner"
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      isPublished: {
        type: "boolean",
        title: "Published",
        default: true
      },
      viewCount: {
        type: "integer",
        title: "View Count",
        minimum: 0,
        default: 0
      },
      helpfulVotes: {
        type: "object",
        title: "Helpful Votes",
        properties: {
          positive: {
            type: "integer",
            title: "Positive Votes",
            minimum: 0,
            default: 0
          },
          negative: {
            type: "integer",
            title: "Negative Votes", 
            minimum: 0,
            default: 0
          }
        }
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "How to Get Started - FAQ | Your Platform"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Learn how to get started with our platform. Step-by-step guide for new users to set up their account and begin using our features."
          }
        }
      }
    },
    required: ["question", "answer", "category", "priority"]
  },
  uiSchema: {
    "ui:order": [
      "question", "answer", "category", "tags", "priority", 
      "searchKeywords", "relatedQuestions", "difficulty", 
      "lastUpdated", "isPublished", "viewCount", "helpfulVotes", "seo"
    ],
    
    question: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter your FAQ question...",
        help: "Write a clear, specific question that users commonly ask"
      }
    },
    
    answer: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Provide a comprehensive answer...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 400,
        minHeight: 200
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., Getting Started, Account Management",
        help: "Category to organize your FAQ items"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    priority: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "low", label: "🟢 Low Priority" },
          { value: "medium", label: "🟡 Medium Priority" },
          { value: "high", label: "🟠 High Priority" },
          { value: "critical", label: "🔴 Critical" }
        ]
      }
    },
    
    searchKeywords: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Keyword"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter search keyword..."
        }
      }
    },
    
    relatedQuestions: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Related Question"
      },
      items: {
        "ui:order": ["question", "link"],
        
        question: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter related question..."
          }
        },
        
        link: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter link or anchor (e.g., #section-id)"
          }
        }
      }
    },
    
    difficulty: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 Beginner" },
          { value: "intermediate", label: "🟡 Intermediate" },
          { value: "advanced", label: "🔴 Advanced" }
        ]
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this FAQ was last updated"
      }
    },
    
    isPublished: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Whether this FAQ is visible to users"
      }
    },
    
    viewCount: {
      "ui:widget": "updown",
      "ui:options": {
        help: "Number of times this FAQ has been viewed"
      }
    },
    
    helpfulVotes: {
      "ui:order": ["positive", "negative"],
      
      positive: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Number of users who found this helpful"
        }
      },
      
      negative: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Number of users who didn't find this helpful"
        }
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO title for this FAQ...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this FAQ...",
          help: "Description that appears in search engine results"
        }
      }
    }
  }
};

export default faqConfig;
