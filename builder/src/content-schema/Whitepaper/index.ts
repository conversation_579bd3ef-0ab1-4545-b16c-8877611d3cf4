import { BlockFormConfig } from '../types';

const whitepaperConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Whitepaper Title",
        minLength: 5,
        maxLength: 150,
        default: "The Future of Artificial Intelligence in Business: A Comprehensive Guide"
      },
      slug: {
        type: "string",
        title: "Whitepaper Slug",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "future-ai-business-guide"
      },
      subtitle: {
        type: "string",
        title: "Subtitle",
        maxLength: 200,
        default: "How AI is transforming industries and what businesses need to know to stay competitive"
      },
      abstract: {
        type: "string",
        title: "Abstract",
        minLength: 100,
        maxLength: 1000,
        default: "This whitepaper explores the current state and future potential of artificial intelligence in business applications. We examine key trends, implementation strategies, challenges, and opportunities across various industries. Based on extensive research and real-world case studies, this guide provides actionable insights for business leaders looking to leverage AI technologies for competitive advantage."
      },
      content: {
        type: "string",
        title: "Full Content",
        minLength: 500,
        default: "# Executive Summary\n\nArtificial Intelligence is no longer a futuristic concept—it's a present reality transforming how businesses operate, compete, and deliver value to customers.\n\n## Introduction\n\nThe rapid advancement of AI technologies has created unprecedented opportunities for businesses across all sectors...\n\n## Current State of AI in Business\n\n### Market Overview\n- Global AI market size: $136.6 billion in 2022\n- Expected CAGR: 37.3% from 2023 to 2030\n- Key drivers: automation, data analytics, customer experience\n\n### Industry Applications\n1. **Healthcare**: Diagnostic imaging, drug discovery, personalized medicine\n2. **Finance**: Fraud detection, algorithmic trading, risk assessment\n3. **Retail**: Recommendation engines, inventory optimization, customer service\n4. **Manufacturing**: Predictive maintenance, quality control, supply chain optimization\n\n## Implementation Strategies\n\n### Phase 1: Assessment and Planning\n- Identify use cases with highest ROI potential\n- Assess data readiness and infrastructure requirements\n- Develop AI governance framework\n\n### Phase 2: Pilot Projects\n- Start with low-risk, high-impact initiatives\n- Build internal capabilities and expertise\n- Establish success metrics and KPIs\n\n### Phase 3: Scale and Optimize\n- Expand successful pilots across the organization\n- Integrate AI into core business processes\n- Continuously monitor and improve performance\n\n## Challenges and Considerations\n\n### Technical Challenges\n- Data quality and availability\n- Model interpretability and bias\n- Integration with existing systems\n\n### Organizational Challenges\n- Skills gap and talent acquisition\n- Change management and adoption\n- Ethical considerations and compliance\n\n## Future Outlook\n\nThe next decade will see AI become increasingly sophisticated and accessible, with developments in:\n- Generative AI and large language models\n- Edge computing and real-time processing\n- Autonomous systems and robotics\n- Quantum computing integration\n\n## Conclusion\n\nBusinesses that proactively embrace AI technologies while addressing associated challenges will be best positioned to thrive in the digital economy. Success requires strategic planning, investment in capabilities, and a commitment to continuous learning and adaptation.\n\n## Recommendations\n\n1. **Start Now**: Begin with pilot projects to gain experience\n2. **Invest in Data**: Ensure high-quality, accessible data infrastructure\n3. **Build Capabilities**: Develop internal AI expertise and partnerships\n4. **Focus on Ethics**: Implement responsible AI practices from the start\n5. **Measure Impact**: Establish clear metrics and continuously optimize\n\n---\n\n*This whitepaper is based on extensive research, industry interviews, and analysis of current market trends. For more information or to discuss implementation strategies, contact our AI consulting team.*"
      },
      authors: {
        type: "array",
        title: "Authors",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Author Name",
              minLength: 2,
              maxLength: 100
            },
            title: {
              type: "string",
              title: "Professional Title",
              maxLength: 100
            },
            organization: {
              type: "string",
              title: "Organization",
              maxLength: 100
            },
            bio: {
              type: "string",
              title: "Biography",
              maxLength: 500
            },
            photo: {
              type: "string",
              title: "Photo URL",
              format: "uri"
            },
            email: {
              type: "string",
              title: "Email",
              format: "email"
            },
            linkedin: {
              type: "string",
              title: "LinkedIn Profile",
              format: "uri"
            }
          },
          required: ["name", "title", "organization"]
        },
        default: [
          {
            name: "Dr. Sarah Chen",
            title: "Chief AI Officer",
            organization: "TechVision Research",
            bio: "Dr. Chen is a leading expert in artificial intelligence with over 15 years of experience in AI research and business applications.",
            photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
            email: "<EMAIL>",
            linkedin: "https://linkedin.com/in/sarahchen"
          }
        ],
        minItems: 1,
        maxItems: 5
      },
      publisher: {
        type: "object",
        title: "Publisher Information",
        properties: {
          name: {
            type: "string",
            title: "Publisher Name",
            minLength: 2,
            maxLength: 100,
            default: "TechVision Research Institute"
          },
          logo: {
            type: "string",
            title: "Publisher Logo URL",
            format: "uri",
            default: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop"
          },
          website: {
            type: "string",
            title: "Publisher Website",
            format: "uri",
            default: "https://techvision.com"
          },
          description: {
            type: "string",
            title: "Publisher Description",
            maxLength: 300,
            default: "TechVision Research Institute is a leading technology research organization focused on emerging trends and business applications of cutting-edge technologies."
          }
        },
        required: ["name"]
      },
      category: {
        type: "string",
        title: "Category",
        minLength: 2,
        maxLength: 50,
        default: "Artificial Intelligence"
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["artificial-intelligence", "business-strategy", "digital-transformation", "technology-trends"],
        maxItems: 10,
        minItems: 0
      },
      publishDate: {
        type: "string",
        title: "Publish Date",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      pageCount: {
        type: "integer",
        title: "Page Count",
        minimum: 1,
        maximum: 500,
        default: 25
      },
      language: {
        type: "string",
        title: "Language",
        enum: ["en", "es", "fr", "de", "zh", "ja", "ko"],
        default: "en"
      },
      downloadInfo: {
        type: "object",
        title: "Download Information",
        properties: {
          fileUrl: {
            type: "string",
            title: "PDF File URL",
            format: "uri",
            default: "https://example.com/whitepapers/ai-business-guide.pdf"
          },
          fileSize: {
            type: "string",
            title: "File Size",
            default: "2.5 MB"
          },
          requiresRegistration: {
            type: "boolean",
            title: "Requires Registration",
            default: true
          },
          downloadCount: {
            type: "integer",
            title: "Download Count",
            minimum: 0,
            default: 0
          }
        },
        required: ["fileUrl"]
      },
      isPublished: {
        type: "boolean",
        title: "Published",
        default: false
      },
      isFeatured: {
        type: "boolean",
        title: "Featured Whitepaper",
        default: false
      },
      accessLevel: {
        type: "string",
        title: "Access Level",
        enum: ["public", "registered", "premium", "enterprise"],
        default: "registered"
      },
      relatedContent: {
        type: "array",
        title: "Related Content",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "Content Title",
              minLength: 2,
              maxLength: 150
            },
            type: {
              type: "string",
              title: "Content Type",
              enum: ["whitepaper", "case-study", "blog-post", "webinar", "video"],
              default: "whitepaper"
            },
            url: {
              type: "string",
              title: "URL",
              format: "uri"
            }
          },
          required: ["title", "type", "url"]
        },
        default: [],
        maxItems: 5,
        minItems: 0
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "The Future of AI in Business - Free Whitepaper Download"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Download our comprehensive whitepaper on AI in business. Learn about implementation strategies, challenges, and future opportunities. Free PDF guide available."
          },
          keywords: {
            type: "array",
            title: "SEO Keywords",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["AI whitepaper", "artificial intelligence business", "AI implementation guide", "business AI strategy"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "abstract", "content", "authors", "publisher", "category", "downloadInfo"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "subtitle", "abstract", "content", "authors", "publisher",
      "category", "tags", "publishDate", "lastUpdated", "pageCount", "language",
      "downloadInfo", "isPublished", "isFeatured", "accessLevel", "relatedContent", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter whitepaper title...",
        help: "A compelling title that clearly communicates the value proposition"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., future-ai-business-guide",
        help: "URL-friendly identifier (lowercase letters, numbers, and hyphens only)"
      }
    },
    
    subtitle: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Optional subtitle to provide additional context...",
        help: "A descriptive subtitle that expands on the main title"
      }
    },
    
    abstract: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "Write a compelling abstract that summarizes the whitepaper...",
        help: "A concise summary that highlights key findings and value"
      }
    },
    
    content: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Write the full whitepaper content...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 600,
        minHeight: 400
      }
    },
    
    authors: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Author"
      },
      items: {
        "ui:order": ["name", "title", "organization", "bio", "photo", "email", "linkedin"],
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Author full name..."
          }
        },
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Professional title..."
          }
        },
        
        organization: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Organization or company..."
          }
        },
        
        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "Brief author biography..."
          }
        },
        
        photo: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Author photo URL..."
          }
        },
        
        email: {
          "ui:widget": "email",
          "ui:options": {
            placeholder: "<EMAIL>"
          }
        },
        
        linkedin: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "LinkedIn profile URL..."
          }
        }
      }
    },
    
    publisher: {
      "ui:order": ["name", "logo", "website", "description"],
      
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Publisher organization name..."
        }
      },
      
      logo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Publisher logo URL..."
        }
      },
      
      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Publisher website URL..."
        }
      },
      
      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "Brief description of the publisher..."
        }
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., Artificial Intelligence, Digital Marketing, Cybersecurity"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    publishDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this whitepaper was published"
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this whitepaper was last updated"
      }
    },
    
    pageCount: {
      "ui:widget": "updown",
      "ui:options": {
        help: "Number of pages in the PDF"
      }
    },
    
    language: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "en", label: "English" },
          { value: "es", label: "Spanish" },
          { value: "fr", label: "French" },
          { value: "de", label: "German" },
          { value: "zh", label: "Chinese" },
          { value: "ja", label: "Japanese" },
          { value: "ko", label: "Korean" }
        ]
      }
    },
    
    downloadInfo: {
      "ui:order": ["fileUrl", "fileSize", "requiresRegistration", "downloadCount"],
      
      fileUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://example.com/whitepaper.pdf"
        }
      },
      
      fileSize: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., 2.5 MB, 1.2 GB"
        }
      },
      
      requiresRegistration: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "Whether users need to register to download"
        }
      },
      
      downloadCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Number of times this whitepaper has been downloaded"
        }
      }
    },
    
    isPublished: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Whether this whitepaper is visible to users"
      }
    },
    
    isFeatured: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Feature this whitepaper on the homepage"
      }
    },
    
    accessLevel: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "public", label: "🌍 Public - Anyone can access" },
          { value: "registered", label: "📝 Registered - Requires registration" },
          { value: "premium", label: "⭐ Premium - Requires premium account" },
          { value: "enterprise", label: "🏢 Enterprise - Enterprise customers only" }
        ]
      }
    },
    
    relatedContent: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Related Content"
      },
      items: {
        "ui:order": ["title", "type", "url"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Related content title..."
          }
        },
        
        type: {
          "ui:widget": "select"
        },
        
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Content URL..."
          }
        }
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO title for this whitepaper...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this whitepaper...",
          help: "Description that appears in search engine results"
        }
      },
      
      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Keyword"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter SEO keyword..."
          }
        }
      }
    }
  }
};

export default whitepaperConfig;
