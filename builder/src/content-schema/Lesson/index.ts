import { BlockFormConfig } from '../types';

const lessonConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "课时标题",
        minLength: 5,
        maxLength: 150,
        default: "React组件基础：创建你的第一个组件"
      },
      slug: {
        type: "string",
        title: "课时标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "react-components-basics"
      },
      description: {
        type: "string",
        title: "课时描述",
        minLength: 20,
        maxLength: 500,
        default: "在这节课中，你将学习React组件的基本概念，了解函数组件和类组件的区别，并动手创建你的第一个React组件。通过实际编码练习，掌握组件的创建、使用和基本属性传递。"
      },
      courseId: {
        type: "string",
        title: "所属课程ID",
        minLength: 1,
        default: "react-fullstack-development"
      },
      moduleId: {
        type: "string",
        title: "所属模块ID",
        minLength: 1,
        default: "react-basics"
      },
      order: {
        type: "integer",
        title: "课时顺序",
        minimum: 1,
        default: 1
      },
      type: {
        type: "string",
        title: "课时类型",
        enum: ["video", "text", "quiz", "assignment", "live", "interactive"],
        default: "video"
      },
      content: {
        type: "string",
        title: "课时内容",
        minLength: 50,
        default: "# React组件基础\n\n## 什么是React组件？\n\nReact组件是构建用户界面的基本单元。每个组件都是一个独立的、可复用的代码片段，它接收输入（称为props）并返回描述UI外观的React元素。\n\n## 函数组件 vs 类组件\n\n### 函数组件\n```jsx\nfunction Welcome(props) {\n  return <h1>Hello, {props.name}!</h1>;\n}\n```\n\n### 类组件\n```jsx\nclass Welcome extends React.Component {\n  render() {\n    return <h1>Hello, {this.props.name}!</h1>;\n  }\n}\n```\n\n## 实践练习\n\n现在让我们创建一个简单的用户卡片组件：\n\n```jsx\nfunction UserCard({ name, email, avatar }) {\n  return (\n    <div className=\"user-card\">\n      <img src={avatar} alt={name} />\n      <h3>{name}</h3>\n      <p>{email}</p>\n    </div>\n  );\n}\n```\n\n## 小结\n\n在这节课中，我们学习了：\n- React组件的基本概念\n- 函数组件和类组件的语法\n- 如何创建和使用组件\n- Props的基本用法\n\n下一节课，我们将深入学习组件的状态管理。"
      },
      duration: {
        type: "integer",
        title: "课时时长(分钟)",
        minimum: 1,
        maximum: 180,
        default: 25
      },
      difficulty: {
        type: "string",
        title: "难度级别",
        enum: ["beginner", "intermediate", "advanced"],
        default: "beginner"
      },
      learningObjectives: {
        type: "array",
        title: "学习目标",
        items: {
          type: "string",
          minLength: 10,
          maxLength: 200
        },
        default: [
          "理解React组件的基本概念和作用",
          "掌握函数组件和类组件的语法差异",
          "能够创建简单的React组件",
          "学会使用props传递数据"
        ],
        maxItems: 8,
        minItems: 1
      },
      prerequisites: {
        type: "array",
        title: "前置知识",
        items: {
          type: "string",
          minLength: 5,
          maxLength: 100
        },
        default: [
          "JavaScript ES6基础语法",
          "HTML和CSS基础知识"
        ],
        maxItems: 5,
        minItems: 0
      },
      resources: {
        type: "array",
        title: "学习资源",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "资源标题",
              minLength: 2,
              maxLength: 100
            },
            type: {
              type: "string",
              title: "资源类型",
              enum: ["video", "document", "code", "link", "download"],
              default: "document"
            },
            url: {
              type: "string",
              title: "资源链接",
              format: "uri"
            },
            description: {
              type: "string",
              title: "资源描述",
              maxLength: 200
            },
            duration: {
              type: "integer",
              title: "时长(分钟)",
              minimum: 1,
              maximum: 120
            },
            isRequired: {
              type: "boolean",
              title: "是否必需",
              default: true
            }
          },
          required: ["title", "type", "url"]
        },
        default: [
          {
            title: "React官方文档 - 组件",
            type: "link",
            url: "https://react.dev/learn/your-first-component",
            description: "React官方文档中关于组件的详细说明",
            isRequired: false
          },
          {
            title: "课时练习代码",
            type: "code",
            url: "https://github.com/example/react-components-exercise",
            description: "本课时的练习代码和示例",
            isRequired: true
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      exercises: {
        type: "array",
        title: "练习题",
        items: {
          type: "object",
          properties: {
            question: {
              type: "string",
              title: "题目",
              minLength: 10,
              maxLength: 500
            },
            type: {
              type: "string",
              title: "题目类型",
              enum: ["multiple-choice", "true-false", "coding", "short-answer"],
              default: "multiple-choice"
            },
            options: {
              type: "array",
              title: "选项",
              items: {
                type: "string",
                minLength: 1,
                maxLength: 200
              },
              maxItems: 6,
              minItems: 2
            },
            correctAnswer: {
              type: "string",
              title: "正确答案",
              maxLength: 1000
            },
            explanation: {
              type: "string",
              title: "答案解释",
              maxLength: 500
            },
            points: {
              type: "integer",
              title: "分值",
              minimum: 1,
              maximum: 100,
              default: 10
            }
          },
          required: ["question", "type", "correctAnswer"]
        },
        default: [
          {
            question: "以下哪个是创建React函数组件的正确语法？",
            type: "multiple-choice",
            options: [
              "function MyComponent() { return <div>Hello</div>; }",
              "const MyComponent = () => { return <div>Hello</div>; }",
              "class MyComponent extends Component { render() { return <div>Hello</div>; } }",
              "以上都正确"
            ],
            correctAnswer: "以上都正确",
            explanation: "React支持多种创建组件的方式，包括函数声明、箭头函数和类组件。",
            points: 10
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      videoInfo: {
        type: "object",
        title: "视频信息",
        properties: {
          videoUrl: {
            type: "string",
            title: "视频链接",
            format: "uri",
            default: "https://example.com/videos/react-components-basics.mp4"
          },
          thumbnailUrl: {
            type: "string",
            title: "视频缩略图",
            format: "uri",
            default: "https://example.com/thumbnails/react-components-basics.jpg"
          },
          subtitles: {
            type: "array",
            title: "字幕文件",
            items: {
              type: "object",
              properties: {
                language: {
                  type: "string",
                  title: "语言",
                  enum: ["zh-CN", "en", "ja", "ko"],
                  default: "zh-CN"
                },
                url: {
                  type: "string",
                  title: "字幕文件URL",
                  format: "uri"
                }
              },
              required: ["language", "url"]
            },
            default: [
              {
                language: "zh-CN",
                url: "https://example.com/subtitles/react-components-basics-zh.vtt"
              }
            ],
            maxItems: 5
          },
          chapters: {
            type: "array",
            title: "视频章节",
            items: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  title: "章节标题",
                  minLength: 2,
                  maxLength: 100
                },
                startTime: {
                  type: "integer",
                  title: "开始时间(秒)",
                  minimum: 0
                },
                endTime: {
                  type: "integer",
                  title: "结束时间(秒)",
                  minimum: 0
                }
              },
              required: ["title", "startTime"]
            },
            default: [
              {
                title: "课程介绍",
                startTime: 0,
                endTime: 120
              },
              {
                title: "组件基础概念",
                startTime: 120,
                endTime: 600
              },
              {
                title: "实践练习",
                startTime: 600,
                endTime: 1200
              }
            ],
            maxItems: 20
          }
        }
      },
      completionCriteria: {
        type: "object",
        title: "完成条件",
        properties: {
          watchPercentage: {
            type: "integer",
            title: "观看完成度(%)",
            minimum: 50,
            maximum: 100,
            default: 80
          },
          exerciseScore: {
            type: "integer",
            title: "练习最低分数",
            minimum: 0,
            maximum: 100,
            default: 70
          },
          requireAllResources: {
            type: "boolean",
            title: "需要查看所有必需资源",
            default: true
          }
        }
      },
      tags: {
        type: "array",
        title: "标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["React", "组件", "基础", "前端"],
        maxItems: 10,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "published", "archived"],
        default: "draft"
      },
      isFree: {
        type: "boolean",
        title: "免费试听",
        default: false
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      viewCount: {
        type: "integer",
        title: "观看次数",
        minimum: 0,
        default: 0
      },
      completionRate: {
        type: "number",
        title: "完成率(%)",
        minimum: 0,
        maximum: 100,
        default: 0
      },
      rating: {
        type: "object",
        title: "课时评分",
        properties: {
          average: {
            type: "number",
            title: "平均评分",
            minimum: 0,
            maximum: 5,
            default: 0
          },
          count: {
            type: "integer",
            title: "评分人数",
            minimum: 0,
            default: 0
          }
        }
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "React组件基础：创建你的第一个组件 - 在线课程"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "学习React组件的基本概念，掌握函数组件和类组件的创建方法。通过实际练习，快速入门React组件开发。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["React组件", "函数组件", "类组件", "前端开发"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "courseId", "moduleId", "order", "type", "content", "duration", "learningObjectives"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "courseId", "moduleId", "order", "type",
      "content", "duration", "difficulty", "learningObjectives", "prerequisites",
      "resources", "exercises", "videoInfo", "completionCriteria", "tags",
      "status", "isFree", "publishDate", "lastUpdated", "viewCount",
      "completionRate", "rating", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入课时标题...",
        help: "简洁明确的课时标题，突出本节课的核心内容"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：react-components-basics",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "描述这节课的主要内容和学习收获...",
        help: "吸引学员的课时介绍"
      }
    },
    
    courseId: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "所属课程的ID",
        help: "这节课属于哪个课程"
      }
    },
    
    moduleId: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "所属模块的ID",
        help: "这节课属于课程中的哪个模块"
      }
    },
    
    order: {
      "ui:widget": "updown",
      "ui:options": {
        help: "在模块中的排序位置"
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "video", label: "📹 视频课程" },
          { value: "text", label: "📄 图文教程" },
          { value: "quiz", label: "📝 测验练习" },
          { value: "assignment", label: "📋 作业任务" },
          { value: "live", label: "🎥 直播课程" },
          { value: "interactive", label: "🎮 互动练习" }
        ]
      }
    },
    
    content: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "编写课时的详细内容...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 500,
        minHeight: 300
      }
    },
    
    duration: {
      "ui:widget": "updown",
      "ui:options": {
        help: "课时时长（分钟）"
      }
    },
    
    difficulty: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 初级" },
          { value: "intermediate", label: "🟡 中级" },
          { value: "advanced", label: "🟠 高级" }
        ]
      }
    },

    learningObjectives: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加学习目标"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "描述学员完成这节课后能够掌握的技能..."
        }
      }
    },

    prerequisites: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加前置知识"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "这节课需要的前置知识..."
        }
      }
    },

    resources: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加学习资源"
      },
      items: {
        "ui:order": ["title", "type", "url", "description", "duration", "isRequired"],

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "资源标题..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "video", label: "📹 视频" },
              { value: "document", label: "📄 文档" },
              { value: "code", label: "💻 代码" },
              { value: "link", label: "🔗 链接" },
              { value: "download", label: "📥 下载" }
            ]
          }
        },

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "资源链接..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "资源描述..."
          }
        },

        duration: {
          "ui:widget": "updown",
          "ui:options": {
            help: "资源时长（分钟）"
          }
        },

        isRequired: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为必需资源"
          }
        }
      }
    },

    exercises: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加练习题"
      },
      items: {
        "ui:order": ["question", "type", "options", "correctAnswer", "explanation", "points"],

        question: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "输入题目..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "multiple-choice", label: "选择题" },
              { value: "true-false", label: "判断题" },
              { value: "coding", label: "编程题" },
              { value: "short-answer", label: "简答题" }
            ]
          }
        },

        options: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加选项"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "选项内容..."
            }
          }
        },

        correctAnswer: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "正确答案..."
          }
        },

        explanation: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "答案解释..."
          }
        },

        points: {
          "ui:widget": "updown",
          "ui:options": {
            help: "题目分值"
          }
        }
      }
    },

    videoInfo: {
      "ui:order": ["videoUrl", "thumbnailUrl", "subtitles", "chapters"],

      videoUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "视频文件URL..."
        }
      },

      thumbnailUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "视频缩略图URL..."
        }
      },

      subtitles: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加字幕"
        },
        items: {
          "ui:order": ["language", "url"],

          language: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "zh-CN", label: "中文" },
                { value: "en", label: "English" },
                { value: "ja", label: "日本語" },
                { value: "ko", label: "한국어" }
              ]
            }
          },

          url: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "字幕文件URL..."
            }
          }
        }
      },

      chapters: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加章节"
        },
        items: {
          "ui:order": ["title", "startTime", "endTime"],

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "章节标题..."
            }
          },

          startTime: {
            "ui:widget": "updown",
            "ui:options": {
              help: "开始时间（秒）"
            }
          },

          endTime: {
            "ui:widget": "updown",
            "ui:options": {
              help: "结束时间（秒）"
            }
          }
        }
      }
    },

    completionCriteria: {
      "ui:order": ["watchPercentage", "exerciseScore", "requireAllResources"],

      watchPercentage: {
        "ui:widget": "updown",
        "ui:options": {
          help: "需要观看的百分比"
        }
      },

      exerciseScore: {
        "ui:widget": "updown",
        "ui:options": {
          help: "练习题最低分数"
        }
      },

      requireAllResources: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否需要查看所有必需资源"
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    isFree: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "是否免费试听"
      }
    },

    publishDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "课时发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    viewCount: {
      "ui:widget": "updown",
      "ui:options": {
        help: "观看次数"
      }
    },

    completionRate: {
      "ui:widget": "updown",
      "ui:options": {
        step: 0.1,
        help: "完成率百分比"
      }
    },

    rating: {
      "ui:order": ["average", "count"],

      average: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "平均评分（0-5分）"
        }
      },

      count: {
        "ui:widget": "updown",
        "ui:options": {
          help: "评分人数"
        }
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default lessonConfig;
