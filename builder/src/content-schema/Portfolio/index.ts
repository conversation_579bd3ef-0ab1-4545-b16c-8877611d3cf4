import { BlockFormConfig } from '../types';

const portfolioConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "项目标题",
        minLength: 5,
        maxLength: 150,
        default: "智能客服系统 - AI驱动的多渠道客户服务平台"
      },
      slug: {
        type: "string",
        title: "项目标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "ai-customer-service-platform"
      },
      description: {
        type: "string",
        title: "项目描述",
        minLength: 50,
        maxLength: 2000,
        default: "这是一个基于人工智能技术的智能客服系统，支持多渠道接入（网页、微信、APP），具备自然语言理解、智能问答、情感分析等功能。系统采用微服务架构，支持高并发访问，为企业提供7x24小时的智能客户服务。"
      },
      category: {
        type: "string",
        title: "项目分类",
        enum: ["web-development", "mobile-app", "desktop-app", "ai-ml", "data-science", "design", "game", "blockchain", "iot", "other"],
        default: "ai-ml"
      },
      type: {
        type: "string",
        title: "项目类型",
        enum: ["personal", "commercial", "open-source", "academic", "freelance", "team"],
        default: "commercial"
      },
      status: {
        type: "string",
        title: "项目状态",
        enum: ["planning", "in-progress", "completed", "maintenance", "archived"],
        default: "completed"
      },
      startDate: {
        type: "string",
        title: "开始日期",
        format: "date",
        default: "2024-01-15"
      },
      endDate: {
        type: "string",
        title: "结束日期",
        format: "date",
        default: "2024-06-30"
      },
      duration: {
        type: "string",
        title: "项目周期",
        maxLength: 50,
        default: "6个月"
      },
      technologies: {
        type: "array",
        title: "技术栈",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "技术名称",
              minLength: 2,
              maxLength: 50
            },
            category: {
              type: "string",
              title: "技术分类",
              enum: ["frontend", "backend", "database", "devops", "ai-ml", "design", "testing", "other"],
              default: "frontend"
            },
            proficiency: {
              type: "string",
              title: "熟练程度",
              enum: ["beginner", "intermediate", "advanced", "expert"],
              default: "intermediate"
            },
            version: {
              type: "string",
              title: "版本",
              maxLength: 20
            }
          },
          required: ["name", "category", "proficiency"]
        },
        default: [
          {
            name: "React",
            category: "frontend",
            proficiency: "advanced",
            version: "18.2"
          },
          {
            name: "Node.js",
            category: "backend",
            proficiency: "advanced",
            version: "20.x"
          },
          {
            name: "Python",
            category: "ai-ml",
            proficiency: "expert",
            version: "3.11"
          },
          {
            name: "TensorFlow",
            category: "ai-ml",
            proficiency: "advanced",
            version: "2.13"
          },
          {
            name: "MongoDB",
            category: "database",
            proficiency: "intermediate",
            version: "7.0"
          }
        ],
        maxItems: 20,
        minItems: 1
      },
      features: {
        type: "array",
        title: "核心功能",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "功能标题",
              minLength: 5,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "功能描述",
              maxLength: 500
            },
            priority: {
              type: "string",
              title: "优先级",
              enum: ["low", "medium", "high", "critical"],
              default: "medium"
            },
            status: {
              type: "string",
              title: "实现状态",
              enum: ["planned", "in-progress", "completed", "testing"],
              default: "completed"
            }
          },
          required: ["title", "description", "priority", "status"]
        },
        default: [
          {
            title: "智能问答系统",
            description: "基于NLP技术的智能问答，支持多轮对话和上下文理解",
            priority: "critical",
            status: "completed"
          },
          {
            title: "多渠道接入",
            description: "支持网页、微信、APP等多种渠道的统一接入",
            priority: "high",
            status: "completed"
          },
          {
            title: "情感分析",
            description: "实时分析用户情感，提供个性化服务",
            priority: "medium",
            status: "completed"
          },
          {
            title: "数据分析仪表板",
            description: "提供客服数据的可视化分析和报表",
            priority: "medium",
            status: "completed"
          }
        ],
        maxItems: 15,
        minItems: 1
      },
      links: {
        type: "object",
        title: "项目链接",
        properties: {
          liveDemo: {
            type: "string",
            title: "在线演示",
            format: "uri",
            default: "https://demo.ai-customer-service.com"
          },
          repository: {
            type: "string",
            title: "代码仓库",
            format: "uri",
            default: "https://github.com/username/ai-customer-service"
          },
          documentation: {
            type: "string",
            title: "项目文档",
            format: "uri",
            default: "https://docs.ai-customer-service.com"
          },
          presentation: {
            type: "string",
            title: "项目展示",
            format: "uri"
          },
          video: {
            type: "string",
            title: "演示视频",
            format: "uri"
          }
        }
      },
      media: {
        type: "object",
        title: "媒体资源",
        properties: {
          screenshots: {
            type: "array",
            title: "项目截图",
            items: {
              type: "object",
              properties: {
                url: {
                  type: "string",
                  title: "图片URL",
                  format: "uri"
                },
                caption: {
                  type: "string",
                  title: "图片说明",
                  maxLength: 200
                },
                alt: {
                  type: "string",
                  title: "替代文本",
                  maxLength: 100
                }
              },
              required: ["url", "caption"]
            },
            default: [
              {
                url: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop",
                caption: "智能客服系统主界面",
                alt: "AI客服系统主界面截图"
              },
              {
                url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop",
                caption: "数据分析仪表板",
                alt: "客服数据分析仪表板截图"
              }
            ],
            maxItems: 10,
            minItems: 0
          },
          videos: {
            type: "array",
            title: "演示视频",
            items: {
              type: "object",
              properties: {
                url: {
                  type: "string",
                  title: "视频URL",
                  format: "uri"
                },
                title: {
                  type: "string",
                  title: "视频标题",
                  maxLength: 100
                },
                duration: {
                  type: "integer",
                  title: "视频时长(秒)",
                  minimum: 1,
                  maximum: 3600
                },
                thumbnail: {
                  type: "string",
                  title: "视频缩略图",
                  format: "uri"
                }
              },
              required: ["url", "title"]
            },
            maxItems: 5,
            minItems: 0
          }
        }
      },
      achievements: {
        type: "object",
        title: "项目成果",
        properties: {
          metrics: {
            type: "array",
            title: "关键指标",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  title: "指标名称",
                  maxLength: 50
                },
                value: {
                  type: "string",
                  title: "指标值",
                  maxLength: 50
                },
                unit: {
                  type: "string",
                  title: "单位",
                  maxLength: 20
                },
                description: {
                  type: "string",
                  title: "指标说明",
                  maxLength: 200
                }
              },
              required: ["name", "value"]
            },
            default: [
              {
                name: "响应时间",
                value: "< 500ms",
                unit: "毫秒",
                description: "平均API响应时间"
              },
              {
                name: "准确率",
                value: "95%",
                unit: "百分比",
                description: "智能问答准确率"
              },
              {
                name: "用户满意度",
                value: "4.8",
                unit: "分",
                description: "用户评分（5分制）"
              }
            ],
            maxItems: 10,
            minItems: 0
          },
          awards: {
            type: "array",
            title: "获得奖项",
            items: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  title: "奖项名称",
                  maxLength: 100
                },
                organization: {
                  type: "string",
                  title: "颁发机构",
                  maxLength: 100
                },
                date: {
                  type: "string",
                  title: "获奖日期",
                  format: "date"
                },
                description: {
                  type: "string",
                  title: "奖项描述",
                  maxLength: 300
                }
              },
              required: ["title", "organization", "date"]
            },
            maxItems: 5,
            minItems: 0
          },
          recognition: {
            type: "array",
            title: "媒体报道",
            items: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  title: "报道标题",
                  maxLength: 150
                },
                media: {
                  type: "string",
                  title: "媒体名称",
                  maxLength: 100
                },
                url: {
                  type: "string",
                  title: "报道链接",
                  format: "uri"
                },
                date: {
                  type: "string",
                  title: "报道日期",
                  format: "date"
                }
              },
              required: ["title", "media", "url", "date"]
            },
            maxItems: 5,
            minItems: 0
          }
        }
      },
      team: {
        type: "array",
        title: "团队成员",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "姓名",
              minLength: 2,
              maxLength: 50
            },
            role: {
              type: "string",
              title: "角色",
              maxLength: 50
            },
            avatar: {
              type: "string",
              title: "头像",
              format: "uri"
            },
            bio: {
              type: "string",
              title: "个人简介",
              maxLength: 300
            },
            contribution: {
              type: "string",
              title: "主要贡献",
              maxLength: 300
            },
            social: {
              type: "object",
              title: "社交链接",
              properties: {
                linkedin: {
                  type: "string",
                  title: "LinkedIn",
                  format: "uri"
                },
                github: {
                  type: "string",
                  title: "GitHub",
                  format: "uri"
                },
                website: {
                  type: "string",
                  title: "个人网站",
                  format: "uri"
                }
              }
            }
          },
          required: ["name", "role"]
        },
        default: [
          {
            name: "张三",
            role: "项目负责人 & 全栈工程师",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            bio: "资深全栈工程师，专注于AI技术和Web开发",
            contribution: "负责整体架构设计、AI模型训练和前端开发",
            social: {
              github: "https://github.com/zhangsan",
              linkedin: "https://linkedin.com/in/zhangsan",
              website: "https://zhangsan.dev"
            }
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      tags: {
        type: "array",
        title: "项目标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["AI", "客服系统", "React", "Node.js", "机器学习"],
        maxItems: 15,
        minItems: 0
      },
      visibility: {
        type: "string",
        title: "可见性",
        enum: ["public", "private", "portfolio-only"],
        default: "public"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "智能客服系统 - AI驱动的多渠道客户服务平台 | 项目作品集"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "基于人工智能技术的智能客服系统，支持多渠道接入，具备自然语言理解、智能问答、情感分析等功能。查看项目详情和技术实现。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["AI客服", "智能客服系统", "React项目", "机器学习应用"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "category", "type", "status", "technologies", "features"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "category", "type", "status",
      "startDate", "endDate", "duration", "technologies", "features",
      "links", "media", "achievements", "team", "tags", "visibility",
      "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入项目标题...",
        help: "简洁明确的项目名称，突出核心价值"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：ai-customer-service-platform",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "详细描述项目背景、目标、解决的问题和技术亮点...",
        help: "详细的项目介绍，有助于展示项目价值"
      }
    },
    
    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "web-development", label: "🌐 Web开发" },
          { value: "mobile-app", label: "📱 移动应用" },
          { value: "desktop-app", label: "💻 桌面应用" },
          { value: "ai-ml", label: "🤖 AI/机器学习" },
          { value: "data-science", label: "📊 数据科学" },
          { value: "design", label: "🎨 设计" },
          { value: "game", label: "🎮 游戏开发" },
          { value: "blockchain", label: "⛓️ 区块链" },
          { value: "iot", label: "🌐 物联网" },
          { value: "other", label: "🔧 其他" }
        ]
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "personal", label: "👤 个人项目" },
          { value: "commercial", label: "💼 商业项目" },
          { value: "open-source", label: "🔓 开源项目" },
          { value: "academic", label: "🎓 学术项目" },
          { value: "freelance", label: "💰 自由职业" },
          { value: "team", label: "👥 团队项目" }
        ]
      }
    },
    
    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "planning", label: "📋 规划中" },
          { value: "in-progress", label: "🚧 进行中" },
          { value: "completed", label: "✅ 已完成" },
          { value: "maintenance", label: "🔧 维护中" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    startDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "项目开始日期"
      }
    },

    endDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "项目结束日期"
      }
    },

    duration: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：6个月、3周、1年",
        help: "项目持续时间"
      }
    },

    technologies: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加技术"
      },
      items: {
        "ui:order": ["name", "category", "proficiency", "version"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "技术名称..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "frontend", label: "前端" },
              { value: "backend", label: "后端" },
              { value: "database", label: "数据库" },
              { value: "devops", label: "运维" },
              { value: "ai-ml", label: "AI/机器学习" },
              { value: "design", label: "设计" },
              { value: "testing", label: "测试" },
              { value: "other", label: "其他" }
            ]
          }
        },

        proficiency: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "beginner", label: "初学者" },
              { value: "intermediate", label: "中级" },
              { value: "advanced", label: "高级" },
              { value: "expert", label: "专家" }
            ]
          }
        },

        version: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "版本号..."
          }
        }
      }
    },

    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加功能"
      },
      items: {
        "ui:order": ["title", "description", "priority", "status"],

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "功能标题..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "功能详细描述..."
          }
        },

        priority: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "low", label: "🟢 低" },
              { value: "medium", label: "🟡 中" },
              { value: "high", label: "🟠 高" },
              { value: "critical", label: "🔴 关键" }
            ]
          }
        },

        status: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "planned", label: "📋 计划中" },
              { value: "in-progress", label: "🚧 开发中" },
              { value: "completed", label: "✅ 已完成" },
              { value: "testing", label: "🧪 测试中" }
            ]
          }
        }
      }
    },

    links: {
      "ui:order": ["liveDemo", "repository", "documentation", "presentation", "video"],

      liveDemo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "在线演示链接..."
        }
      },

      repository: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "代码仓库链接..."
        }
      },

      documentation: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "项目文档链接..."
        }
      },

      presentation: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "项目展示链接..."
        }
      },

      video: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "演示视频链接..."
        }
      }
    },

    media: {
      "ui:order": ["screenshots", "videos"],

      screenshots: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加截图"
        },
        items: {
          "ui:order": ["url", "caption", "alt"],

          url: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "图片URL..."
            }
          },

          caption: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "图片说明..."
            }
          },

          alt: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "替代文本..."
            }
          }
        }
      },

      videos: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加视频"
        },
        items: {
          "ui:order": ["url", "title", "duration", "thumbnail"],

          url: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "视频URL..."
            }
          },

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "视频标题..."
            }
          },

          duration: {
            "ui:widget": "updown",
            "ui:options": {
              help: "视频时长（秒）"
            }
          },

          thumbnail: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "视频缩略图URL..."
            }
          }
        }
      }
    },

    achievements: {
      "ui:order": ["metrics", "awards", "recognition"],

      metrics: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加指标"
        },
        items: {
          "ui:order": ["name", "value", "unit", "description"],

          name: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "指标名称..."
            }
          },

          value: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "指标值..."
            }
          },

          unit: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "单位..."
            }
          },

          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "指标说明..."
            }
          }
        }
      },

      awards: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加奖项"
        },
        items: {
          "ui:order": ["title", "organization", "date", "description"],

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "奖项名称..."
            }
          },

          organization: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "颁发机构..."
            }
          },

          date: {
            "ui:widget": "date",
            "ui:options": {
              help: "获奖日期"
            }
          },

          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "奖项描述..."
            }
          }
        }
      },

      recognition: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加媒体报道"
        },
        items: {
          "ui:order": ["title", "media", "url", "date"],

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "报道标题..."
            }
          },

          media: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "媒体名称..."
            }
          },

          url: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "报道链接..."
            }
          },

          date: {
            "ui:widget": "date",
            "ui:options": {
              help: "报道日期"
            }
          }
        }
      }
    },

    team: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加团队成员"
      },
      items: {
        "ui:order": ["name", "role", "avatar", "bio", "contribution", "social"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "成员姓名..."
          }
        },

        role: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "角色职位..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "头像图片URL..."
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "个人简介..."
          }
        },

        contribution: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "主要贡献..."
          }
        },

        social: {
          "ui:order": ["linkedin", "github", "website"],

          linkedin: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "LinkedIn链接..."
            }
          },

          github: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "GitHub链接..."
            }
          },

          website: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "个人网站..."
            }
          }
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    visibility: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "public", label: "🌍 公开" },
          { value: "private", label: "🔒 私有" },
          { value: "portfolio-only", label: "📁 仅作品集" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "项目发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default portfolioConfig;
