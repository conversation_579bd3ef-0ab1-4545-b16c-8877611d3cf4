import { BlockFormConfig } from '../types';

const websiteTemplateConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "模版标题",
        minLength: 5,
        maxLength: 100,
        default: "现代商务网站模版 - 专业企业展示解决方案"
      },
      slug: {
        type: "string",
        title: "模版标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "modern-business-website-template"
      },
      description: {
        type: "string",
        title: "模版描述",
        minLength: 50,
        maxLength: 1000,
        default: "这是一个现代化的商务网站模版，专为中小企业和创业公司设计。包含首页、关于我们、服务介绍、案例展示、联系我们等完整页面，响应式设计，支持多种定制选项。"
      },
      category: {
        type: "string",
        title: "模版分类",
        enum: ["business", "portfolio", "ecommerce", "blog", "landing", "saas", "agency", "restaurant", "education", "healthcare", "nonprofit", "personal", "other"],
        default: "business"
      },
      industry: {
        type: "array",
        title: "适用行业",
        items: {
          type: "string",
          enum: ["technology", "finance", "healthcare", "education", "retail", "manufacturing", "consulting", "marketing", "real-estate", "food-beverage", "travel", "fitness", "legal", "nonprofit", "other"]
        },
        default: ["technology", "consulting"],
        minItems: 1,
        maxItems: 5
      },
      designStyle: {
        type: "string",
        title: "设计风格",
        enum: ["modern", "minimalist", "corporate", "creative", "elegant", "bold", "classic", "trendy"],
        default: "modern"
      },
      colorScheme: {
        type: "object",
        title: "配色方案",
        properties: {
          primary: {
            type: "string",
            title: "主色调",
            default: "#3B82F6"
          },
          secondary: {
            type: "string",
            title: "辅助色",
            default: "#10B981"
          },
          accent: {
            type: "string",
            title: "强调色",
            default: "#F59E0B"
          },
          background: {
            type: "string",
            title: "背景色",
            default: "#FFFFFF"
          },
          text: {
            type: "string",
            title: "文字色",
            default: "#1F2937"
          }
        }
      },
      features: {
        type: "array",
        title: "模版特性",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "特性名称",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "特性描述",
              maxLength: 300
            },
            category: {
              type: "string",
              title: "特性类别",
              enum: ["design", "functionality", "performance", "seo", "accessibility", "integration", "customization"],
              default: "design"
            },
            included: {
              type: "boolean",
              title: "是否包含",
              default: true
            }
          },
          required: ["name", "description", "category"]
        },
        default: [
          {
            name: "响应式设计",
            description: "完美适配桌面、平板和手机设备",
            category: "design",
            included: true
          },
          {
            name: "SEO优化",
            description: "内置SEO最佳实践，提升搜索引擎排名",
            category: "seo",
            included: true
          },
          {
            name: "快速加载",
            description: "优化的代码结构，确保页面快速加载",
            category: "performance",
            included: true
          },
          {
            name: "联系表单",
            description: "内置联系表单，支持邮件通知",
            category: "functionality",
            included: true
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      pages: {
        type: "array",
        title: "包含页面",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "页面名称",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "页面描述",
              maxLength: 300
            },
            type: {
              type: "string",
              title: "页面类型",
              enum: ["home", "about", "services", "portfolio", "blog", "contact", "pricing", "team", "testimonials", "faq", "privacy", "terms", "other"],
              default: "other"
            },
            required: {
              type: "boolean",
              title: "是否必需",
              default: true
            }
          },
          required: ["name", "description", "type"]
        },
        default: [
          {
            name: "首页",
            description: "企业主页，展示核心业务和价值主张",
            type: "home",
            required: true
          },
          {
            name: "关于我们",
            description: "公司介绍、团队展示和企业文化",
            type: "about",
            required: true
          },
          {
            name: "服务介绍",
            description: "详细的服务项目和解决方案",
            type: "services",
            required: true
          },
          {
            name: "联系我们",
            description: "联系方式、地址和联系表单",
            type: "contact",
            required: true
          }
        ],
        maxItems: 20,
        minItems: 1
      },
      technical: {
        type: "object",
        title: "技术规格",
        properties: {
          framework: {
            type: "string",
            title: "技术框架",
            enum: ["react", "vue", "angular", "html-css", "wordpress", "nextjs", "nuxtjs", "gatsby", "other"],
            default: "react"
          },
          cssFramework: {
            type: "string",
            title: "CSS框架",
            enum: ["tailwind", "bootstrap", "bulma", "foundation", "custom", "other"],
            default: "tailwind"
          },
          responsive: {
            type: "boolean",
            title: "响应式设计",
            default: true
          },
          browserSupport: {
            type: "array",
            title: "浏览器支持",
            items: {
              type: "string"
            },
            default: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"],
            maxItems: 10
          },
          performance: {
            type: "object",
            title: "性能指标",
            properties: {
              loadTime: {
                type: "string",
                title: "加载时间",
                default: "< 3秒"
              },
              lighthouseScore: {
                type: "integer",
                title: "Lighthouse评分",
                minimum: 0,
                maximum: 100,
                default: 95
              },
              mobileOptimized: {
                type: "boolean",
                title: "移动端优化",
                default: true
              }
            }
          }
        },
        required: ["framework", "cssFramework", "responsive"]
      },
      pricing: {
        type: "object",
        title: "价格信息",
        properties: {
          type: {
            type: "string",
            title: "价格类型",
            enum: ["free", "premium", "subscription", "custom"],
            default: "premium"
          },
          price: {
            type: "number",
            title: "价格",
            minimum: 0,
            default: 299
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          license: {
            type: "string",
            title: "许可类型",
            enum: ["single-site", "multi-site", "developer", "extended"],
            default: "single-site"
          },
          support: {
            type: "object",
            title: "技术支持",
            properties: {
              included: {
                type: "boolean",
                title: "包含支持",
                default: true
              },
              duration: {
                type: "string",
                title: "支持期限",
                default: "6个月"
              },
              channels: {
                type: "array",
                title: "支持渠道",
                items: {
                  type: "string",
                  enum: ["email", "chat", "phone", "documentation", "video-tutorials"]
                },
                default: ["email", "documentation"],
                maxItems: 5
              }
            }
          }
        },
        required: ["type", "price", "currency", "license"]
      },
      demo: {
        type: "object",
        title: "演示信息",
        properties: {
          liveUrl: {
            type: "string",
            title: "在线演示",
            format: "uri"
          },
          adminUrl: {
            type: "string",
            title: "后台演示",
            format: "uri"
          },
          credentials: {
            type: "object",
            title: "演示账号",
            properties: {
              username: {
                type: "string",
                title: "用户名",
                default: "demo"
              },
              password: {
                type: "string",
                title: "密码",
                default: "demo123"
              }
            }
          }
        }
      },
      images: {
        type: "array",
        title: "模版截图",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "图片类型",
              enum: ["desktop", "tablet", "mobile", "full-page", "section", "admin"],
              default: "desktop"
            },
            isPrimary: {
              type: "boolean",
              title: "是否为主图",
              default: false
            }
          },
          required: ["url", "caption", "type"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=800&fit=crop",
            caption: "现代商务网站模版 - 桌面端首页展示",
            alt: "网站模版桌面端截图",
            type: "desktop",
            isPrimary: true
          },
          {
            url: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=1200&fit=crop",
            caption: "移动端响应式设计展示",
            alt: "网站模版移动端截图",
            type: "mobile",
            isPrimary: false
          }
        ],
        maxItems: 20,
        minItems: 0
      }
    },
    required: ["title", "slug", "description", "category", "designStyle", "features", "pages", "technical", "pricing"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "category", "industry", "designStyle", "colorScheme",
      "features", "pages", "technical", "pricing", "demo", "images",
      "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入网站模版标题...",
        help: "吸引人的模版名称，突出特色和用途"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：modern-business-template",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "详细描述模版的特色、适用场景和优势...",
        help: "详细的模版介绍，有助于用户了解模版价值"
      }
    },
    
    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "business", label: "💼 商务企业" },
          { value: "portfolio", label: "🎨 作品集" },
          { value: "ecommerce", label: "🛒 电商" },
          { value: "blog", label: "📝 博客" },
          { value: "landing", label: "🚀 落地页" },
          { value: "saas", label: "☁️ SaaS" },
          { value: "agency", label: "🏢 代理机构" },
          { value: "restaurant", label: "🍽️ 餐饮" },
          { value: "education", label: "🎓 教育" },
          { value: "healthcare", label: "🏥 医疗" },
          { value: "nonprofit", label: "🤝 非营利" },
          { value: "personal", label: "👤 个人" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },
    
    industry: {
      "ui:widget": "checkboxes",
      "ui:options": {
        enumOptions: [
          { value: "technology", label: "💻 科技" },
          { value: "finance", label: "💰 金融" },
          { value: "healthcare", label: "🏥 医疗" },
          { value: "education", label: "🎓 教育" },
          { value: "retail", label: "🛍️ 零售" },
          { value: "manufacturing", label: "🏭 制造业" },
          { value: "consulting", label: "💡 咨询" },
          { value: "marketing", label: "📢 营销" },
          { value: "real-estate", label: "🏠 房地产" },
          { value: "food-beverage", label: "🍽️ 餐饮" },
          { value: "travel", label: "✈️ 旅游" },
          { value: "fitness", label: "💪 健身" },
          { value: "legal", label: "⚖️ 法律" },
          { value: "nonprofit", label: "🤝 非营利" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },
    
    designStyle: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "modern", label: "🔥 现代风格" },
          { value: "minimalist", label: "✨ 极简风格" },
          { value: "corporate", label: "🏢 企业风格" },
          { value: "creative", label: "🎨 创意风格" },
          { value: "elegant", label: "💎 优雅风格" },
          { value: "bold", label: "⚡ 大胆风格" },
          { value: "classic", label: "🎩 经典风格" },
          { value: "trendy", label: "🌟 时尚风格" }
        ]
      }
    },

    colorScheme: {
      "ui:order": ["primary", "secondary", "accent", "background", "text"],

      primary: {
        "ui:widget": "color",
        "ui:options": {
          help: "网站主色调"
        }
      },

      secondary: {
        "ui:widget": "color",
        "ui:options": {
          help: "辅助色调"
        }
      },

      accent: {
        "ui:widget": "color",
        "ui:options": {
          help: "强调色调"
        }
      },

      background: {
        "ui:widget": "color",
        "ui:options": {
          help: "背景颜色"
        }
      },

      text: {
        "ui:widget": "color",
        "ui:options": {
          help: "文字颜色"
        }
      }
    },

    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加特性"
      },
      items: {
        "ui:order": ["name", "description", "category", "included"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "特性名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "特性描述..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "design", label: "🎨 设计" },
              { value: "functionality", label: "⚙️ 功能" },
              { value: "performance", label: "⚡ 性能" },
              { value: "seo", label: "🔍 SEO" },
              { value: "accessibility", label: "♿ 无障碍" },
              { value: "integration", label: "🔗 集成" },
              { value: "customization", label: "🛠️ 定制" }
            ]
          }
        },

        included: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该特性是否包含在模版中"
          }
        }
      }
    },

    pages: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加页面"
      },
      items: {
        "ui:order": ["name", "description", "type", "required"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "页面名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "页面描述..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "home", label: "🏠 首页" },
              { value: "about", label: "ℹ️ 关于" },
              { value: "services", label: "🛠️ 服务" },
              { value: "portfolio", label: "🎨 作品集" },
              { value: "blog", label: "📝 博客" },
              { value: "contact", label: "📞 联系" },
              { value: "pricing", label: "💰 价格" },
              { value: "team", label: "👥 团队" },
              { value: "testimonials", label: "💬 推荐" },
              { value: "faq", label: "❓ 常见问题" },
              { value: "privacy", label: "🔒 隐私政策" },
              { value: "terms", label: "📋 服务条款" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        required: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该页面是否为必需页面"
          }
        }
      }
    },

    technical: {
      "ui:order": ["framework", "cssFramework", "responsive", "browserSupport", "performance"],

      framework: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "react", label: "⚛️ React" },
            { value: "vue", label: "💚 Vue.js" },
            { value: "angular", label: "🅰️ Angular" },
            { value: "html-css", label: "🌐 HTML/CSS" },
            { value: "wordpress", label: "📝 WordPress" },
            { value: "nextjs", label: "▲ Next.js" },
            { value: "nuxtjs", label: "💚 Nuxt.js" },
            { value: "gatsby", label: "🚀 Gatsby" },
            { value: "other", label: "📦 其他" }
          ]
        }
      },

      cssFramework: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "tailwind", label: "🎨 Tailwind CSS" },
            { value: "bootstrap", label: "🅱️ Bootstrap" },
            { value: "bulma", label: "💪 Bulma" },
            { value: "foundation", label: "🏗️ Foundation" },
            { value: "custom", label: "🛠️ 自定义" },
            { value: "other", label: "📦 其他" }
          ]
        }
      },

      responsive: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否支持响应式设计"
        }
      },

      browserSupport: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加浏览器支持"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "浏览器版本..."
          }
        }
      },

      performance: {
        "ui:order": ["loadTime", "lighthouseScore", "mobileOptimized"],

        loadTime: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：< 3秒",
            help: "页面加载时间"
          }
        },

        lighthouseScore: {
          "ui:widget": "updown",
          "ui:options": {
            help: "Lighthouse性能评分（0-100）"
          }
        },

        mobileOptimized: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否针对移动端优化"
          }
        }
      }
    },

    pricing: {
      "ui:order": ["type", "price", "currency", "license", "support"],

      type: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "free", label: "🆓 免费" },
            { value: "premium", label: "💎 付费" },
            { value: "subscription", label: "🔄 订阅" },
            { value: "custom", label: "🛠️ 定制" }
          ]
        }
      },

      price: {
        "ui:widget": "updown",
        "ui:options": {
          help: "模版价格"
        }
      },

      currency: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "CNY", label: "¥ 人民币" },
            { value: "USD", label: "$ 美元" },
            { value: "EUR", label: "€ 欧元" }
          ]
        }
      },

      license: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "single-site", label: "🏠 单站点" },
            { value: "multi-site", label: "🏢 多站点" },
            { value: "developer", label: "👨‍💻 开发者" },
            { value: "extended", label: "🚀 扩展版" }
          ]
        }
      },

      support: {
        "ui:order": ["included", "duration", "channels"],

        included: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否包含技术支持"
          }
        },

        duration: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：6个月",
            help: "技术支持期限"
          }
        },

        channels: {
          "ui:widget": "checkboxes",
          "ui:options": {
            enumOptions: [
              { value: "email", label: "📧 邮件" },
              { value: "chat", label: "💬 在线聊天" },
              { value: "phone", label: "📞 电话" },
              { value: "documentation", label: "📚 文档" },
              { value: "video-tutorials", label: "🎥 视频教程" }
            ]
          }
        }
      }
    },

    demo: {
      "ui:order": ["liveUrl", "adminUrl", "credentials"],

      liveUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://demo.example.com",
          help: "在线演示地址"
        }
      },

      adminUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://demo.example.com/admin",
          help: "后台管理演示地址"
        }
      },

      credentials: {
        "ui:order": ["username", "password"],

        username: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演示用户名..."
          }
        },

        password: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演示密码..."
          }
        }
      }
    },

    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加截图"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "type", "isPrimary"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "desktop", label: "🖥️ 桌面端" },
              { value: "tablet", label: "📱 平板端" },
              { value: "mobile", label: "📱 移动端" },
              { value: "full-page", label: "📄 完整页面" },
              { value: "section", label: "📋 页面片段" },
              { value: "admin", label: "⚙️ 后台管理" }
            ]
          }
        },

        isPrimary: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为主要展示图片"
          }
        }
      }
    }
  }
};

export default websiteTemplateConfig;
