import { BlockFormConfig } from '../types';

const vacationRentalConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "民宿标题",
        minLength: 5,
        maxLength: 150,
        default: "山景别墅 - 温馨舒适的度假民宿体验"
      },
      slug: {
        type: "string",
        title: "民宿标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "mountain-view-villa-vacation-rental"
      },
      description: {
        type: "string",
        title: "民宿描述",
        minLength: 100,
        maxLength: 2000,
        default: "坐落在风景如画的山脚下，这间精心装修的别墅民宿为您提供宁静舒适的度假体验。拥有宽敞的客厅、现代化厨房和私人花园，是家庭聚会和朋友聚餐的理想选择。"
      },
      propertyType: {
        type: "string",
        title: "房屋类型",
        enum: ["apartment", "house", "villa", "cabin", "cottage", "loft", "townhouse", "other"],
        default: "villa"
      },
      location: {
        type: "object",
        title: "位置信息",
        properties: {
          address: {
            type: "string",
            title: "详细地址",
            maxLength: 200,
            default: "北京市怀柔区雁栖湖风景区"
          },
          city: {
            type: "string",
            title: "城市",
            maxLength: 50,
            default: "北京"
          },
          state: {
            type: "string",
            title: "省份/州",
            maxLength: 50,
            default: "北京市"
          },
          country: {
            type: "string",
            title: "国家",
            maxLength: 50,
            default: "中国"
          },
          zipCode: {
            type: "string",
            title: "邮编",
            maxLength: 20,
            default: "101400"
          },
          latitude: {
            type: "number",
            title: "纬度",
            minimum: -90,
            maximum: 90,
            default: 40.4319
          },
          longitude: {
            type: "number",
            title: "经度",
            minimum: -180,
            maximum: 180,
            default: 116.6312
          },
          neighborhood: {
            type: "string",
            title: "周边区域",
            maxLength: 100,
            default: "雁栖湖风景区"
          }
        },
        required: ["address", "city", "country"]
      },
      accommodation: {
        type: "object",
        title: "住宿信息",
        properties: {
          maxGuests: {
            type: "integer",
            title: "最大入住人数",
            minimum: 1,
            maximum: 50,
            default: 6
          },
          bedrooms: {
            type: "integer",
            title: "卧室数量",
            minimum: 0,
            maximum: 20,
            default: 3
          },
          bathrooms: {
            type: "number",
            title: "浴室数量",
            minimum: 0,
            maximum: 20,
            default: 2
          },
          beds: {
            type: "integer",
            title: "床位数量",
            minimum: 1,
            maximum: 50,
            default: 4
          },
          area: {
            type: "number",
            title: "面积(平方米)",
            minimum: 10,
            maximum: 10000,
            default: 150
          },
          checkIn: {
            type: "string",
            title: "入住时间",
            default: "15:00"
          },
          checkOut: {
            type: "string",
            title: "退房时间",
            default: "11:00"
          },
          minStay: {
            type: "integer",
            title: "最少入住天数",
            minimum: 1,
            maximum: 365,
            default: 1
          }
        },
        required: ["maxGuests", "bedrooms", "bathrooms", "beds"]
      },
      pricing: {
        type: "object",
        title: "价格信息",
        properties: {
          basePrice: {
            type: "number",
            title: "基础价格(每晚)",
            minimum: 0,
            default: 580
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          weekendSurcharge: {
            type: "number",
            title: "周末加价(%)",
            minimum: 0,
            maximum: 100,
            default: 20
          },
          cleaningFee: {
            type: "number",
            title: "清洁费",
            minimum: 0,
            default: 100
          },
          securityDeposit: {
            type: "number",
            title: "押金",
            minimum: 0,
            default: 500
          },
          extraGuestFee: {
            type: "number",
            title: "额外客人费用(每人每晚)",
            minimum: 0,
            default: 50
          },
          seasonalPricing: {
            type: "array",
            title: "季节性定价",
            items: {
              type: "object",
              properties: {
                season: {
                  type: "string",
                  title: "季节",
                  maxLength: 50
                },
                startDate: {
                  type: "string",
                  title: "开始日期",
                  format: "date"
                },
                endDate: {
                  type: "string",
                  title: "结束日期",
                  format: "date"
                },
                priceMultiplier: {
                  type: "number",
                  title: "价格倍数",
                  minimum: 0.1,
                  maximum: 10,
                  default: 1.0
                }
              },
              required: ["season", "startDate", "endDate", "priceMultiplier"]
            },
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["basePrice", "currency"]
      },
      amenities: {
        type: "array",
        title: "设施服务",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "设施名称",
              maxLength: 100
            },
            category: {
              type: "string",
              title: "设施类别",
              enum: ["basic", "kitchen", "bathroom", "bedroom", "entertainment", "outdoor", "safety", "accessibility", "other"],
              default: "basic"
            },
            description: {
              type: "string",
              title: "设施描述",
              maxLength: 200
            },
            available: {
              type: "boolean",
              title: "是否可用",
              default: true
            },
            extraCharge: {
              type: "boolean",
              title: "是否额外收费",
              default: false
            }
          },
          required: ["name", "category"]
        },
        default: [
          {
            name: "免费WiFi",
            category: "basic",
            description: "全屋覆盖高速无线网络",
            available: true,
            extraCharge: false
          },
          {
            name: "空调",
            category: "basic",
            description: "每个房间都配备独立空调",
            available: true,
            extraCharge: false
          },
          {
            name: "厨房",
            category: "kitchen",
            description: "设备齐全的现代化厨房",
            available: true,
            extraCharge: false
          },
          {
            name: "私人花园",
            category: "outdoor",
            description: "带有户外座椅的私人花园",
            available: true,
            extraCharge: false
          },
          {
            name: "停车位",
            category: "basic",
            description: "免费私人停车位",
            available: true,
            extraCharge: false
          }
        ],
        maxItems: 50,
        minItems: 0
      },
      houseRules: {
        type: "object",
        title: "入住规则",
        properties: {
          smokingAllowed: {
            type: "boolean",
            title: "允许吸烟",
            default: false
          },
          petsAllowed: {
            type: "boolean",
            title: "允许宠物",
            default: false
          },
          partiesAllowed: {
            type: "boolean",
            title: "允许聚会",
            default: false
          },
          childrenAllowed: {
            type: "boolean",
            title: "允许儿童",
            default: true
          },
          quietHours: {
            type: "object",
            title: "安静时间",
            properties: {
              start: {
                type: "string",
                title: "开始时间",
                default: "22:00"
              },
              end: {
                type: "string",
                title: "结束时间",
                default: "08:00"
              }
            }
          },
          additionalRules: {
            type: "array",
            title: "其他规则",
            items: {
              type: "string",
              maxLength: 200
            },
            default: [
              "请保持房屋整洁",
              "损坏物品需要赔偿",
              "退房时请关闭所有电器"
            ],
            maxItems: 20
          }
        }
      },
      host: {
        type: "object",
        title: "房东信息",
        properties: {
          name: {
            type: "string",
            title: "房东姓名",
            maxLength: 50,
            default: "李女士"
          },
          avatar: {
            type: "string",
            title: "房东头像",
            format: "uri",
            default: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
          },
          bio: {
            type: "string",
            title: "房东介绍",
            maxLength: 500,
            default: "热情好客的本地房东，熟悉周边环境，乐于为客人提供旅行建议和帮助。"
          },
          responseTime: {
            type: "string",
            title: "回复时间",
            enum: ["within-hour", "within-few-hours", "within-day", "few-days"],
            default: "within-few-hours"
          },
          languages: {
            type: "array",
            title: "使用语言",
            items: {
              type: "string",
              maxLength: 30
            },
            default: ["中文", "英语"],
            maxItems: 10
          },
          joinDate: {
            type: "string",
            title: "加入日期",
            format: "date",
            default: "2020-01-01"
          },
          verified: {
            type: "boolean",
            title: "已认证",
            default: true
          }
        },
        required: ["name", "bio"]
      },
      images: {
        type: "array",
        title: "民宿图片",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "图片类型",
              enum: ["exterior", "interior", "bedroom", "bathroom", "kitchen", "living-room", "outdoor", "amenity", "view"],
              default: "interior"
            },
            isPrimary: {
              type: "boolean",
              title: "是否为主图",
              default: false
            }
          },
          required: ["url", "caption", "type"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop",
            caption: "民宿外观 - 山景别墅全貌",
            alt: "山景别墅外观",
            type: "exterior",
            isPrimary: true
          },
          {
            url: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
            caption: "宽敞明亮的客厅",
            alt: "客厅内景",
            type: "living-room",
            isPrimary: false
          }
        ],
        maxItems: 30,
        minItems: 0
      },
      availability: {
        type: "object",
        title: "可预订性",
        properties: {
          instantBook: {
            type: "boolean",
            title: "支持即时预订",
            default: true
          },
          advanceNotice: {
            type: "integer",
            title: "提前预订天数",
            minimum: 0,
            maximum: 365,
            default: 1
          },
          preparationTime: {
            type: "integer",
            title: "准备时间(小时)",
            minimum: 0,
            maximum: 72,
            default: 2
          },
          maxStayLength: {
            type: "integer",
            title: "最长入住天数",
            minimum: 1,
            maximum: 365,
            default: 30
          },
          blockedDates: {
            type: "array",
            title: "不可预订日期",
            items: {
              type: "object",
              properties: {
                startDate: {
                  type: "string",
                  title: "开始日期",
                  format: "date"
                },
                endDate: {
                  type: "string",
                  title: "结束日期",
                  format: "date"
                },
                reason: {
                  type: "string",
                  title: "原因",
                  maxLength: 100
                }
              },
              required: ["startDate", "endDate"]
            },
            maxItems: 50,
            minItems: 0
          }
        }
      },
      tags: {
        type: "array",
        title: "民宿标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["山景", "别墅", "家庭友好", "宠物友好", "度假"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "山景别墅民宿 - 温馨舒适的度假体验 | 民宿预订"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "坐落在风景如画的山脚下的精美别墅民宿，提供宁静舒适的度假体验。设施齐全，环境优美，是家庭聚会和朋友聚餐的理想选择。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["山景民宿", "别墅度假", "家庭民宿", "北京民宿"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "propertyType", "location", "accommodation", "pricing", "host"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "propertyType", "location", "accommodation",
      "pricing", "amenities", "houseRules", "host", "images", "availability",
      "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入民宿标题...",
        help: "吸引人的民宿名称，突出特色和位置"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：mountain-view-villa",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 5,
        placeholder: "详细描述民宿的特色、环境和体验...",
        help: "详细的民宿介绍，有助于吸引客人"
      }
    },
    
    propertyType: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "apartment", label: "🏠 公寓" },
          { value: "house", label: "🏡 独栋房屋" },
          { value: "villa", label: "🏘️ 别墅" },
          { value: "cabin", label: "🏕️ 小木屋" },
          { value: "cottage", label: "🏚️ 乡村小屋" },
          { value: "loft", label: "🏢 阁楼" },
          { value: "townhouse", label: "🏘️ 联排别墅" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },

    location: {
      "ui:order": ["address", "city", "state", "country", "zipCode", "latitude", "longitude", "neighborhood"],

      address: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "详细地址..."
        }
      },

      city: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "城市名称..."
        }
      },

      state: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "省份或州..."
        }
      },

      country: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "国家..."
        }
      },

      zipCode: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "邮政编码..."
        }
      },

      latitude: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.000001,
          help: "纬度坐标"
        }
      },

      longitude: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.000001,
          help: "经度坐标"
        }
      },

      neighborhood: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "周边区域或地标..."
        }
      }
    },

    accommodation: {
      "ui:order": ["maxGuests", "bedrooms", "bathrooms", "beds", "area", "checkIn", "checkOut", "minStay"],

      maxGuests: {
        "ui:widget": "updown",
        "ui:options": {
          help: "最大入住人数"
        }
      },

      bedrooms: {
        "ui:widget": "updown",
        "ui:options": {
          help: "卧室数量"
        }
      },

      bathrooms: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.5,
          help: "浴室数量（可以是半间）"
        }
      },

      beds: {
        "ui:widget": "updown",
        "ui:options": {
          help: "床位总数"
        }
      },

      area: {
        "ui:widget": "updown",
        "ui:options": {
          help: "房屋面积（平方米）"
        }
      },

      checkIn: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "例如：15:00",
          help: "入住时间"
        }
      },

      checkOut: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "例如：11:00",
          help: "退房时间"
        }
      },

      minStay: {
        "ui:widget": "updown",
        "ui:options": {
          help: "最少入住天数"
        }
      }
    },

    pricing: {
      "ui:order": ["basePrice", "currency", "weekendSurcharge", "cleaningFee", "securityDeposit", "extraGuestFee", "seasonalPricing"],

      basePrice: {
        "ui:widget": "updown",
        "ui:options": {
          help: "基础价格（每晚）"
        }
      },

      currency: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "CNY", label: "¥ 人民币" },
            { value: "USD", label: "$ 美元" },
            { value: "EUR", label: "€ 欧元" }
          ]
        }
      },

      weekendSurcharge: {
        "ui:widget": "updown",
        "ui:options": {
          help: "周末加价百分比"
        }
      },

      cleaningFee: {
        "ui:widget": "updown",
        "ui:options": {
          help: "一次性清洁费"
        }
      },

      securityDeposit: {
        "ui:widget": "updown",
        "ui:options": {
          help: "安全押金"
        }
      },

      extraGuestFee: {
        "ui:widget": "updown",
        "ui:options": {
          help: "超出基础人数的额外费用"
        }
      },

      seasonalPricing: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加季节性定价"
        },
        items: {
          "ui:order": ["season", "startDate", "endDate", "priceMultiplier"],

          season: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "季节名称..."
            }
          },

          startDate: {
            "ui:widget": "date",
            "ui:options": {
              help: "季节开始日期"
            }
          },

          endDate: {
            "ui:widget": "date",
            "ui:options": {
              help: "季节结束日期"
            }
          },

          priceMultiplier: {
            "ui:widget": "updown",
            "ui:options": {
              step: 0.1,
              help: "价格倍数（1.0为基础价格）"
            }
          }
        }
      }
    },

    amenities: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加设施"
      },
      items: {
        "ui:order": ["name", "category", "description", "available", "extraCharge"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "设施名称..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "basic", label: "🏠 基础设施" },
              { value: "kitchen", label: "🍳 厨房设施" },
              { value: "bathroom", label: "🚿 浴室设施" },
              { value: "bedroom", label: "🛏️ 卧室设施" },
              { value: "entertainment", label: "📺 娱乐设施" },
              { value: "outdoor", label: "🌳 户外设施" },
              { value: "safety", label: "🔒 安全设施" },
              { value: "accessibility", label: "♿ 无障碍设施" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "设施描述..."
          }
        },

        available: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "设施是否可用"
          }
        },

        extraCharge: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否需要额外收费"
          }
        }
      }
    },

    houseRules: {
      "ui:order": ["smokingAllowed", "petsAllowed", "partiesAllowed", "childrenAllowed", "quietHours", "additionalRules"],

      smokingAllowed: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否允许在室内吸烟"
        }
      },

      petsAllowed: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否允许携带宠物"
        }
      },

      partiesAllowed: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否允许举办聚会"
        }
      },

      childrenAllowed: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否欢迎儿童入住"
        }
      },

      quietHours: {
        "ui:order": ["start", "end"],

        start: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：22:00",
            help: "安静时间开始"
          }
        },

        end: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "例如：08:00",
            help: "安静时间结束"
          }
        }
      },

      additionalRules: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加规则"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "入住规则..."
          }
        }
      }
    },

    host: {
      "ui:order": ["name", "avatar", "bio", "responseTime", "languages", "joinDate", "verified"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "房东姓名..."
        }
      },

      avatar: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "房东头像URL..."
        }
      },

      bio: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 4,
          placeholder: "房东自我介绍..."
        }
      },

      responseTime: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "within-hour", label: "⚡ 1小时内" },
            { value: "within-few-hours", label: "🕐 几小时内" },
            { value: "within-day", label: "📅 1天内" },
            { value: "few-days", label: "📆 几天内" }
          ]
        }
      },

      languages: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加语言"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "语言..."
          }
        }
      },

      joinDate: {
        "ui:widget": "date",
        "ui:options": {
          help: "房东加入平台的日期"
        }
      },

      verified: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "房东是否已通过身份认证"
        }
      }
    },

    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加图片"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "type", "isPrimary"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "exterior", label: "🏠 外观" },
              { value: "interior", label: "🏡 内景" },
              { value: "bedroom", label: "🛏️ 卧室" },
              { value: "bathroom", label: "🚿 浴室" },
              { value: "kitchen", label: "🍳 厨房" },
              { value: "living-room", label: "🛋️ 客厅" },
              { value: "outdoor", label: "🌳 户外" },
              { value: "amenity", label: "🎯 设施" },
              { value: "view", label: "🌄 景观" }
            ]
          }
        },

        isPrimary: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为主要展示图片"
          }
        }
      }
    },

    availability: {
      "ui:order": ["instantBook", "advanceNotice", "preparationTime", "maxStayLength", "blockedDates"],

      instantBook: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "客人是否可以直接预订"
        }
      },

      advanceNotice: {
        "ui:widget": "updown",
        "ui:options": {
          help: "需要提前多少天预订"
        }
      },

      preparationTime: {
        "ui:widget": "updown",
        "ui:options": {
          help: "房屋准备时间（小时）"
        }
      },

      maxStayLength: {
        "ui:widget": "updown",
        "ui:options": {
          help: "最长入住天数"
        }
      },

      blockedDates: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加不可预订日期"
        },
        items: {
          "ui:order": ["startDate", "endDate", "reason"],

          startDate: {
            "ui:widget": "date",
            "ui:options": {
              help: "开始日期"
            }
          },

          endDate: {
            "ui:widget": "date",
            "ui:options": {
              help: "结束日期"
            }
          },

          reason: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "不可预订的原因..."
            }
          }
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "民宿发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default vacationRentalConfig;
