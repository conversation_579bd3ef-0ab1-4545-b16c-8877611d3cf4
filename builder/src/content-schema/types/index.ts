/**
 * CMS内容模型类型定义
 * 
 * 为所有CMS编辑器提供类型安全的数据结构定义
 */

import { RJSFSchema, UiSchema } from '@rjsf/utils';

// 基础配置接口
export interface BlockFormConfig {
  schema: RJSFSchema;
  uiSchema?: UiSchema;
}

// 博客内容数据类型
export interface BlogDetailData {
  title: string;
  excerpt: string;
  content: string;
  featuredImage: {
    url: string;
    alt: string;
  };
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  category: string;
  tags: string[];
  readingTime: number;
  seo: {
    metaTitle: string;
    metaDescription: string;
  };
}

// 修改日志数据类型
export interface ChangelogData {
  version: string;
  releaseDate: string;
  releaseType: 'major' | 'minor' | 'patch' | 'hotfix';
  title: string;
  summary: string;
  changes: ChangelogEntry[];
  seo: {
    metaTitle: string;
    metaDescription: string;
  };
}

export interface ChangelogEntry {
  type: 'added' | 'improved' | 'fixed' | 'security';
  description: string;
  details?: string;
}

// 文档数据类型
export interface DocumentationData {
  title: string;
  slug: string;
  category: string;
  docType: 'guide' | 'api' | 'tutorial' | 'reference' | 'faq';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  summary: string;
  content: string;
  estimatedTime: number;
  prerequisites: string[];
  codeExamples: CodeExample[];
  relatedDocs: string[];
  lastUpdated: string;
  version: string;
  changelog: ChangelogEntry[];
  seo: SEOData;
}

export interface CodeExample {
  title: string;
  description: string;
  content: string;
}

export interface SEOData {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

// 编辑器组件Props类型
export interface EditorPageProps<T> {
  initialData?: T;
  onSave: (data: T) => void;
  onError?: (error: Error) => void;
  isLoading?: boolean;
  className?: string;
}

// 表单验证结果类型
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// 编辑器状态类型
export interface EditorState<T> {
  data: T;
  isDirty: boolean;
  isValid: boolean;
  errors: ValidationError[];
  isSubmitting: boolean;
}

// 预览配置类型
export interface PreviewConfig {
  showRawJson?: boolean;
  showFormattedPreview?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}
