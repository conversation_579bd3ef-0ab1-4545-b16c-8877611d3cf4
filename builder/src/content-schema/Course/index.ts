import { BlockFormConfig } from '../types';

const courseConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "课程标题",
        minLength: 5,
        maxLength: 150,
        default: "React 全栈开发实战课程"
      },
      slug: {
        type: "string",
        title: "课程标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "react-fullstack-development"
      },
      subtitle: {
        type: "string",
        title: "副标题",
        maxLength: 200,
        default: "从零基础到项目实战，掌握现代Web开发技术栈"
      },
      description: {
        type: "string",
        title: "课程描述",
        minLength: 100,
        default: "本课程将带你从React基础知识开始，逐步深入学习现代Web开发的核心技术。通过理论学习和实战项目，你将掌握React、Node.js、数据库等全栈开发技能，最终能够独立开发完整的Web应用程序。"
      },
      level: {
        type: "string",
        title: "课程难度",
        enum: ["beginner", "intermediate", "advanced", "expert"],
        default: "intermediate"
      },
      category: {
        type: "string",
        title: "课程分类",
        minLength: 2,
        maxLength: 50,
        default: "Web开发"
      },
      tags: {
        type: "array",
        title: "课程标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["React", "JavaScript", "全栈开发", "前端"],
        maxItems: 10,
        minItems: 1
      },
      learningObjectives: {
        type: "array",
        title: "学习目标",
        items: {
          type: "string",
          minLength: 10,
          maxLength: 200
        },
        default: [
          "掌握React组件开发和状态管理",
          "学会使用Node.js构建后端API",
          "理解前后端数据交互和数据库操作",
          "能够独立开发完整的全栈Web应用"
        ],
        maxItems: 10,
        minItems: 1
      },
      prerequisites: {
        type: "array",
        title: "前置条件",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "前置技能",
              minLength: 2,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "技能描述",
              maxLength: 200
            },
            required: {
              type: "boolean",
              title: "是否必需",
              default: true
            },
            level: {
              type: "string",
              title: "要求水平",
              enum: ["basic", "intermediate", "advanced"],
              default: "basic"
            }
          },
          required: ["title", "required"]
        },
        default: [
          {
            title: "HTML/CSS基础",
            description: "了解基本的HTML标签和CSS样式",
            required: true,
            level: "basic"
          },
          {
            title: "JavaScript基础",
            description: "掌握JavaScript基本语法和ES6特性",
            required: true,
            level: "intermediate"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      curriculum: {
        type: "array",
        title: "课程大纲",
        items: {
          type: "object",
          properties: {
            moduleTitle: {
              type: "string",
              title: "模块标题",
              minLength: 5,
              maxLength: 100
            },
            moduleDescription: {
              type: "string",
              title: "模块描述",
              maxLength: 300
            },
            duration: {
              type: "integer",
              title: "模块时长(分钟)",
              minimum: 5,
              maximum: 600
            },
            lessons: {
              type: "array",
              title: "课时列表",
              items: {
                type: "object",
                properties: {
                  lessonTitle: {
                    type: "string",
                    title: "课时标题",
                    minLength: 5,
                    maxLength: 100
                  },
                  duration: {
                    type: "integer",
                    title: "课时时长(分钟)",
                    minimum: 5,
                    maximum: 120
                  },
                  type: {
                    type: "string",
                    title: "课时类型",
                    enum: ["video", "text", "quiz", "assignment", "live"],
                    default: "video"
                  },
                  isFree: {
                    type: "boolean",
                    title: "免费试听",
                    default: false
                  }
                },
                required: ["lessonTitle", "duration", "type"]
              },
              default: [],
              maxItems: 20
            }
          },
          required: ["moduleTitle", "duration"]
        },
        default: [
          {
            moduleTitle: "React基础入门",
            moduleDescription: "学习React的核心概念和基础用法",
            duration: 180,
            lessons: [
              {
                lessonTitle: "React简介和环境搭建",
                duration: 30,
                type: "video",
                isFree: true
              },
              {
                lessonTitle: "组件和JSX语法",
                duration: 45,
                type: "video",
                isFree: false
              }
            ]
          }
        ],
        maxItems: 15,
        minItems: 1
      },
      instructors: {
        type: "array",
        title: "讲师信息",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "讲师姓名",
              minLength: 2,
              maxLength: 50
            },
            title: {
              type: "string",
              title: "职业头衔",
              maxLength: 100
            },
            bio: {
              type: "string",
              title: "讲师简介",
              maxLength: 500
            },
            avatar: {
              type: "string",
              title: "头像URL",
              format: "uri"
            },
            experience: {
              type: "integer",
              title: "从业年限",
              minimum: 0,
              maximum: 50
            },
            specialties: {
              type: "array",
              title: "专业领域",
              items: {
                type: "string",
                minLength: 2,
                maxLength: 30
              },
              maxItems: 10
            },
            social: {
              type: "object",
              title: "社交媒体",
              properties: {
                website: {
                  type: "string",
                  title: "个人网站",
                  format: "uri"
                },
                linkedin: {
                  type: "string",
                  title: "LinkedIn",
                  format: "uri"
                },
                github: {
                  type: "string",
                  title: "GitHub",
                  format: "uri"
                }
              }
            }
          },
          required: ["name", "title"]
        },
        default: [
          {
            name: "张三",
            title: "高级前端工程师",
            bio: "拥有8年Web开发经验，专注于React生态系统和现代前端技术。曾在多家知名互联网公司担任技术负责人。",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            experience: 8,
            specialties: ["React", "JavaScript", "Node.js", "TypeScript"],
            social: {
              website: "https://example.com",
              linkedin: "https://linkedin.com/in/zhangsan",
              github: "https://github.com/zhangsan"
            }
          }
        ],
        maxItems: 5,
        minItems: 1
      },
      pricing: {
        type: "object",
        title: "课程定价",
        properties: {
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          originalPrice: {
            type: "number",
            title: "原价",
            minimum: 0,
            default: 299
          },
          currentPrice: {
            type: "number",
            title: "现价",
            minimum: 0,
            default: 199
          },
          isFree: {
            type: "boolean",
            title: "免费课程",
            default: false
          },
          hasDiscount: {
            type: "boolean",
            title: "限时优惠",
            default: true
          },
          discountEndDate: {
            type: "string",
            title: "优惠截止日期",
            format: "date"
          }
        },
        required: ["currency", "currentPrice"]
      },
      duration: {
        type: "object",
        title: "课程时长",
        properties: {
          totalMinutes: {
            type: "integer",
            title: "总时长(分钟)",
            minimum: 30,
            default: 1200
          },
          totalLessons: {
            type: "integer",
            title: "总课时数",
            minimum: 1,
            default: 24
          },
          estimatedWeeks: {
            type: "integer",
            title: "预计学习周数",
            minimum: 1,
            maximum: 52,
            default: 8
          }
        },
        required: ["totalMinutes", "totalLessons"]
      },
      features: {
        type: "array",
        title: "课程特色",
        items: {
          type: "object",
          properties: {
            feature: {
              type: "string",
              title: "特色功能",
              minLength: 5,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "功能描述",
              maxLength: 200
            },
            icon: {
              type: "string",
              title: "图标名称",
              default: "CheckCircle"
            }
          },
          required: ["feature"]
        },
        default: [
          {
            feature: "项目实战",
            description: "通过真实项目案例学习，理论与实践相结合",
            icon: "Code"
          },
          {
            feature: "终身学习",
            description: "一次购买，终身可以重复学习和复习",
            icon: "Infinity"
          },
          {
            feature: "社群支持",
            description: "加入学习社群，与同学和讲师互动交流",
            icon: "Users"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      status: {
        type: "string",
        title: "课程状态",
        enum: ["draft", "published", "archived", "coming-soon"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      enrollmentCount: {
        type: "integer",
        title: "报名人数",
        minimum: 0,
        default: 0
      },
      rating: {
        type: "object",
        title: "课程评分",
        properties: {
          average: {
            type: "number",
            title: "平均评分",
            minimum: 0,
            maximum: 5,
            default: 0
          },
          count: {
            type: "integer",
            title: "评分人数",
            minimum: 0,
            default: 0
          }
        }
      },
      certificate: {
        type: "object",
        title: "证书设置",
        properties: {
          available: {
            type: "boolean",
            title: "提供证书",
            default: true
          },
          certificateName: {
            type: "string",
            title: "证书名称",
            maxLength: 100,
            default: "React全栈开发认证证书"
          },
          completionRequirement: {
            type: "integer",
            title: "完成要求(%)",
            minimum: 50,
            maximum: 100,
            default: 80
          }
        }
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "React全栈开发实战课程 - 从零基础到项目实战"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "专业的React全栈开发课程，包含前端React、后端Node.js、数据库等完整技术栈。通过实战项目学习，快速掌握现代Web开发技能。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["React课程", "全栈开发", "JavaScript培训", "Web开发教程"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "level", "category", "learningObjectives", "curriculum", "instructors", "pricing", "duration"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "subtitle", "description", "level", "category", "tags",
      "learningObjectives", "prerequisites", "curriculum", "instructors", "pricing",
      "duration", "features", "status", "publishDate", "lastUpdated",
      "enrollmentCount", "rating", "certificate", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入课程标题...",
        help: "简洁明确的课程标题，突出核心技术和价值"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：react-fullstack-development",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    subtitle: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "课程副标题或简短描述...",
        help: "补充说明课程特色和学习成果"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "详细描述课程内容、学习目标和适用人群...",
        help: "吸引学员的详细课程介绍"
      }
    },
    
    level: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 初级 - 适合零基础学员" },
          { value: "intermediate", label: "🟡 中级 - 需要一定基础" },
          { value: "advanced", label: "🟠 高级 - 需要丰富经验" },
          { value: "expert", label: "🔴 专家级 - 深度专业内容" }
        ]
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：Web开发、移动开发、数据科学",
        help: "课程所属的主要分类"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },
    
    learningObjectives: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加学习目标"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "描述学员完成课程后能够掌握的技能..."
        }
      }
    },
    
    prerequisites: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加前置条件"
      },
      items: {
        "ui:order": ["title", "description", "required", "level"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "前置技能名称..."
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "详细描述所需的技能水平..."
          }
        },
        
        required: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为必需的前置条件"
          }
        },
        
        level: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "basic", label: "基础了解" },
              { value: "intermediate", label: "中等熟练" },
              { value: "advanced", label: "高级掌握" }
            ]
          }
        }
      }
    },

    curriculum: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加课程模块"
      },
      items: {
        "ui:order": ["moduleTitle", "moduleDescription", "duration", "lessons"],

        moduleTitle: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "模块标题..."
          }
        },

        moduleDescription: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "模块描述..."
          }
        },

        duration: {
          "ui:widget": "updown",
          "ui:options": {
            help: "模块总时长（分钟）"
          }
        },

        lessons: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加课时"
          },
          items: {
            "ui:order": ["lessonTitle", "duration", "type", "isFree"],

            lessonTitle: {
              "ui:widget": "text",
              "ui:options": {
                placeholder: "课时标题..."
              }
            },

            duration: {
              "ui:widget": "updown",
              "ui:options": {
                help: "课时时长（分钟）"
              }
            },

            type: {
              "ui:widget": "select",
              "ui:options": {
                enumOptions: [
                  { value: "video", label: "📹 视频课程" },
                  { value: "text", label: "📄 图文教程" },
                  { value: "quiz", label: "📝 测验练习" },
                  { value: "assignment", label: "📋 作业任务" },
                  { value: "live", label: "🎥 直播课程" }
                ]
              }
            },

            isFree: {
              "ui:widget": "checkbox",
              "ui:options": {
                help: "是否免费试听"
              }
            }
          }
        }
      }
    },

    instructors: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加讲师"
      },
      items: {
        "ui:order": ["name", "title", "bio", "avatar", "experience", "specialties", "social"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "讲师姓名..."
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "职业头衔..."
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 4,
            placeholder: "讲师简介和教学经验..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "头像图片URL..."
          }
        },

        experience: {
          "ui:widget": "updown",
          "ui:options": {
            help: "从业年限"
          }
        },

        specialties: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加专业领域"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "专业技能..."
            }
          }
        },

        social: {
          "ui:order": ["website", "linkedin", "github"],

          website: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "个人网站URL..."
            }
          },

          linkedin: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "LinkedIn个人资料URL..."
            }
          },

          github: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "GitHub个人资料URL..."
            }
          }
        }
      }
    },

    pricing: {
      "ui:order": ["currency", "originalPrice", "currentPrice", "isFree", "hasDiscount", "discountEndDate"],

      currency: {
        "ui:widget": "select"
      },

      originalPrice: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "课程原价"
        }
      },

      currentPrice: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "当前售价"
        }
      },

      isFree: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否为免费课程"
        }
      },

      hasDiscount: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否有限时优惠"
        }
      },

      discountEndDate: {
        "ui:widget": "date",
        "ui:options": {
          help: "优惠活动截止日期"
        }
      }
    },

    duration: {
      "ui:order": ["totalMinutes", "totalLessons", "estimatedWeeks"],

      totalMinutes: {
        "ui:widget": "updown",
        "ui:options": {
          help: "课程总时长（分钟）"
        }
      },

      totalLessons: {
        "ui:widget": "updown",
        "ui:options": {
          help: "总课时数量"
        }
      },

      estimatedWeeks: {
        "ui:widget": "updown",
        "ui:options": {
          help: "预计学习周数"
        }
      }
    },

    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加课程特色"
      },
      items: {
        "ui:order": ["feature", "description", "icon"],

        feature: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "特色功能名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "功能详细描述..."
          }
        },

        icon: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图标名称（Lucide图标）",
            help: "用于显示的图标名称"
          }
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" },
          { value: "coming-soon", label: "🚀 即将推出" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "课程发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    enrollmentCount: {
      "ui:widget": "updown",
      "ui:options": {
        help: "当前报名学员数量"
      }
    },

    rating: {
      "ui:order": ["average", "count"],

      average: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "平均评分（0-5分）"
        }
      },

      count: {
        "ui:widget": "updown",
        "ui:options": {
          help: "评分人数"
        }
      }
    },

    certificate: {
      "ui:order": ["available", "certificateName", "completionRequirement"],

      available: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "完成课程后是否颁发证书"
        }
      },

      certificateName: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "证书名称..."
        }
      },

      completionRequirement: {
        "ui:widget": "updown",
        "ui:options": {
          help: "获得证书需要的完成度百分比"
        }
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default courseConfig;
