import { BlockFormConfig } from '../types';

const recipeConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "食谱标题",
        minLength: 5,
        maxLength: 150,
        default: "经典红烧肉 - 传统家常菜的完美做法"
      },
      slug: {
        type: "string",
        title: "食谱标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "classic-braised-pork-belly"
      },
      description: {
        type: "string",
        title: "食谱描述",
        minLength: 50,
        maxLength: 1000,
        default: "这道经典红烧肉采用传统做法，肥瘦相间的五花肉经过精心烹制，色泽红亮，肉质软糯，甜而不腻。配以简单的调料和独特的烹饪技巧，让您在家也能做出餐厅级别的美味红烧肉。"
      },
      cuisine: {
        type: "string",
        title: "菜系",
        enum: ["chinese", "western", "japanese", "korean", "thai", "indian", "italian", "french", "mexican", "other"],
        default: "chinese"
      },
      category: {
        type: "string",
        title: "菜品分类",
        enum: ["appetizer", "main-course", "side-dish", "soup", "dessert", "beverage", "snack", "breakfast", "lunch", "dinner"],
        default: "main-course"
      },
      difficulty: {
        type: "string",
        title: "难度等级",
        enum: ["beginner", "intermediate", "advanced", "expert"],
        default: "intermediate"
      },
      servings: {
        type: "integer",
        title: "份量",
        minimum: 1,
        maximum: 20,
        default: 4
      },
      prepTime: {
        type: "integer",
        title: "准备时间(分钟)",
        minimum: 1,
        maximum: 480,
        default: 20
      },
      cookTime: {
        type: "integer",
        title: "烹饪时间(分钟)",
        minimum: 1,
        maximum: 480,
        default: 60
      },
      totalTime: {
        type: "integer",
        title: "总时间(分钟)",
        minimum: 1,
        maximum: 960,
        default: 80
      },
      ingredients: {
        type: "array",
        title: "食材清单",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "食材名称",
              minLength: 1,
              maxLength: 100
            },
            amount: {
              type: "number",
              title: "用量",
              minimum: 0
            },
            unit: {
              type: "string",
              title: "单位",
              enum: ["g", "kg", "ml", "l", "个", "只", "根", "片", "块", "勺", "杯", "少许", "适量"],
              default: "g"
            },
            category: {
              type: "string",
              title: "食材类别",
              enum: ["meat", "vegetable", "fruit", "grain", "dairy", "spice", "sauce", "oil", "other"],
              default: "other"
            },
            optional: {
              type: "boolean",
              title: "可选食材",
              default: false
            },
            notes: {
              type: "string",
              title: "备注",
              maxLength: 200
            }
          },
          required: ["name", "amount", "unit", "category"]
        },
        default: [
          {
            name: "五花肉",
            amount: 500,
            unit: "g",
            category: "meat",
            optional: false,
            notes: "选择肥瘦相间的新鲜五花肉"
          },
          {
            name: "生抽",
            amount: 3,
            unit: "勺",
            category: "sauce",
            optional: false
          },
          {
            name: "老抽",
            amount: 1,
            unit: "勺",
            category: "sauce",
            optional: false,
            notes: "用于上色"
          },
          {
            name: "冰糖",
            amount: 30,
            unit: "g",
            category: "other",
            optional: false
          },
          {
            name: "料酒",
            amount: 2,
            unit: "勺",
            category: "sauce",
            optional: false
          },
          {
            name: "生姜",
            amount: 3,
            unit: "片",
            category: "spice",
            optional: false
          },
          {
            name: "大葱",
            amount: 2,
            unit: "根",
            category: "vegetable",
            optional: false
          }
        ],
        maxItems: 30,
        minItems: 1
      },
      instructions: {
        type: "array",
        title: "制作步骤",
        items: {
          type: "object",
          properties: {
            step: {
              type: "integer",
              title: "步骤序号",
              minimum: 1
            },
            title: {
              type: "string",
              title: "步骤标题",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "详细说明",
              minLength: 10,
              maxLength: 500
            },
            duration: {
              type: "integer",
              title: "所需时间(分钟)",
              minimum: 0,
              maximum: 240
            },
            temperature: {
              type: "string",
              title: "温度设置",
              maxLength: 50
            },
            tips: {
              type: "string",
              title: "小贴士",
              maxLength: 300
            },
            image: {
              type: "string",
              title: "步骤图片",
              format: "uri"
            }
          },
          required: ["step", "description"]
        },
        default: [
          {
            step: 1,
            title: "准备食材",
            description: "将五花肉洗净切成3cm见方的块状，生姜切片，大葱切段备用。",
            duration: 10,
            tips: "肉块不要切得太小，否则容易散烂"
          },
          {
            step: 2,
            title: "焯水处理",
            description: "锅中加水烧开，放入肉块焯水3-5分钟，撇去浮沫后捞出沥干。",
            duration: 8,
            temperature: "大火",
            tips: "焯水可以去除血水和腥味"
          },
          {
            step: 3,
            title: "炒糖色",
            description: "锅中放少量油，小火炒制冰糖至焦糖色，倒入肉块翻炒上色。",
            duration: 5,
            temperature: "小火",
            tips: "炒糖色时火候要小，避免炒糊"
          },
          {
            step: 4,
            title: "调味炖煮",
            description: "加入生抽、老抽、料酒，放入姜片和葱段，加适量热水没过肉块，大火烧开后转小火炖煮45分钟。",
            duration: 50,
            temperature: "先大火后小火",
            tips: "水要用热水，冷水会让肉质变硬"
          },
          {
            step: 5,
            title: "收汁装盘",
            description: "大火收汁至汤汁浓稠，撒上葱花即可装盘。",
            duration: 5,
            temperature: "大火",
            tips: "收汁时要不断翻炒，避免粘锅"
          }
        ],
        maxItems: 20,
        minItems: 1
      },
      nutrition: {
        type: "object",
        title: "营养信息",
        properties: {
          calories: {
            type: "integer",
            title: "热量(卡路里/份)",
            minimum: 0,
            default: 450
          },
          protein: {
            type: "number",
            title: "蛋白质(g/份)",
            minimum: 0,
            default: 25.5
          },
          carbohydrates: {
            type: "number",
            title: "碳水化合物(g/份)",
            minimum: 0,
            default: 8.2
          },
          fat: {
            type: "number",
            title: "脂肪(g/份)",
            minimum: 0,
            default: 35.8
          },
          fiber: {
            type: "number",
            title: "膳食纤维(g/份)",
            minimum: 0,
            default: 0.5
          },
          sodium: {
            type: "number",
            title: "钠(mg/份)",
            minimum: 0,
            default: 890
          },
          sugar: {
            type: "number",
            title: "糖分(g/份)",
            minimum: 0,
            default: 6.8
          }
        }
      },
      equipment: {
        type: "array",
        title: "所需厨具",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "厨具名称",
              maxLength: 50
            },
            essential: {
              type: "boolean",
              title: "是否必需",
              default: true
            },
            alternative: {
              type: "string",
              title: "替代方案",
              maxLength: 100
            }
          },
          required: ["name", "essential"]
        },
        default: [
          {
            name: "炒锅",
            essential: true,
            alternative: "平底锅或砂锅"
          },
          {
            name: "锅铲",
            essential: true
          },
          {
            name: "菜刀",
            essential: true
          },
          {
            name: "砧板",
            essential: true
          }
        ],
        maxItems: 15,
        minItems: 0
      },
      tips: {
        type: "array",
        title: "烹饪技巧",
        items: {
          type: "object",
          properties: {
            category: {
              type: "string",
              title: "技巧类别",
              enum: ["ingredient", "technique", "timing", "storage", "serving", "variation"],
              default: "technique"
            },
            title: {
              type: "string",
              title: "技巧标题",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "详细说明",
              maxLength: 300
            }
          },
          required: ["category", "title", "description"]
        },
        default: [
          {
            category: "ingredient",
            title: "选择五花肉",
            description: "选择肥瘦相间、层次分明的五花肉，肉质要新鲜有弹性"
          },
          {
            category: "technique",
            title: "炒糖色技巧",
            description: "炒糖色时要用小火慢炒，糖色呈焦糖色时立即下肉，动作要快"
          },
          {
            category: "timing",
            title: "炖煮时间",
            description: "炖煮时间要充足，至少45分钟，让肉质充分软烂"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      images: {
        type: "array",
        title: "食谱图片",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "图片类型",
              enum: ["main", "ingredient", "step", "final"],
              default: "main"
            }
          },
          required: ["url", "caption", "type"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop",
            caption: "经典红烧肉成品图",
            alt: "红烧肉最终成品",
            type: "main"
          }
        ],
        maxItems: 15,
        minItems: 0
      },
      video: {
        type: "object",
        title: "制作视频",
        properties: {
          url: {
            type: "string",
            title: "视频URL",
            format: "uri"
          },
          title: {
            type: "string",
            title: "视频标题",
            maxLength: 150
          },
          duration: {
            type: "integer",
            title: "视频时长(秒)",
            minimum: 1,
            maximum: 3600
          },
          thumbnail: {
            type: "string",
            title: "视频缩略图",
            format: "uri"
          },
          platform: {
            type: "string",
            title: "视频平台",
            enum: ["youtube", "bilibili", "tiktok", "self-hosted", "other"],
            default: "self-hosted"
          }
        }
      },
      tags: {
        type: "array",
        title: "食谱标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["红烧肉", "家常菜", "中式菜", "下饭菜", "肉类"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "经典红烧肉做法 - 传统家常菜完美制作教程 | 美食食谱"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "学会这道经典红烧肉的传统做法，肥瘦相间，色泽红亮，肉质软糯。详细步骤图解，新手也能轻松掌握，让您在家做出餐厅级美味。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["红烧肉做法", "家常菜谱", "中式菜谱", "肉类料理"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "cuisine", "category", "difficulty", "servings", "prepTime", "cookTime", "ingredients", "instructions"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "cuisine", "category", "difficulty",
      "servings", "prepTime", "cookTime", "totalTime", "ingredients", "instructions",
      "nutrition", "equipment", "tips", "images", "video", "tags",
      "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入食谱标题...",
        help: "吸引人的食谱名称，突出特色和口味"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：classic-braised-pork-belly",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "描述这道菜的特色、口感和制作亮点...",
        help: "详细的食谱介绍，有助于吸引读者"
      }
    },
    
    cuisine: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "chinese", label: "🥢 中式" },
          { value: "western", label: "🍽️ 西式" },
          { value: "japanese", label: "🍱 日式" },
          { value: "korean", label: "🥘 韩式" },
          { value: "thai", label: "🌶️ 泰式" },
          { value: "indian", label: "🍛 印式" },
          { value: "italian", label: "🍝 意式" },
          { value: "french", label: "🥐 法式" },
          { value: "mexican", label: "🌮 墨式" },
          { value: "other", label: "🌍 其他" }
        ]
      }
    },
    
    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "appetizer", label: "🥗 开胃菜" },
          { value: "main-course", label: "🍖 主菜" },
          { value: "side-dish", label: "🥬 配菜" },
          { value: "soup", label: "🍲 汤品" },
          { value: "dessert", label: "🍰 甜品" },
          { value: "beverage", label: "🥤 饮品" },
          { value: "snack", label: "🍿 小食" },
          { value: "breakfast", label: "🌅 早餐" },
          { value: "lunch", label: "🌞 午餐" },
          { value: "dinner", label: "🌙 晚餐" }
        ]
      }
    },
    
    difficulty: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 新手" },
          { value: "intermediate", label: "🟡 中级" },
          { value: "advanced", label: "🟠 高级" },
          { value: "expert", label: "🔴 专家" }
        ]
      }
    },

    servings: {
      "ui:widget": "updown",
      "ui:options": {
        help: "这道菜可以供几人食用"
      }
    },

    prepTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "准备食材所需时间（分钟）"
      }
    },

    cookTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "烹饪制作所需时间（分钟）"
      }
    },

    totalTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "从开始到完成的总时间（分钟）"
      }
    },

    ingredients: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加食材"
      },
      items: {
        "ui:order": ["name", "amount", "unit", "category", "optional", "notes"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "食材名称..."
          }
        },

        amount: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.1,
            help: "用量"
          }
        },

        unit: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "g", label: "克(g)" },
              { value: "kg", label: "千克(kg)" },
              { value: "ml", label: "毫升(ml)" },
              { value: "l", label: "升(l)" },
              { value: "个", label: "个" },
              { value: "只", label: "只" },
              { value: "根", label: "根" },
              { value: "片", label: "片" },
              { value: "块", label: "块" },
              { value: "勺", label: "勺" },
              { value: "杯", label: "杯" },
              { value: "少许", label: "少许" },
              { value: "适量", label: "适量" }
            ]
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "meat", label: "🥩 肉类" },
              { value: "vegetable", label: "🥬 蔬菜" },
              { value: "fruit", label: "🍎 水果" },
              { value: "grain", label: "🌾 谷物" },
              { value: "dairy", label: "🥛 乳制品" },
              { value: "spice", label: "🧂 调料" },
              { value: "sauce", label: "🍯 酱料" },
              { value: "oil", label: "🫒 油脂" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        optional: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为可选食材"
          }
        },

        notes: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "食材备注..."
          }
        }
      }
    },

    instructions: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加步骤"
      },
      items: {
        "ui:order": ["step", "title", "description", "duration", "temperature", "tips", "image"],

        step: {
          "ui:widget": "updown",
          "ui:options": {
            help: "步骤序号"
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "步骤标题..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "详细描述这个步骤的操作..."
          }
        },

        duration: {
          "ui:widget": "updown",
          "ui:options": {
            help: "这个步骤需要的时间（分钟）"
          }
        },

        temperature: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "温度设置，如：大火、中火、小火..."
          }
        },

        tips: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "这个步骤的小贴士..."
          }
        },

        image: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "步骤图片URL..."
          }
        }
      }
    },

    nutrition: {
      "ui:order": ["calories", "protein", "carbohydrates", "fat", "fiber", "sodium", "sugar"],

      calories: {
        "ui:widget": "updown",
        "ui:options": {
          help: "每份热量（卡路里）"
        }
      },

      protein: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份蛋白质含量（克）"
        }
      },

      carbohydrates: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份碳水化合物含量（克）"
        }
      },

      fat: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份脂肪含量（克）"
        }
      },

      fiber: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份膳食纤维含量（克）"
        }
      },

      sodium: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份钠含量（毫克）"
        }
      },

      sugar: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "每份糖分含量（克）"
        }
      }
    },

    equipment: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加厨具"
      },
      items: {
        "ui:order": ["name", "essential", "alternative"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "厨具名称..."
          }
        },

        essential: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为必需厨具"
          }
        },

        alternative: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代方案..."
          }
        }
      }
    },

    tips: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加技巧"
      },
      items: {
        "ui:order": ["category", "title", "description"],

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "ingredient", label: "🥬 食材选择" },
              { value: "technique", label: "🔥 烹饪技巧" },
              { value: "timing", label: "⏰ 时间掌握" },
              { value: "storage", label: "📦 保存方法" },
              { value: "serving", label: "🍽️ 装盘摆盘" },
              { value: "variation", label: "🔄 变化做法" }
            ]
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "技巧标题..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "详细说明..."
          }
        }
      }
    },

    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加图片"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "type"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "main", label: "🖼️ 主图" },
              { value: "ingredient", label: "🥬 食材图" },
              { value: "step", label: "📋 步骤图" },
              { value: "final", label: "✨ 成品图" }
            ]
          }
        }
      }
    },

    video: {
      "ui:order": ["url", "title", "duration", "thumbnail", "platform"],

      url: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "视频链接..."
        }
      },

      title: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "视频标题..."
        }
      },

      duration: {
        "ui:widget": "updown",
        "ui:options": {
          help: "视频时长（秒）"
        }
      },

      thumbnail: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "视频缩略图URL..."
        }
      },

      platform: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "youtube", label: "📺 YouTube" },
            { value: "bilibili", label: "📺 哔哩哔哩" },
            { value: "tiktok", label: "📱 TikTok" },
            { value: "self-hosted", label: "🏠 自托管" },
            { value: "other", label: "🌐 其他" }
          ]
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "食谱发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default recipeConfig;
