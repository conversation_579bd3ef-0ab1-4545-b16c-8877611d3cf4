import { BlockFormConfig } from '../types';

const jobPostingConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "职位标题",
        minLength: 5,
        maxLength: 150,
        default: "高级前端工程师 - React/TypeScript"
      },
      slug: {
        type: "string",
        title: "职位标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "senior-frontend-engineer-react-typescript"
      },
      description: {
        type: "string",
        title: "职位描述",
        minLength: 100,
        maxLength: 5000,
        default: "我们正在寻找一位经验丰富的高级前端工程师加入我们的技术团队。你将负责开发和维护我们的Web应用程序，使用React、TypeScript等现代前端技术栈。我们提供灵活的工作环境、有竞争力的薪资待遇，以及良好的职业发展机会。"
      },
      department: {
        type: "string",
        title: "所属部门",
        minLength: 2,
        maxLength: 100,
        default: "技术部"
      },
      level: {
        type: "string",
        title: "职位级别",
        enum: ["intern", "junior", "mid", "senior", "lead", "principal", "director"],
        default: "senior"
      },
      type: {
        type: "string",
        title: "工作类型",
        enum: ["full-time", "part-time", "contract", "freelance", "internship"],
        default: "full-time"
      },
      workMode: {
        type: "string",
        title: "工作模式",
        enum: ["remote", "onsite", "hybrid"],
        default: "hybrid"
      },
      location: {
        type: "object",
        title: "工作地点",
        properties: {
          city: {
            type: "string",
            title: "城市",
            maxLength: 50,
            default: "上海"
          },
          country: {
            type: "string",
            title: "国家",
            maxLength: 50,
            default: "中国"
          },
          address: {
            type: "string",
            title: "详细地址",
            maxLength: 200,
            default: "上海市浦东新区张江高科技园区"
          },
          allowRemote: {
            type: "boolean",
            title: "支持远程工作",
            default: true
          }
        },
        required: ["city", "country"]
      },
      salary: {
        type: "object",
        title: "薪资待遇",
        properties: {
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          minSalary: {
            type: "integer",
            title: "最低薪资",
            minimum: 0,
            default: 25000
          },
          maxSalary: {
            type: "integer",
            title: "最高薪资",
            minimum: 0,
            default: 40000
          },
          period: {
            type: "string",
            title: "薪资周期",
            enum: ["monthly", "yearly", "hourly"],
            default: "monthly"
          },
          negotiable: {
            type: "boolean",
            title: "薪资面议",
            default: false
          },
          showSalary: {
            type: "boolean",
            title: "公开薪资",
            default: true
          }
        },
        required: ["currency", "period"]
      },
      requirements: {
        type: "object",
        title: "职位要求",
        properties: {
          education: {
            type: "string",
            title: "学历要求",
            enum: ["high-school", "associate", "bachelor", "master", "phd", "no-requirement"],
            default: "bachelor"
          },
          experience: {
            type: "object",
            title: "工作经验",
            properties: {
              minYears: {
                type: "integer",
                title: "最少年限",
                minimum: 0,
                maximum: 30,
                default: 3
              },
              maxYears: {
                type: "integer",
                title: "最多年限",
                minimum: 0,
                maximum: 30,
                default: 8
              },
              required: {
                type: "boolean",
                title: "经验必需",
                default: true
              }
            }
          },
          skills: {
            type: "array",
            title: "技能要求",
            items: {
              type: "object",
              properties: {
                skill: {
                  type: "string",
                  title: "技能名称",
                  minLength: 2,
                  maxLength: 50
                },
                level: {
                  type: "string",
                  title: "要求水平",
                  enum: ["basic", "intermediate", "advanced", "expert"],
                  default: "intermediate"
                },
                required: {
                  type: "boolean",
                  title: "是否必需",
                  default: true
                },
                yearsOfExperience: {
                  type: "integer",
                  title: "使用年限",
                  minimum: 0,
                  maximum: 20
                }
              },
              required: ["skill", "level", "required"]
            },
            default: [
              {
                skill: "React",
                level: "advanced",
                required: true,
                yearsOfExperience: 3
              },
              {
                skill: "TypeScript",
                level: "intermediate",
                required: true,
                yearsOfExperience: 2
              },
              {
                skill: "JavaScript",
                level: "advanced",
                required: true,
                yearsOfExperience: 5
              }
            ],
            maxItems: 20,
            minItems: 1
          },
          languages: {
            type: "array",
            title: "语言要求",
            items: {
              type: "object",
              properties: {
                language: {
                  type: "string",
                  title: "语言",
                  enum: ["chinese", "english", "japanese", "korean", "spanish", "french", "german"],
                  default: "chinese"
                },
                level: {
                  type: "string",
                  title: "水平要求",
                  enum: ["basic", "conversational", "business", "fluent", "native"],
                  default: "conversational"
                },
                required: {
                  type: "boolean",
                  title: "是否必需",
                  default: true
                }
              },
              required: ["language", "level", "required"]
            },
            default: [
              {
                language: "chinese",
                level: "native",
                required: true
              },
              {
                language: "english",
                level: "business",
                required: false
              }
            ],
            maxItems: 10,
            minItems: 0
          }
        }
      },
      responsibilities: {
        type: "array",
        title: "工作职责",
        items: {
          type: "string",
          minLength: 10,
          maxLength: 300
        },
        default: [
          "负责前端产品的架构设计和技术选型",
          "开发和维护高质量的Web应用程序",
          "与产品经理、设计师和后端工程师协作",
          "优化前端性能，提升用户体验",
          "参与代码审查，确保代码质量",
          "指导初级工程师，分享技术经验"
        ],
        maxItems: 15,
        minItems: 1
      },
      benefits: {
        type: "array",
        title: "福利待遇",
        items: {
          type: "object",
          properties: {
            category: {
              type: "string",
              title: "福利类别",
              enum: ["insurance", "vacation", "bonus", "stock", "training", "equipment", "wellness", "other"],
              default: "insurance"
            },
            title: {
              type: "string",
              title: "福利名称",
              minLength: 2,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "福利描述",
              maxLength: 300
            }
          },
          required: ["category", "title"]
        },
        default: [
          {
            category: "insurance",
            title: "五险一金",
            description: "完善的社会保险和住房公积金"
          },
          {
            category: "vacation",
            title: "带薪年假",
            description: "15天带薪年假，另有病假、婚假等"
          },
          {
            category: "bonus",
            title: "年终奖金",
            description: "根据个人和公司业绩发放年终奖"
          },
          {
            category: "training",
            title: "培训机会",
            description: "技术培训、会议参与、学习津贴"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      company: {
        type: "object",
        title: "公司信息",
        properties: {
          name: {
            type: "string",
            title: "公司名称",
            minLength: 2,
            maxLength: 100,
            default: "TechCorp 科技有限公司"
          },
          logo: {
            type: "string",
            title: "公司Logo",
            format: "uri",
            default: "https://example.com/company-logo.png"
          },
          website: {
            type: "string",
            title: "公司官网",
            format: "uri",
            default: "https://techcorp.com"
          },
          industry: {
            type: "string",
            title: "所属行业",
            maxLength: 100,
            default: "互联网/软件"
          },
          size: {
            type: "string",
            title: "公司规模",
            enum: ["startup", "small", "medium", "large", "enterprise"],
            default: "medium"
          },
          description: {
            type: "string",
            title: "公司简介",
            maxLength: 1000,
            default: "TechCorp是一家专注于前沿技术的创新公司，致力于为客户提供优质的软件解决方案。我们拥有年轻有活力的团队，开放包容的企业文化，以及广阔的发展空间。"
          },
          culture: {
            type: "array",
            title: "企业文化",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["创新", "协作", "成长", "开放"],
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["name"]
      },
      application: {
        type: "object",
        title: "申请方式",
        properties: {
          method: {
            type: "string",
            title: "申请方式",
            enum: ["email", "form", "external-link", "phone"],
            default: "email"
          },
          email: {
            type: "string",
            title: "申请邮箱",
            format: "email",
            default: "<EMAIL>"
          },
          applicationUrl: {
            type: "string",
            title: "申请链接",
            format: "uri"
          },
          phone: {
            type: "string",
            title: "联系电话",
            maxLength: 20
          },
          instructions: {
            type: "string",
            title: "申请说明",
            maxLength: 500,
            default: "请发送简历至邮箱，邮件标题请注明应聘职位。我们会在3个工作日内回复。"
          },
          requiredDocuments: {
            type: "array",
            title: "所需材料",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 100
            },
            default: ["个人简历", "作品集", "学历证明"],
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["method"]
      },
      status: {
        type: "string",
        title: "招聘状态",
        enum: ["draft", "active", "paused", "filled", "cancelled"],
        default: "draft"
      },
      priority: {
        type: "string",
        title: "招聘优先级",
        enum: ["low", "normal", "high", "urgent"],
        default: "normal"
      },
      openings: {
        type: "integer",
        title: "招聘人数",
        minimum: 1,
        maximum: 100,
        default: 1
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      expiryDate: {
        type: "string",
        title: "截止日期",
        format: "date",
        default: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      tags: {
        type: "array",
        title: "职位标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["前端开发", "React", "TypeScript", "远程工作"],
        maxItems: 15,
        minItems: 0
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "高级前端工程师招聘 - React/TypeScript | TechCorp"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "TechCorp招聘高级前端工程师，要求React、TypeScript经验，提供有竞争力薪资和完善福利。支持远程工作，欢迎投递简历。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["前端工程师招聘", "React开发", "TypeScript", "远程工作"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "department", "level", "type", "workMode", "location", "requirements", "responsibilities", "company", "application"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "department", "level", "type", "workMode",
      "location", "salary", "requirements", "responsibilities", "benefits",
      "company", "application", "status", "priority", "openings",
      "publishDate", "expiryDate", "lastUpdated", "tags", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入职位标题...",
        help: "清晰明确的职位标题，包含关键技能"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：senior-frontend-engineer-react",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 8,
        placeholder: "详细描述职位要求、工作内容和发展机会...",
        help: "吸引候选人的详细职位描述"
      }
    },
    
    department: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：技术部、产品部、市场部",
        help: "职位所属的部门"
      }
    },
    
    level: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "intern", label: "🎓 实习生" },
          { value: "junior", label: "🌱 初级" },
          { value: "mid", label: "🌿 中级" },
          { value: "senior", label: "🌳 高级" },
          { value: "lead", label: "👑 技术负责人" },
          { value: "principal", label: "🏆 首席" },
          { value: "director", label: "🎯 总监" }
        ]
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "full-time", label: "💼 全职" },
          { value: "part-time", label: "⏰ 兼职" },
          { value: "contract", label: "📝 合同工" },
          { value: "freelance", label: "🆓 自由职业" },
          { value: "internship", label: "🎓 实习" }
        ]
      }
    },
    
    workMode: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "remote", label: "🏠 远程工作" },
          { value: "onsite", label: "🏢 现场办公" },
          { value: "hybrid", label: "🔄 混合模式" }
        ]
      }
    },

    location: {
      "ui:order": ["city", "country", "address", "allowRemote"],

      city: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "工作城市..."
        }
      },

      country: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "国家..."
        }
      },

      address: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "详细地址..."
        }
      },

      allowRemote: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否支持远程工作"
        }
      }
    },

    salary: {
      "ui:order": ["currency", "minSalary", "maxSalary", "period", "negotiable", "showSalary"],

      currency: {
        "ui:widget": "select"
      },

      minSalary: {
        "ui:widget": "updown",
        "ui:options": {
          help: "最低薪资"
        }
      },

      maxSalary: {
        "ui:widget": "updown",
        "ui:options": {
          help: "最高薪资"
        }
      },

      period: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "monthly", label: "月薪" },
            { value: "yearly", label: "年薪" },
            { value: "hourly", label: "时薪" }
          ]
        }
      },

      negotiable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "薪资是否面议"
        }
      },

      showSalary: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否公开显示薪资"
        }
      }
    },

    requirements: {
      "ui:order": ["education", "experience", "skills", "languages"],

      education: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "high-school", label: "高中" },
            { value: "associate", label: "大专" },
            { value: "bachelor", label: "本科" },
            { value: "master", label: "硕士" },
            { value: "phd", label: "博士" },
            { value: "no-requirement", label: "不限" }
          ]
        }
      },

      experience: {
        "ui:order": ["minYears", "maxYears", "required"],

        minYears: {
          "ui:widget": "updown",
          "ui:options": {
            help: "最少工作年限"
          }
        },

        maxYears: {
          "ui:widget": "updown",
          "ui:options": {
            help: "最多工作年限"
          }
        },

        required: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否必须有工作经验"
          }
        }
      },

      skills: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加技能要求"
        },
        items: {
          "ui:order": ["skill", "level", "required", "yearsOfExperience"],

          skill: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "技能名称..."
            }
          },

          level: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "basic", label: "基础" },
                { value: "intermediate", label: "中级" },
                { value: "advanced", label: "高级" },
                { value: "expert", label: "专家" }
              ]
            }
          },

          required: {
            "ui:widget": "checkbox",
            "ui:options": {
              help: "是否为必需技能"
            }
          },

          yearsOfExperience: {
            "ui:widget": "updown",
            "ui:options": {
              help: "要求使用年限"
            }
          }
        }
      },

      languages: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加语言要求"
        },
        items: {
          "ui:order": ["language", "level", "required"],

          language: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "chinese", label: "中文" },
                { value: "english", label: "英语" },
                { value: "japanese", label: "日语" },
                { value: "korean", label: "韩语" },
                { value: "spanish", label: "西班牙语" },
                { value: "french", label: "法语" },
                { value: "german", label: "德语" }
              ]
            }
          },

          level: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "basic", label: "基础" },
                { value: "conversational", label: "日常交流" },
                { value: "business", label: "商务水平" },
                { value: "fluent", label: "流利" },
                { value: "native", label: "母语" }
              ]
            }
          },

          required: {
            "ui:widget": "checkbox",
            "ui:options": {
              help: "是否为必需语言"
            }
          }
        }
      }
    },

    responsibilities: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加工作职责"
      },
      items: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "描述具体的工作职责..."
        }
      }
    },

    benefits: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加福利待遇"
      },
      items: {
        "ui:order": ["category", "title", "description"],

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "insurance", label: "🛡️ 保险" },
              { value: "vacation", label: "🏖️ 假期" },
              { value: "bonus", label: "💰 奖金" },
              { value: "stock", label: "📈 股权" },
              { value: "training", label: "📚 培训" },
              { value: "equipment", label: "💻 设备" },
              { value: "wellness", label: "🏃 健康" },
              { value: "other", label: "🎁 其他" }
            ]
          }
        },

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "福利名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "福利详细描述..."
          }
        }
      }
    },

    company: {
      "ui:order": ["name", "logo", "website", "industry", "size", "description", "culture"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "公司名称..."
        }
      },

      logo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "公司Logo URL..."
        }
      },

      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "公司官网..."
        }
      },

      industry: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "所属行业..."
        }
      },

      size: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "startup", label: "🚀 初创公司 (1-20人)" },
            { value: "small", label: "🏪 小型公司 (21-100人)" },
            { value: "medium", label: "🏢 中型公司 (101-500人)" },
            { value: "large", label: "🏬 大型公司 (501-5000人)" },
            { value: "enterprise", label: "🏭 企业集团 (5000+人)" }
          ]
        }
      },

      description: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 4,
          placeholder: "公司简介..."
        }
      },

      culture: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加企业文化"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "企业文化关键词..."
          }
        }
      }
    },

    application: {
      "ui:order": ["method", "email", "applicationUrl", "phone", "instructions", "requiredDocuments"],

      method: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "email", label: "📧 邮箱申请" },
            { value: "form", label: "📝 在线表单" },
            { value: "external-link", label: "🔗 外部链接" },
            { value: "phone", label: "📞 电话联系" }
          ]
        }
      },

      email: {
        "ui:widget": "email",
        "ui:options": {
          placeholder: "<EMAIL>"
        }
      },

      applicationUrl: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "申请页面链接..."
        }
      },

      phone: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "联系电话..."
        }
      },

      instructions: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "申请说明和注意事项..."
        }
      },

      requiredDocuments: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加所需材料"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "所需材料..."
          }
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "active", label: "✅ 招聘中" },
          { value: "paused", label: "⏸️ 暂停" },
          { value: "filled", label: "✅ 已招满" },
          { value: "cancelled", label: "❌ 已取消" }
        ]
      }
    },

    priority: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "low", label: "🟢 低优先级" },
          { value: "normal", label: "🟡 普通" },
          { value: "high", label: "🟠 高优先级" },
          { value: "urgent", label: "🔴 紧急" }
        ]
      }
    },

    openings: {
      "ui:widget": "updown",
      "ui:options": {
        help: "计划招聘人数"
      }
    },

    publishDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "职位发布日期"
      }
    },

    expiryDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "招聘截止日期"
      }
    },

    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default jobPostingConfig;
