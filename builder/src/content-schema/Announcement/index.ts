import { BlockFormConfig } from '../types';

const announcementConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Announcement Title",
        minLength: 5,
        maxLength: 150,
        default: "Important System Maintenance Scheduled"
      },
      content: {
        type: "string",
        title: "Announcement Content",
        minLength: 20,
        default: "# System Maintenance Notice\n\nWe will be performing scheduled maintenance on our systems to improve performance and security.\n\n## Maintenance Details\n\n- **Date**: Sunday, March 15, 2024\n- **Time**: 2:00 AM - 6:00 AM EST\n- **Expected Downtime**: 4 hours\n\n## What to Expect\n\nDuring this maintenance window:\n- All services will be temporarily unavailable\n- No data will be lost\n- All features will be restored after maintenance\n\nWe apologize for any inconvenience and appreciate your patience."
      },
      priority: {
        type: "string",
        title: "Priority Level",
        enum: ["low", "normal", "high", "urgent", "critical"],
        default: "normal"
      },
      type: {
        type: "string",
        title: "Announcement Type",
        enum: ["general", "maintenance", "feature", "security", "policy", "event"],
        default: "general"
      },
      targetAudience: {
        type: "array",
        title: "Target Audience",
        items: {
          type: "string",
          enum: ["all-users", "free-users", "premium-users", "enterprise-users", "developers", "administrators"]
        },
        default: ["all-users"],
        minItems: 1,
        uniqueItems: true
      },
      publishDate: {
        type: "string",
        title: "Publish Date",
        format: "date-time",
        default: new Date().toISOString()
      },
      expiryDate: {
        type: "string",
        title: "Expiry Date",
        format: "date-time"
      },
      isSticky: {
        type: "boolean",
        title: "Pin to Top",
        default: false
      },
      showBanner: {
        type: "boolean",
        title: "Show as Banner",
        default: false
      },
      bannerColor: {
        type: "string",
        title: "Banner Color",
        enum: ["blue", "green", "yellow", "red", "purple", "gray"],
        default: "blue"
      },
      actionButton: {
        type: "object",
        title: "Action Button",
        properties: {
          enabled: {
            type: "boolean",
            title: "Enable Action Button",
            default: false
          },
          text: {
            type: "string",
            title: "Button Text",
            minLength: 1,
            maxLength: 50,
            default: "Learn More"
          },
          url: {
            type: "string",
            title: "Button URL",
            format: "uri",
            default: "https://example.com"
          },
          style: {
            type: "string",
            title: "Button Style",
            enum: ["primary", "secondary", "outline"],
            default: "primary"
          }
        }
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["maintenance", "system", "scheduled"],
        maxItems: 10,
        minItems: 0
      },
      author: {
        type: "object",
        title: "Author Information",
        properties: {
          name: {
            type: "string",
            title: "Author Name",
            minLength: 2,
            maxLength: 100,
            default: "System Administrator"
          },
          email: {
            type: "string",
            title: "Author Email",
            format: "email",
            default: "<EMAIL>"
          },
          role: {
            type: "string",
            title: "Author Role",
            default: "Administrator"
          }
        },
        required: ["name"]
      },
      status: {
        type: "string",
        title: "Status",
        enum: ["draft", "scheduled", "published", "archived"],
        default: "draft"
      },
      readTime: {
        type: "integer",
        title: "Estimated Read Time (minutes)",
        minimum: 1,
        maximum: 30,
        default: 2
      },
      isUrgent: {
        type: "boolean",
        title: "Urgent Announcement",
        default: false
      },
      notificationSettings: {
        type: "object",
        title: "Notification Settings",
        properties: {
          sendEmail: {
            type: "boolean",
            title: "Send Email Notification",
            default: false
          },
          sendPush: {
            type: "boolean",
            title: "Send Push Notification",
            default: false
          },
          sendSMS: {
            type: "boolean",
            title: "Send SMS Notification",
            default: false
          }
        }
      },
      relatedLinks: {
        type: "array",
        title: "Related Links",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "Link Title",
              minLength: 2,
              maxLength: 100
            },
            url: {
              type: "string",
              title: "URL",
              format: "uri"
            },
            description: {
              type: "string",
              title: "Description",
              maxLength: 200
            }
          },
          required: ["title", "url"]
        },
        default: [],
        maxItems: 5,
        minItems: 0
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "Important System Maintenance Scheduled | Company Updates"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "We will be performing scheduled maintenance on our systems. Learn about the maintenance schedule and what to expect during the downtime."
          }
        }
      }
    },
    required: ["title", "content", "priority", "type", "targetAudience", "publishDate", "author"]
  },
  uiSchema: {
    "ui:order": [
      "title", "content", "priority", "type", "targetAudience", 
      "publishDate", "expiryDate", "isSticky", "showBanner", "bannerColor",
      "actionButton", "tags", "author", "status", "readTime", "isUrgent",
      "notificationSettings", "relatedLinks", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter announcement title...",
        help: "A clear, attention-grabbing title for your announcement"
      }
    },
    
    content: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Write your announcement content...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 500,
        minHeight: 300
      }
    },
    
    priority: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "low", label: "🟢 Low Priority" },
          { value: "normal", label: "🔵 Normal Priority" },
          { value: "high", label: "🟡 High Priority" },
          { value: "urgent", label: "🟠 Urgent" },
          { value: "critical", label: "🔴 Critical" }
        ]
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "general", label: "📢 General" },
          { value: "maintenance", label: "🔧 Maintenance" },
          { value: "feature", label: "✨ Feature Update" },
          { value: "security", label: "🔒 Security" },
          { value: "policy", label: "📋 Policy" },
          { value: "event", label: "🎉 Event" }
        ]
      }
    },
    
    targetAudience: {
      "ui:widget": "checkboxes",
      "ui:options": {
        enumOptions: [
          { value: "all-users", label: "All Users" },
          { value: "free-users", label: "Free Users" },
          { value: "premium-users", label: "Premium Users" },
          { value: "enterprise-users", label: "Enterprise Users" },
          { value: "developers", label: "Developers" },
          { value: "administrators", label: "Administrators" }
        ],
        inline: true
      }
    },
    
    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "When this announcement should be published"
      }
    },
    
    expiryDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "Optional: When this announcement should be automatically hidden"
      }
    },
    
    isSticky: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Pin this announcement to the top of the list"
      }
    },
    
    showBanner: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Display this announcement as a banner across the top of pages"
      }
    },
    
    bannerColor: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "blue", label: "🔵 Blue" },
          { value: "green", label: "🟢 Green" },
          { value: "yellow", label: "🟡 Yellow" },
          { value: "red", label: "🔴 Red" },
          { value: "purple", label: "🟣 Purple" },
          { value: "gray", label: "⚫ Gray" }
        ]
      }
    },
    
    actionButton: {
      "ui:order": ["enabled", "text", "url", "style"],
      
      enabled: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "Add an action button to this announcement"
        }
      },
      
      text: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Button text..."
        }
      },
      
      url: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://example.com"
        }
      },
      
      style: {
        "ui:widget": "select"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    author: {
      "ui:order": ["name", "email", "role"],
      
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Author name..."
        }
      },
      
      email: {
        "ui:widget": "email",
        "ui:options": {
          placeholder: "<EMAIL>"
        }
      },
      
      role: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Author role..."
        }
      }
    },
    
    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 Draft" },
          { value: "scheduled", label: "⏰ Scheduled" },
          { value: "published", label: "✅ Published" },
          { value: "archived", label: "📦 Archived" }
        ]
      }
    },
    
    readTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "Estimated time to read this announcement"
      }
    },
    
    isUrgent: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Mark as urgent to highlight this announcement"
      }
    },
    
    notificationSettings: {
      "ui:order": ["sendEmail", "sendPush", "sendSMS"],
      
      sendEmail: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "Send email notifications to target audience"
        }
      },
      
      sendPush: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "Send push notifications to mobile users"
        }
      },
      
      sendSMS: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "Send SMS notifications (premium feature)"
        }
      }
    },
    
    relatedLinks: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Related Link"
      },
      items: {
        "ui:order": ["title", "url", "description"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Link title..."
          }
        },
        
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "https://example.com"
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Optional description..."
          }
        }
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO title for this announcement...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this announcement...",
          help: "Description that appears in search engine results"
        }
      }
    }
  }
};

export default announcementConfig;
