import { BlockFormConfig } from '../types';

const movieConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "电影标题",
        minLength: 2,
        maxLength: 200,
        default: "流浪地球2 - 科幻史诗巨制的视觉盛宴"
      },
      slug: {
        type: "string",
        title: "电影标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "wandering-earth-2-sci-fi-epic"
      },
      description: {
        type: "string",
        title: "电影简介",
        minLength: 50,
        maxLength: 2000,
        default: "《流浪地球2》是一部宏大的科幻史诗电影，讲述了人类面临太阳危机时，通过国际合作建造行星发动机拯救地球的故事。影片以震撼的视觉效果和深刻的人文思考，展现了人类在绝境中的坚韧与希望。"
      },
      originalTitle: {
        type: "string",
        title: "原始标题",
        maxLength: 200,
        default: "The Wandering Earth II"
      },
      genre: {
        type: "array",
        title: "电影类型",
        items: {
          type: "string",
          enum: ["action", "adventure", "animation", "comedy", "crime", "documentary", "drama", "family", "fantasy", "history", "horror", "music", "mystery", "romance", "sci-fi", "thriller", "war", "western"]
        },
        default: ["sci-fi", "action", "drama"],
        minItems: 1,
        maxItems: 5
      },
      releaseDate: {
        type: "string",
        title: "上映日期",
        format: "date",
        default: "2023-01-22"
      },
      duration: {
        type: "integer",
        title: "片长(分钟)",
        minimum: 1,
        maximum: 600,
        default: 173
      },
      rating: {
        type: "object",
        title: "评分信息",
        properties: {
          imdb: {
            type: "number",
            title: "IMDb评分",
            minimum: 0,
            maximum: 10,
            default: 7.4
          },
          douban: {
            type: "number",
            title: "豆瓣评分",
            minimum: 0,
            maximum: 10,
            default: 8.3
          },
          rottenTomatoes: {
            type: "number",
            title: "烂番茄评分",
            minimum: 0,
            maximum: 100,
            default: 75
          },
          metacritic: {
            type: "number",
            title: "Metacritic评分",
            minimum: 0,
            maximum: 100,
            default: 68
          }
        }
      },
      director: {
        type: "array",
        title: "导演",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "姓名",
              maxLength: 100
            },
            bio: {
              type: "string",
              title: "简介",
              maxLength: 500
            },
            avatar: {
              type: "string",
              title: "头像",
              format: "uri"
            }
          },
          required: ["name"]
        },
        default: [
          {
            name: "郭帆",
            bio: "中国著名导演，以科幻电影见长，代表作品包括《流浪地球》系列",
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
          }
        ],
        maxItems: 10,
        minItems: 1
      },
      cast: {
        type: "array",
        title: "主要演员",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "演员姓名",
              maxLength: 100
            },
            character: {
              type: "string",
              title: "角色名称",
              maxLength: 100
            },
            bio: {
              type: "string",
              title: "演员简介",
              maxLength: 500
            },
            avatar: {
              type: "string",
              title: "头像",
              format: "uri"
            },
            isLead: {
              type: "boolean",
              title: "是否主演",
              default: false
            }
          },
          required: ["name", "character"]
        },
        default: [
          {
            name: "吴京",
            character: "刘培强",
            bio: "中国著名演员，动作片明星",
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
            isLead: true
          },
          {
            name: "刘德华",
            character: "图恒宇",
            bio: "香港著名演员、歌手",
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
            isLead: true
          },
          {
            name: "李雪健",
            character: "周喆直",
            bio: "中国著名演员，实力派表演艺术家",
            avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
            isLead: false
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      production: {
        type: "object",
        title: "制作信息",
        properties: {
          studio: {
            type: "array",
            title: "制片公司",
            items: {
              type: "string",
              maxLength: 100
            },
            default: ["中国电影股份有限公司", "北京文化传媒有限公司"],
            maxItems: 10
          },
          distributor: {
            type: "array",
            title: "发行公司",
            items: {
              type: "string",
              maxLength: 100
            },
            default: ["中国电影股份有限公司"],
            maxItems: 10
          },
          budget: {
            type: "number",
            title: "制作预算(万元)",
            minimum: 0,
            default: 60000
          },
          boxOffice: {
            type: "object",
            title: "票房收入",
            properties: {
              domestic: {
                type: "number",
                title: "国内票房(万元)",
                minimum: 0,
                default: 406800
              },
              international: {
                type: "number",
                title: "国际票房(万元)",
                minimum: 0,
                default: 5200
              },
              total: {
                type: "number",
                title: "全球票房(万元)",
                minimum: 0,
                default: 412000
              }
            }
          }
        }
      },
      technical: {
        type: "object",
        title: "技术规格",
        properties: {
          aspectRatio: {
            type: "string",
            title: "画面比例",
            default: "2.35:1"
          },
          soundMix: {
            type: "array",
            title: "音响格式",
            items: {
              type: "string"
            },
            default: ["Dolby Atmos", "DTS:X", "IMAX Enhanced"],
            maxItems: 10
          },
          color: {
            type: "string",
            title: "色彩",
            enum: ["color", "black-and-white", "colorized"],
            default: "color"
          },
          filmingFormat: {
            type: "array",
            title: "拍摄格式",
            items: {
              type: "string"
            },
            default: ["IMAX", "35mm", "Digital"],
            maxItems: 10
          }
        }
      },
      awards: {
        type: "array",
        title: "获奖记录",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "奖项名称",
              maxLength: 200
            },
            category: {
              type: "string",
              title: "奖项类别",
              maxLength: 100
            },
            year: {
              type: "integer",
              title: "获奖年份",
              minimum: 1900,
              maximum: 2100
            },
            result: {
              type: "string",
              title: "获奖结果",
              enum: ["won", "nominated"],
              default: "won"
            },
            organization: {
              type: "string",
              title: "颁奖机构",
              maxLength: 100
            }
          },
          required: ["name", "category", "year", "result"]
        },
        default: [
          {
            name: "最佳视觉效果",
            category: "技术奖项",
            year: 2023,
            result: "won",
            organization: "中国电影金鸡奖"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      images: {
        type: "array",
        title: "电影图片",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            type: {
              type: "string",
              title: "图片类型",
              enum: ["poster", "still", "behind-scenes", "promotional"],
              default: "poster"
            }
          },
          required: ["url", "caption", "type"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1489599511986-c2e8b3b5b6b8?w=800&h=1200&fit=crop",
            caption: "《流浪地球2》官方海报",
            alt: "流浪地球2电影海报",
            type: "poster"
          },
          {
            url: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
            caption: "电影剧照 - 行星发动机场景",
            alt: "流浪地球2剧照",
            type: "still"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      tags: {
        type: "array",
        title: "电影标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["科幻电影", "中国电影", "视觉特效", "太空题材"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "流浪地球2 - 科幻史诗巨制电影详情 | 电影资讯"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "《流浪地球2》是一部宏大的科幻史诗电影，震撼视觉效果展现人类拯救地球的故事。查看详细剧情、演员阵容、评分和幕后花絮。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["流浪地球2", "科幻电影", "中国电影", "吴京", "刘德华"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "genre", "releaseDate", "duration", "director"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "originalTitle", "genre", "releaseDate", "duration",
      "rating", "director", "cast", "production", "technical", "awards", "images",
      "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入电影标题...",
        help: "电影的中文标题"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：wandering-earth-2",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 5,
        placeholder: "详细描述电影的剧情、特色和亮点...",
        help: "电影的详细介绍和剧情简介"
      }
    },
    
    originalTitle: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "电影的原始标题（如英文名）..."
      }
    },
    
    genre: {
      "ui:widget": "checkboxes",
      "ui:options": {
        enumOptions: [
          { value: "action", label: "🎬 动作" },
          { value: "adventure", label: "🗺️ 冒险" },
          { value: "animation", label: "🎨 动画" },
          { value: "comedy", label: "😄 喜剧" },
          { value: "crime", label: "🔍 犯罪" },
          { value: "documentary", label: "📹 纪录片" },
          { value: "drama", label: "🎭 剧情" },
          { value: "family", label: "👨‍👩‍👧‍👦 家庭" },
          { value: "fantasy", label: "🧙‍♂️ 奇幻" },
          { value: "history", label: "📚 历史" },
          { value: "horror", label: "👻 恐怖" },
          { value: "music", label: "🎵 音乐" },
          { value: "mystery", label: "🔮 悬疑" },
          { value: "romance", label: "💕 爱情" },
          { value: "sci-fi", label: "🚀 科幻" },
          { value: "thriller", label: "😱 惊悚" },
          { value: "war", label: "⚔️ 战争" },
          { value: "western", label: "🤠 西部" }
        ]
      }
    },

    releaseDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "电影首映或上映日期"
      }
    },

    duration: {
      "ui:widget": "updown",
      "ui:options": {
        help: "电影时长（分钟）"
      }
    },

    rating: {
      "ui:order": ["imdb", "douban", "rottenTomatoes", "metacritic"],

      imdb: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "IMDb评分（0-10分）"
        }
      },

      douban: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "豆瓣评分（0-10分）"
        }
      },

      rottenTomatoes: {
        "ui:widget": "updown",
        "ui:options": {
          help: "烂番茄评分（0-100%）"
        }
      },

      metacritic: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Metacritic评分（0-100分）"
        }
      }
    },

    director: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加导演"
      },
      items: {
        "ui:order": ["name", "bio", "avatar"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "导演姓名..."
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "导演简介..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "导演头像URL..."
          }
        }
      }
    },

    cast: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加演员"
      },
      items: {
        "ui:order": ["name", "character", "bio", "avatar", "isLead"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演员姓名..."
          }
        },

        character: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "角色名称..."
          }
        },

        bio: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "演员简介..."
          }
        },

        avatar: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "演员头像URL..."
          }
        },

        isLead: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为主演"
          }
        }
      }
    },

    production: {
      "ui:order": ["studio", "distributor", "budget", "boxOffice"],

      studio: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加制片公司"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "制片公司名称..."
          }
        }
      },

      distributor: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加发行公司"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "发行公司名称..."
          }
        }
      },

      budget: {
        "ui:widget": "updown",
        "ui:options": {
          help: "制作预算（万元）"
        }
      },

      boxOffice: {
        "ui:order": ["domestic", "international", "total"],

        domestic: {
          "ui:widget": "updown",
          "ui:options": {
            help: "国内票房（万元）"
          }
        },

        international: {
          "ui:widget": "updown",
          "ui:options": {
            help: "国际票房（万元）"
          }
        },

        total: {
          "ui:widget": "updown",
          "ui:options": {
            help: "全球总票房（万元）"
          }
        }
      }
    },

    technical: {
      "ui:order": ["aspectRatio", "soundMix", "color", "filmingFormat"],

      aspectRatio: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "画面比例，如：2.35:1"
        }
      },

      soundMix: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加音响格式"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "音响格式..."
          }
        }
      },

      color: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "color", label: "🌈 彩色" },
            { value: "black-and-white", label: "⚫ 黑白" },
            { value: "colorized", label: "🎨 着色" }
          ]
        }
      },

      filmingFormat: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加拍摄格式"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "拍摄格式..."
          }
        }
      }
    },

    awards: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加奖项"
      },
      items: {
        "ui:order": ["name", "category", "year", "result", "organization"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "奖项名称..."
          }
        },

        category: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "奖项类别..."
          }
        },

        year: {
          "ui:widget": "updown",
          "ui:options": {
            help: "获奖年份"
          }
        },

        result: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "won", label: "🏆 获奖" },
              { value: "nominated", label: "🎯 提名" }
            ]
          }
        },

        organization: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "颁奖机构..."
          }
        }
      }
    },

    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加图片"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "type"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "图片说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        type: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "poster", label: "🎬 海报" },
              { value: "still", label: "📸 剧照" },
              { value: "behind-scenes", label: "🎥 幕后" },
              { value: "promotional", label: "📢 宣传" }
            ]
          }
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "内容发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default movieConfig;
