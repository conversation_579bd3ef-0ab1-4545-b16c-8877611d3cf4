import { BlockFormConfig } from '../types';

const productConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      name: {
        type: "string",
        title: "Product Name",
        minLength: 2,
        maxLength: 100,
        default: "Premium Analytics Dashboard"
      },
      slug: {
        type: "string",
        title: "Product Slug",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 2,
        maxLength: 100,
        default: "premium-analytics-dashboard"
      },
      shortDescription: {
        type: "string",
        title: "Short Description",
        minLength: 10,
        maxLength: 200,
        default: "Powerful analytics dashboard with real-time insights and customizable reports for data-driven decision making."
      },
      description: {
        type: "string",
        title: "Full Description",
        minLength: 50,
        default: "# Premium Analytics Dashboard\n\nOur Premium Analytics Dashboard provides comprehensive insights into your business performance with real-time data visualization, customizable reports, and advanced analytics features.\n\n## Key Benefits\n\n- **Real-time Data**: Get instant insights as your data updates\n- **Custom Reports**: Create tailored reports for your specific needs\n- **Advanced Analytics**: Leverage machine learning for predictive insights\n- **Team Collaboration**: Share insights and collaborate with your team"
      },
      category: {
        type: "string",
        title: "Product Category",
        minLength: 2,
        maxLength: 50,
        default: "Analytics & Reporting"
      },
      features: {
        type: "array",
        title: "Key Features",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Feature Name",
              minLength: 2,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "Feature Description",
              minLength: 10,
              maxLength: 300
            },
            icon: {
              type: "string",
              title: "Icon Name",
              default: "BarChart"
            }
          },
          required: ["name", "description"]
        },
        default: [
          {
            name: "Real-time Analytics",
            description: "Monitor your key metrics in real-time with live data updates and instant notifications.",
            icon: "Activity"
          },
          {
            name: "Custom Dashboards",
            description: "Create personalized dashboards with drag-and-drop widgets tailored to your needs.",
            icon: "Layout"
          },
          {
            name: "Advanced Reporting",
            description: "Generate comprehensive reports with automated scheduling and export options.",
            icon: "FileText"
          }
        ],
        maxItems: 10,
        minItems: 1
      },
      pricing: {
        type: "object",
        title: "Pricing Information",
        properties: {
          currency: {
            type: "string",
            title: "Currency",
            enum: ["USD", "EUR", "GBP", "JPY", "CNY"],
            default: "USD"
          },
          basePrice: {
            type: "number",
            title: "Base Price",
            minimum: 0,
            default: 99.99
          },
          billingCycle: {
            type: "string",
            title: "Billing Cycle",
            enum: ["monthly", "yearly", "one-time"],
            default: "monthly"
          },
          discountPrice: {
            type: "number",
            title: "Discount Price",
            minimum: 0,
            default: 79.99
          },
          hasFreeTrial: {
            type: "boolean",
            title: "Free Trial Available",
            default: true
          },
          trialDays: {
            type: "integer",
            title: "Trial Days",
            minimum: 1,
            maximum: 365,
            default: 14
          }
        },
        required: ["currency", "basePrice", "billingCycle"]
      },
      images: {
        type: "array",
        title: "Product Images",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "Image URL",
              format: "uri",
              default: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600"
            },
            alt: {
              type: "string",
              title: "Alt Text",
              minLength: 5,
              maxLength: 100
            },
            caption: {
              type: "string",
              title: "Caption",
              maxLength: 200
            },
            isPrimary: {
              type: "boolean",
              title: "Primary Image",
              default: false
            }
          },
          required: ["url", "alt"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600",
            alt: "Analytics dashboard showing various charts and metrics",
            caption: "Main dashboard view with real-time analytics",
            isPrimary: true
          }
        ],
        maxItems: 10,
        minItems: 1
      },
      specifications: {
        type: "array",
        title: "Technical Specifications",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Specification Name",
              minLength: 2,
              maxLength: 50
            },
            value: {
              type: "string",
              title: "Specification Value",
              minLength: 1,
              maxLength: 100
            }
          },
          required: ["name", "value"]
        },
        default: [
          {
            name: "Data Sources",
            value: "50+ integrations"
          },
          {
            name: "Data Retention",
            value: "2 years"
          },
          {
            name: "API Rate Limit",
            value: "10,000 requests/hour"
          },
          {
            name: "Export Formats",
            value: "PDF, Excel, CSV, JSON"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      tags: {
        type: "array",
        title: "Product Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["analytics", "dashboard", "reporting", "business-intelligence"],
        maxItems: 10,
        minItems: 0
      },
      status: {
        type: "string",
        title: "Product Status",
        enum: ["draft", "active", "discontinued", "coming-soon"],
        default: "active"
      },
      launchDate: {
        type: "string",
        title: "Launch Date",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "Premium Analytics Dashboard - Advanced Business Intelligence Tool"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Transform your data into actionable insights with our Premium Analytics Dashboard. Real-time analytics, custom reports, and advanced features."
          },
          keywords: {
            type: "array",
            title: "SEO Keywords",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["analytics dashboard", "business intelligence", "data visualization", "reporting tool"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["name", "slug", "shortDescription", "description", "category", "features", "pricing", "images"]
  },
  uiSchema: {
    "ui:order": [
      "name", "slug", "shortDescription", "description", "category", 
      "features", "pricing", "images", "specifications", "tags", 
      "status", "launchDate", "lastUpdated", "seo"
    ],
    
    name: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter product name...",
        help: "A clear, memorable name for your product"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., premium-analytics-dashboard",
        help: "URL-friendly identifier (lowercase letters, numbers, and hyphens only)"
      }
    },
    
    shortDescription: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "Brief description of your product...",
        help: "A concise summary that appears in product listings"
      }
    },
    
    description: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "Detailed product description...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 500,
        minHeight: 300
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., Analytics & Reporting, Development Tools",
        help: "Category to organize your products"
      }
    },
    
    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Feature"
      },
      items: {
        "ui:order": ["name", "description", "icon"],
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Feature name..."
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Describe this feature..."
          }
        },
        
        icon: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Icon name (e.g., BarChart, Activity)",
            help: "Lucide icon name for this feature"
          }
        }
      }
    },
    
    pricing: {
      "ui:order": ["currency", "basePrice", "discountPrice", "billingCycle", "hasFreeTrial", "trialDays"],
      
      currency: {
        "ui:widget": "select"
      },
      
      basePrice: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01
        }
      },
      
      discountPrice: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "Optional discounted price"
        }
      },
      
      billingCycle: {
        "ui:widget": "select"
      },
      
      hasFreeTrial: {
        "ui:widget": "checkbox"
      },
      
      trialDays: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Number of days for free trial"
        }
      }
    },
    
    images: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Image"
      },
      items: {
        "ui:order": ["url", "alt", "caption", "isPrimary"],
        
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "https://example.com/image.jpg"
          }
        },
        
        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Descriptive alt text for accessibility..."
          }
        },
        
        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Optional image caption..."
          }
        },
        
        isPrimary: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "Mark as primary product image"
          }
        }
      }
    },
    
    specifications: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Specification"
      },
      items: {
        "ui:order": ["name", "value"],
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Specification name..."
          }
        },
        
        value: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Specification value..."
          }
        }
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 Draft" },
          { value: "active", label: "✅ Active" },
          { value: "discontinued", label: "❌ Discontinued" },
          { value: "coming-soon", label: "🚀 Coming Soon" }
        ]
      }
    },
    
    launchDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this product was launched"
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help": "Date when this product was last updated"
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder": "SEO title for this product...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this product...",
          help: "Description that appears in search engine results"
        }
      },
      
      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Keyword"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter SEO keyword..."
          }
        }
      }
    }
  }
};

export default productConfig;
