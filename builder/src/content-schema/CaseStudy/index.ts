import { BlockFormConfig } from '../types';

const caseStudyConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Case Study Title",
        minLength: 5,
        maxLength: 150,
        default: "How TechCorp Increased Revenue by 300% with Our Platform"
      },
      slug: {
        type: "string",
        title: "Case Study Slug",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "techcorp-revenue-increase-case-study"
      },
      summary: {
        type: "string",
        title: "Executive Summary",
        minLength: 50,
        maxLength: 500,
        default: "TechCorp, a mid-sized software company, partnered with us to streamline their sales process and improve customer engagement. Within 6 months, they achieved a 300% increase in revenue and reduced customer acquisition costs by 45%."
      },
      client: {
        type: "object",
        title: "Client Information",
        properties: {
          name: {
            type: "string",
            title: "Client Name",
            minLength: 2,
            maxLength: 100,
            default: "TechCorp Solutions"
          },
          industry: {
            type: "string",
            title: "Industry",
            minLength: 2,
            maxLength: 50,
            default: "Software Development"
          },
          size: {
            type: "string",
            title: "Company Size",
            enum: ["startup", "small", "medium", "large", "enterprise"],
            default: "medium"
          },
          location: {
            type: "string",
            title: "Location",
            maxLength: 100,
            default: "San Francisco, CA"
          },
          website: {
            type: "string",
            title: "Website",
            format: "uri",
            default: "https://techcorp.example.com"
          },
          logo: {
            type: "string",
            title: "Client Logo URL",
            format: "uri",
            default: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop"
          }
        },
        required: ["name", "industry", "size"]
      },
      challenge: {
        type: "object",
        title: "Challenge & Problem",
        properties: {
          overview: {
            type: "string",
            title: "Challenge Overview",
            minLength: 50,
            default: "TechCorp was struggling with an inefficient sales process that resulted in long sales cycles, poor lead qualification, and high customer acquisition costs. Their existing tools were disconnected, leading to data silos and missed opportunities."
          },
          painPoints: {
            type: "array",
            title: "Key Pain Points",
            items: {
              type: "string",
              minLength: 10,
              maxLength: 200
            },
            default: [
              "Sales cycle was 40% longer than industry average",
              "Lead qualification process was manual and time-consuming",
              "Customer data was scattered across multiple systems",
              "Sales team spent 60% of time on administrative tasks"
            ],
            maxItems: 10,
            minItems: 1
          },
          impact: {
            type: "string",
            title: "Business Impact",
            minLength: 30,
            default: "These challenges resulted in missed revenue opportunities, frustrated sales teams, and declining customer satisfaction scores."
          }
        },
        required: ["overview", "painPoints"]
      },
      solution: {
        type: "object",
        title: "Solution & Implementation",
        properties: {
          approach: {
            type: "string",
            title: "Solution Approach",
            minLength: 50,
            default: "We implemented a comprehensive sales automation platform that integrated with TechCorp's existing systems, streamlined their lead qualification process, and provided real-time analytics and insights."
          },
          features: {
            type: "array",
            title: "Key Features Implemented",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  title: "Feature Name",
                  minLength: 2,
                  maxLength: 100
                },
                description: {
                  type: "string",
                  title: "Description",
                  minLength: 10,
                  maxLength: 300
                }
              },
              required: ["name", "description"]
            },
            default: [
              {
                name: "Automated Lead Scoring",
                description: "AI-powered lead scoring system that automatically qualifies and prioritizes leads based on behavior and demographics"
              },
              {
                name: "CRM Integration",
                description: "Seamless integration with existing CRM systems to eliminate data silos and improve data accuracy"
              },
              {
                name: "Real-time Analytics",
                description: "Comprehensive dashboard providing real-time insights into sales performance and pipeline health"
              }
            ],
            maxItems: 10,
            minItems: 1
          },
          timeline: {
            type: "string",
            title: "Implementation Timeline",
            default: "6 months"
          },
          team: {
            type: "array",
            title: "Implementation Team",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["Project Manager", "Sales Engineer", "Technical Consultant", "Customer Success Manager"],
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["approach", "features"]
      },
      results: {
        type: "object",
        title: "Results & Outcomes",
        properties: {
          metrics: {
            type: "array",
            title: "Key Metrics",
            items: {
              type: "object",
              properties: {
                metric: {
                  type: "string",
                  title: "Metric Name",
                  minLength: 2,
                  maxLength: 100
                },
                before: {
                  type: "string",
                  title: "Before Value",
                  maxLength: 50
                },
                after: {
                  type: "string",
                  title: "After Value",
                  maxLength: 50
                },
                improvement: {
                  type: "string",
                  title: "Improvement",
                  maxLength: 50
                }
              },
              required: ["metric", "before", "after", "improvement"]
            },
            default: [
              {
                metric: "Monthly Revenue",
                before: "$500K",
                after: "$2M",
                improvement: "+300%"
              },
              {
                metric: "Sales Cycle Length",
                before: "120 days",
                after: "75 days",
                improvement: "-37.5%"
              },
              {
                metric: "Lead Conversion Rate",
                before: "12%",
                after: "28%",
                improvement: "+133%"
              }
            ],
            maxItems: 10,
            minItems: 1
          },
          testimonial: {
            type: "object",
            title: "Client Testimonial",
            properties: {
              quote: {
                type: "string",
                title: "Testimonial Quote",
                minLength: 50,
                default: "The platform completely transformed our sales process. We've seen unprecedented growth and our team is more productive than ever. The ROI was evident within the first quarter."
              },
              author: {
                type: "string",
                title: "Author Name",
                minLength: 2,
                maxLength: 100,
                default: "Sarah Johnson"
              },
              position: {
                type: "string",
                title: "Author Position",
                minLength: 2,
                maxLength: 100,
                default: "VP of Sales, TechCorp Solutions"
              },
              photo: {
                type: "string",
                title: "Author Photo URL",
                format: "uri",
                default: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
              }
            },
            required: ["quote", "author", "position"]
          }
        },
        required: ["metrics"]
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["sales-automation", "revenue-growth", "crm-integration", "lead-generation"],
        maxItems: 10,
        minItems: 0
      },
      category: {
        type: "string",
        title: "Case Study Category",
        minLength: 2,
        maxLength: 50,
        default: "Sales & Marketing"
      },
      publishDate: {
        type: "string",
        title: "Publish Date",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      isPublished: {
        type: "boolean",
        title: "Published",
        default: false
      },
      isFeatured: {
        type: "boolean",
        title: "Featured Case Study",
        default: false
      },
      readTime: {
        type: "integer",
        title: "Estimated Read Time (minutes)",
        minimum: 1,
        maximum: 30,
        default: 8
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "TechCorp Case Study: 300% Revenue Increase | Success Stories"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Discover how TechCorp achieved a 300% revenue increase and reduced sales cycle time by 37.5% using our sales automation platform. Read the full case study."
          },
          keywords: {
            type: "array",
            title: "SEO Keywords",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["case study", "sales automation", "revenue growth", "crm integration", "success story"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "summary", "client", "challenge", "solution", "results", "category"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "summary", "client", "challenge", "solution", "results",
      "tags", "category", "publishDate", "lastUpdated", "isPublished", 
      "isFeatured", "readTime", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter case study title...",
        help: "A compelling title that highlights the key achievement"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., techcorp-revenue-increase-case-study",
        help: "URL-friendly identifier (lowercase letters, numbers, and hyphens only)"
      }
    },
    
    summary: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "Provide a compelling executive summary...",
        help: "A brief overview that captures the key outcomes and value"
      }
    },
    
    client: {
      "ui:order": ["name", "industry", "size", "location", "website", "logo"],
      
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Client company name..."
        }
      },
      
      industry: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., Software Development, Healthcare, Finance"
        }
      },
      
      size: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "startup", label: "Startup (1-10 employees)" },
            { value: "small", label: "Small (11-50 employees)" },
            { value: "medium", label: "Medium (51-200 employees)" },
            { value: "large", label: "Large (201-1000 employees)" },
            { value: "enterprise", label: "Enterprise (1000+ employees)" }
          ]
        }
      },
      
      location: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "City, State/Country"
        }
      },
      
      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "https://client-website.com"
        }
      },
      
      logo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Client logo image URL..."
        }
      }
    },
    
    challenge: {
      "ui:order": ["overview", "painPoints", "impact"],
      
      overview: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 4,
          placeholder: "Describe the main challenge or problem the client faced..."
        }
      },
      
      painPoints: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Pain Point"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Describe a specific pain point..."
          }
        }
      },
      
      impact: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "Explain the business impact of these challenges..."
        }
      }
    },
    
    solution: {
      "ui:order": ["approach", "features", "timeline", "team"],
      
      approach: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 4,
          placeholder: "Describe the solution approach and methodology..."
        }
      },
      
      features: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Feature"
        },
        items: {
          "ui:order": ["name", "description"],
          
          name: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Feature name..."
            }
          },
          
          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "Describe this feature and its benefits..."
            }
          }
        }
      },
      
      timeline: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "e.g., 6 months, 3 weeks, Q1 2024"
        }
      },
      
      team: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Team Member"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Team member role..."
          }
        }
      }
    },
    
    results: {
      "ui:order": ["metrics", "testimonial"],
      
      metrics: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Metric"
        },
        items: {
          "ui:order": ["metric", "before", "after", "improvement"],
          
          metric: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Metric name..."
            }
          },
          
          before: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Before value..."
            }
          },
          
          after: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "After value..."
            }
          },
          
          improvement: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "e.g., +300%, -50%, 2x"
            }
          }
        }
      },
      
      testimonial: {
        "ui:order": ["quote", "author", "position", "photo"],
        
        quote: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 4,
            placeholder: "Client testimonial quote..."
          }
        },
        
        author: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Testimonial author name..."
          }
        },
        
        position: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Author's position and company..."
          }
        },
        
        photo: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Author photo URL..."
          }
        }
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., Sales & Marketing, Customer Success, Product Development"
      }
    },
    
    publishDate: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this case study was published"
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this case study was last updated"
      }
    },
    
    isPublished: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Whether this case study is visible to users"
      }
    },
    
    isFeatured: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Feature this case study on the homepage"
      }
    },
    
    readTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "Estimated time to read this case study"
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO title for this case study...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this case study...",
          help: "Description that appears in search engine results"
        }
      },
      
      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Keyword"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter SEO keyword..."
          }
        }
      }
    }
  }
};

export default caseStudyConfig;
