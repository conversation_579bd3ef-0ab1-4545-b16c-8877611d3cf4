import { BlockFormConfig } from '../types';

const tutorialConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "Tutorial Title",
        minLength: 5,
        maxLength: 150,
        default: "Getting Started with React Hooks"
      },
      slug: {
        type: "string",
        title: "Tutorial Slug",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "getting-started-react-hooks"
      },
      description: {
        type: "string",
        title: "Tutorial Description",
        minLength: 20,
        maxLength: 500,
        default: "Learn how to use React Hooks to manage state and side effects in functional components. This comprehensive tutorial covers useState, useEffect, and custom hooks with practical examples."
      },
      difficulty: {
        type: "string",
        title: "Difficulty Level",
        enum: ["beginner", "intermediate", "advanced", "expert"],
        default: "beginner"
      },
      estimatedTime: {
        type: "integer",
        title: "Estimated Time (minutes)",
        minimum: 5,
        maximum: 480,
        default: 30
      },
      category: {
        type: "string",
        title: "Category",
        minLength: 2,
        maxLength: 50,
        default: "React Development"
      },
      prerequisites: {
        type: "array",
        title: "Prerequisites",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "Prerequisite Title",
              minLength: 2,
              maxLength: 100
            },
            description: {
              type: "string",
              title: "Description",
              maxLength: 200
            },
            required: {
              type: "boolean",
              title: "Required",
              default: true
            }
          },
          required: ["title"]
        },
        default: [
          {
            title: "Basic JavaScript knowledge",
            description: "Understanding of ES6+ features, functions, and objects",
            required: true
          },
          {
            title: "React fundamentals",
            description: "Basic understanding of React components and JSX",
            required: true
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      steps: {
        type: "array",
        title: "Tutorial Steps",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "Step Title",
              minLength: 5,
              maxLength: 150
            },
            content: {
              type: "string",
              title: "Step Content",
              minLength: 20
            },
            code: {
              type: "string",
              title: "Code Example",
              default: ""
            },
            language: {
              type: "string",
              title: "Code Language",
              enum: ["javascript", "typescript", "jsx", "tsx", "html", "css", "bash", "json", "yaml"],
              default: "javascript"
            },
            estimatedTime: {
              type: "integer",
              title: "Step Time (minutes)",
              minimum: 1,
              maximum: 60,
              default: 5
            },
            tips: {
              type: "array",
              title: "Tips & Notes",
              items: {
                type: "string",
                minLength: 5,
                maxLength: 200
              },
              default: [],
              maxItems: 5
            }
          },
          required: ["title", "content"]
        },
        default: [
          {
            title: "Setting up the project",
            content: "First, let's create a new React project and set up our development environment.",
            code: "npx create-react-app my-hooks-tutorial\ncd my-hooks-tutorial\nnpm start",
            language: "bash",
            estimatedTime: 5,
            tips: ["Make sure you have Node.js installed", "Use the latest version of Create React App"]
          },
          {
            title: "Understanding useState",
            content: "The useState hook allows you to add state to functional components.",
            code: "import React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>Count: {count}</p>\n      <button onClick={() => setCount(count + 1)}>\n        Increment\n      </button>\n    </div>\n  );\n}",
            language: "jsx",
            estimatedTime: 10,
            tips: ["useState returns an array with the current state and a setter function", "Always use the setter function to update state"]
          }
        ],
        minItems: 1,
        maxItems: 20
      },
      tools: {
        type: "array",
        title: "Required Tools",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "Tool Name",
              minLength: 2,
              maxLength: 50
            },
            version: {
              type: "string",
              title: "Version",
              maxLength: 20
            },
            url: {
              type: "string",
              title: "Download URL",
              format: "uri"
            },
            description: {
              type: "string",
              title: "Description",
              maxLength: 200
            }
          },
          required: ["name"]
        },
        default: [
          {
            name: "Node.js",
            version: "18+",
            url: "https://nodejs.org/",
            description: "JavaScript runtime for running React development tools"
          },
          {
            name: "VS Code",
            version: "Latest",
            url: "https://code.visualstudio.com/",
            description: "Recommended code editor with React extensions"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      tags: {
        type: "array",
        title: "Tags",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["react", "hooks", "javascript", "frontend"],
        maxItems: 10,
        minItems: 0
      },
      author: {
        type: "object",
        title: "Author Information",
        properties: {
          name: {
            type: "string",
            title: "Author Name",
            minLength: 2,
            maxLength: 100,
            default: "John Developer"
          },
          bio: {
            type: "string",
            title: "Author Bio",
            maxLength: 300,
            default: "Senior React Developer with 5+ years of experience building modern web applications."
          },
          avatar: {
            type: "string",
            title: "Avatar URL",
            format: "uri",
            default: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
          },
          social: {
            type: "object",
            title: "Social Links",
            properties: {
              twitter: {
                type: "string",
                title: "Twitter",
                default: ""
              },
              github: {
                type: "string",
                title: "GitHub",
                default: ""
              },
              linkedin: {
                type: "string",
                title: "LinkedIn",
                default: ""
              }
            }
          }
        },
        required: ["name"]
      },
      lastUpdated: {
        type: "string",
        title: "Last Updated",
        format: "date",
        default: new Date().toISOString().split('T')[0]
      },
      isPublished: {
        type: "boolean",
        title: "Published",
        default: false
      },
      isFeatured: {
        type: "boolean",
        title: "Featured Tutorial",
        default: false
      },
      completionRate: {
        type: "number",
        title: "Average Completion Rate (%)",
        minimum: 0,
        maximum: 100,
        default: 0
      },
      rating: {
        type: "object",
        title: "Tutorial Rating",
        properties: {
          average: {
            type: "number",
            title: "Average Rating",
            minimum: 0,
            maximum: 5,
            default: 0
          },
          count: {
            type: "integer",
            title: "Rating Count",
            minimum: 0,
            default: 0
          }
        }
      },
      seo: {
        type: "object",
        title: "SEO Settings",
        properties: {
          metaTitle: {
            type: "string",
            title: "Meta Title",
            maxLength: 60,
            default: "Getting Started with React Hooks - Complete Tutorial"
          },
          metaDescription: {
            type: "string",
            title: "Meta Description",
            maxLength: 160,
            default: "Learn React Hooks from scratch with this comprehensive tutorial. Master useState, useEffect, and custom hooks with practical examples and best practices."
          },
          keywords: {
            type: "array",
            title: "SEO Keywords",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["react hooks", "useState", "useEffect", "react tutorial", "javascript"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "difficulty", "estimatedTime", "category", "steps", "author"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "difficulty", "estimatedTime", "category",
      "prerequisites", "steps", "tools", "tags", "author", "lastUpdated",
      "isPublished", "isFeatured", "completionRate", "rating", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "Enter tutorial title...",
        help: "A clear, descriptive title for your tutorial"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., getting-started-react-hooks",
        help: "URL-friendly identifier (lowercase letters, numbers, and hyphens only)"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "Describe what students will learn in this tutorial...",
        help: "A compelling description that explains the tutorial's value"
      }
    },
    
    difficulty: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "beginner", label: "🟢 Beginner" },
          { value: "intermediate", label: "🟡 Intermediate" },
          { value: "advanced", label: "🟠 Advanced" },
          { value: "expert", label: "🔴 Expert" }
        ]
      }
    },
    
    estimatedTime: {
      "ui:widget": "updown",
      "ui:options": {
        help: "Total estimated time to complete the tutorial"
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "e.g., React Development, JavaScript, CSS",
        help: "Category to organize your tutorials"
      }
    },
    
    prerequisites: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Prerequisite"
      },
      items: {
        "ui:order": ["title", "description", "required"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Prerequisite title..."
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Describe what knowledge is needed..."
          }
        },
        
        required: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "Is this prerequisite required or optional?"
          }
        }
      }
    },
    
    steps: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Step"
      },
      items: {
        "ui:order": ["title", "content", "code", "language", "estimatedTime", "tips"],
        
        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Step title..."
          }
        },
        
        content: {
          "ui:widget": "markdown",
          "ui:options": {
            placeholder: "Explain this step in detail...",
            height: 300,
            minHeight: 200
          }
        },
        
        code: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 8,
            placeholder: "Add code example...",
            help: "Optional code example for this step"
          }
        },
        
        language: {
          "ui:widget": "select"
        },
        
        estimatedTime: {
          "ui:widget": "updown",
          "ui:options": {
            help: "Time needed for this step"
          }
        },
        
        tips: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "Add Tip"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "Enter a helpful tip..."
            }
          }
        }
      }
    },
    
    tools: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tool"
      },
      items: {
        "ui:order": ["name", "version", "url", "description"],
        
        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Tool name..."
          }
        },
        
        version: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Version requirement..."
          }
        },
        
        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Download URL..."
          }
        },
        
        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "Tool description..."
          }
        }
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "Add Tag"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Enter tag..."
        }
      }
    },
    
    author: {
      "ui:order": ["name", "bio", "avatar", "social"],
      
      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Author name..."
        }
      },
      
      bio: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "Author bio..."
        }
      },
      
      avatar: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Avatar image URL..."
        }
      },
      
      social: {
        "ui:order": ["twitter", "github", "linkedin"],
        
        twitter: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "@username"
          }
        },
        
        github: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "github.com/username"
          }
        },
        
        linkedin: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "linkedin.com/in/username"
          }
        }
      }
    },
    
    lastUpdated: {
      "ui:widget": "date",
      "ui:options": {
        help: "Date when this tutorial was last updated"
      }
    },
    
    isPublished: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Whether this tutorial is visible to users"
      }
    },
    
    isFeatured: {
      "ui:widget": "checkbox",
      "ui:options": {
        help: "Feature this tutorial on the homepage"
      }
    },
    
    completionRate: {
      "ui:widget": "updown",
      "ui:options": {
        step: 0.1,
        help: "Percentage of users who complete this tutorial"
      }
    },
    
    rating: {
      "ui:order": ["average", "count"],
      
      average: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "Average user rating (0-5 stars)"
        }
      },
      
      count: {
        "ui:widget": "updown",
        "ui:options": {
          help: "Number of ratings received"
        }
      }
    },
    
    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],
      
      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO title for this tutorial...",
          help: "Title that appears in search engine results"
        }
      },
      
      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO description for this tutorial...",
          help: "Description that appears in search engine results"
        }
      },
      
      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "Add Keyword"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Enter SEO keyword..."
          }
        }
      }
    }
  }
};

export default tutorialConfig;
