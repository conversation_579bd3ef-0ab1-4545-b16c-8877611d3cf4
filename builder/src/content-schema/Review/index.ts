import { BlockFormConfig } from '../types';

const reviewConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "评测标题",
        minLength: 5,
        maxLength: 200,
        default: "iPhone 15 Pro 深度评测：AI摄影与钛金属设计的完美结合"
      },
      slug: {
        type: "string",
        title: "评测标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "iphone-15-pro-comprehensive-review"
      },
      description: {
        type: "string",
        title: "评测摘要",
        minLength: 50,
        maxLength: 1000,
        default: "经过两周的深度使用，我们对iPhone 15 Pro进行了全面评测。从全新的钛金属设计到强大的A17 Pro芯片，再到革命性的AI摄影功能，这款手机在多个方面都有显著提升。本评测将详细分析其优缺点，帮助你做出购买决策。"
      },
      reviewedItem: {
        type: "object",
        title: "评测对象",
        properties: {
          name: {
            type: "string",
            title: "产品名称",
            minLength: 2,
            maxLength: 150,
            default: "Apple iPhone 15 Pro"
          },
          brand: {
            type: "string",
            title: "品牌",
            maxLength: 100,
            default: "Apple"
          },
          model: {
            type: "string",
            title: "型号",
            maxLength: 100,
            default: "iPhone 15 Pro"
          },
          category: {
            type: "string",
            title: "产品类别",
            enum: ["smartphone", "laptop", "tablet", "smartwatch", "headphones", "camera", "gaming", "software", "service", "other"],
            default: "smartphone"
          },
          price: {
            type: "object",
            title: "价格信息",
            properties: {
              amount: {
                type: "number",
                title: "价格",
                minimum: 0,
                default: 7999
              },
              currency: {
                type: "string",
                title: "货币",
                enum: ["CNY", "USD", "EUR"],
                default: "CNY"
              },
              priceRange: {
                type: "string",
                title: "价格区间",
                enum: ["budget", "mid-range", "premium", "luxury"],
                default: "premium"
              }
            }
          },
          specifications: {
            type: "array",
            title: "技术规格",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  title: "规格名称",
                  maxLength: 50
                },
                value: {
                  type: "string",
                  title: "规格值",
                  maxLength: 200
                },
                category: {
                  type: "string",
                  title: "规格类别",
                  enum: ["display", "performance", "camera", "battery", "storage", "connectivity", "design", "other"],
                  default: "other"
                }
              },
              required: ["name", "value", "category"]
            },
            default: [
              {
                name: "屏幕尺寸",
                value: "6.1英寸 Super Retina XDR OLED",
                category: "display"
              },
              {
                name: "处理器",
                value: "A17 Pro 芯片",
                category: "performance"
              },
              {
                name: "摄像头",
                value: "48MP 主摄 + 12MP 超广角 + 12MP 长焦",
                category: "camera"
              },
              {
                name: "存储",
                value: "128GB/256GB/512GB/1TB",
                category: "storage"
              }
            ],
            maxItems: 20,
            minItems: 0
          },
          images: {
            type: "array",
            title: "产品图片",
            items: {
              type: "object",
              properties: {
                url: {
                  type: "string",
                  title: "图片URL",
                  format: "uri"
                },
                caption: {
                  type: "string",
                  title: "图片说明",
                  maxLength: 200
                },
                alt: {
                  type: "string",
                  title: "替代文本",
                  maxLength: 100
                }
              },
              required: ["url", "caption"]
            },
            default: [
              {
                url: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop",
                caption: "iPhone 15 Pro 钛金属外观",
                alt: "iPhone 15 Pro 产品图片"
              }
            ],
            maxItems: 10,
            minItems: 0
          }
        },
        required: ["name", "brand", "category"]
      },
      overallRating: {
        type: "object",
        title: "总体评分",
        properties: {
          score: {
            type: "number",
            title: "总分",
            minimum: 0,
            maximum: 10,
            default: 8.5
          },
          maxScore: {
            type: "number",
            title: "满分",
            minimum: 1,
            maximum: 10,
            default: 10
          },
          recommendation: {
            type: "string",
            title: "推荐度",
            enum: ["highly-recommended", "recommended", "conditional", "not-recommended"],
            default: "recommended"
          }
        },
        required: ["score", "maxScore", "recommendation"]
      },
      detailedRatings: {
        type: "array",
        title: "详细评分",
        items: {
          type: "object",
          properties: {
            aspect: {
              type: "string",
              title: "评分维度",
              maxLength: 50
            },
            score: {
              type: "number",
              title: "得分",
              minimum: 0,
              maximum: 10
            },
            maxScore: {
              type: "number",
              title: "满分",
              minimum: 1,
              maximum: 10,
              default: 10
            },
            weight: {
              type: "number",
              title: "权重",
              minimum: 0,
              maximum: 1,
              default: 0.2
            },
            comment: {
              type: "string",
              title: "评分说明",
              maxLength: 500
            }
          },
          required: ["aspect", "score", "comment"]
        },
        default: [
          {
            aspect: "设计与工艺",
            score: 9.0,
            maxScore: 10,
            weight: 0.2,
            comment: "钛金属材质手感出色，边框设计更加圆润，整体质感显著提升"
          },
          {
            aspect: "性能表现",
            score: 9.5,
            maxScore: 10,
            weight: 0.25,
            comment: "A17 Pro芯片性能强劲，游戏和多任务处理都非常流畅"
          },
          {
            aspect: "摄影能力",
            score: 8.5,
            maxScore: 10,
            weight: 0.25,
            comment: "AI摄影功能实用，夜景模式有改进，但变焦能力仍有提升空间"
          },
          {
            aspect: "续航表现",
            score: 7.5,
            maxScore: 10,
            weight: 0.15,
            comment: "日常使用一天无压力，但重度使用仍需要充电"
          },
          {
            aspect: "性价比",
            score: 7.0,
            maxScore: 10,
            weight: 0.15,
            comment: "价格较高，但考虑到功能和品质，性价比尚可接受"
          }
        ],
        maxItems: 10,
        minItems: 1
      },
      prosAndCons: {
        type: "object",
        title: "优缺点分析",
        properties: {
          pros: {
            type: "array",
            title: "优点",
            items: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  title: "优点标题",
                  maxLength: 100
                },
                description: {
                  type: "string",
                  title: "详细说明",
                  maxLength: 500
                },
                importance: {
                  type: "string",
                  title: "重要程度",
                  enum: ["low", "medium", "high", "critical"],
                  default: "medium"
                }
              },
              required: ["title", "description", "importance"]
            },
            default: [
              {
                title: "钛金属设计质感出色",
                description: "全新的钛金属材质不仅减轻了重量，还提供了更好的手感和耐用性",
                importance: "high"
              },
              {
                title: "A17 Pro芯片性能强劲",
                description: "3nm工艺的A17 Pro芯片在性能和能效方面都有显著提升",
                importance: "critical"
              },
              {
                title: "AI摄影功能实用",
                description: "智能场景识别和计算摄影功能让拍照变得更加简单",
                importance: "high"
              }
            ],
            maxItems: 10,
            minItems: 0
          },
          cons: {
            type: "array",
            title: "缺点",
            items: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  title: "缺点标题",
                  maxLength: 100
                },
                description: {
                  type: "string",
                  title: "详细说明",
                  maxLength: 500
                },
                severity: {
                  type: "string",
                  title: "严重程度",
                  enum: ["minor", "moderate", "major", "critical"],
                  default: "moderate"
                }
              },
              required: ["title", "description", "severity"]
            },
            default: [
              {
                title: "价格偏高",
                description: "起售价7999元，对于普通消费者来说价格门槛较高",
                severity: "major"
              },
              {
                title: "续航仍有提升空间",
                description: "虽然比前代有改进，但重度使用仍需要一天多次充电",
                severity: "moderate"
              }
            ],
            maxItems: 10,
            minItems: 0
          }
        }
      },
      targetAudience: {
        type: "object",
        title: "适用人群",
        properties: {
          primary: {
            type: "array",
            title: "主要用户",
            items: {
              type: "string",
              maxLength: 100
            },
            default: ["苹果生态用户", "摄影爱好者", "商务人士"],
            maxItems: 10,
            minItems: 0
          },
          notRecommended: {
            type: "array",
            title: "不推荐用户",
            items: {
              type: "string",
              maxLength: 100
            },
            default: ["预算有限用户", "安卓深度用户"],
            maxItems: 10,
            minItems: 0
          },
          useCase: {
            type: "array",
            title: "使用场景",
            items: {
              type: "string",
              maxLength: 100
            },
            default: ["日常通讯", "移动办公", "摄影创作", "游戏娱乐"],
            maxItems: 15,
            minItems: 0
          }
        }
      },
      comparison: {
        type: "object",
        title: "对比分析",
        properties: {
          competitors: {
            type: "array",
            title: "竞品对比",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  title: "竞品名称",
                  maxLength: 100
                },
                brand: {
                  type: "string",
                  title: "品牌",
                  maxLength: 50
                },
                price: {
                  type: "number",
                  title: "价格",
                  minimum: 0
                },
                advantages: {
                  type: "array",
                  title: "相对优势",
                  items: {
                    type: "string",
                    maxLength: 200
                  },
                  maxItems: 5
                },
                disadvantages: {
                  type: "array",
                  title: "相对劣势",
                  items: {
                    type: "string",
                    maxLength: 200
                  },
                  maxItems: 5
                }
              },
              required: ["name", "brand", "price"]
            },
            default: [
              {
                name: "Samsung Galaxy S24 Ultra",
                brand: "Samsung",
                price: 8999,
                advantages: ["S Pen支持", "更大屏幕", "更强变焦"],
                disadvantages: ["Android生态", "价格更高"]
              }
            ],
            maxItems: 5,
            minItems: 0
          },
          alternatives: {
            type: "array",
            title: "替代方案",
            items: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  title: "产品名称",
                  maxLength: 100
                },
                reason: {
                  type: "string",
                  title: "推荐理由",
                  maxLength: 300
                },
                priceAdvantage: {
                  type: "boolean",
                  title: "价格优势",
                  default: false
                }
              },
              required: ["name", "reason"]
            },
            default: [
              {
                name: "iPhone 14 Pro",
                reason: "如果预算有限，iPhone 14 Pro仍然是很好的选择，性能差距不大但价格更实惠",
                priceAdvantage: true
              }
            ],
            maxItems: 5,
            minItems: 0
          }
        }
      },
      testingMethod: {
        type: "object",
        title: "测试方法",
        properties: {
          duration: {
            type: "string",
            title: "测试周期",
            maxLength: 50,
            default: "2周深度使用"
          },
          environment: {
            type: "string",
            title: "测试环境",
            maxLength: 200,
            default: "日常使用环境，包括办公、娱乐、拍照等多种场景"
          },
          methodology: {
            type: "array",
            title: "测试方法",
            items: {
              type: "string",
              maxLength: 200
            },
            default: ["性能跑分测试", "续航测试", "摄影样张对比", "日常使用体验"],
            maxItems: 10,
            minItems: 0
          }
        }
      },
      conclusion: {
        type: "object",
        title: "评测结论",
        properties: {
          summary: {
            type: "string",
            title: "总结",
            maxLength: 1000,
            default: "iPhone 15 Pro是一款优秀的旗舰手机，钛金属设计和A17 Pro芯片都是亮点。虽然价格较高，但对于追求品质和性能的用户来说，仍然值得推荐。"
          },
          verdict: {
            type: "string",
            title: "最终判断",
            enum: ["excellent", "very-good", "good", "average", "poor"],
            default: "very-good"
          },
          buyRecommendation: {
            type: "string",
            title: "购买建议",
            enum: ["buy-now", "wait-for-discount", "consider-alternatives", "avoid"],
            default: "buy-now"
          }
        }
      },
      reviewer: {
        type: "object",
        title: "评测者信息",
        properties: {
          name: {
            type: "string",
            title: "评测者姓名",
            maxLength: 50,
            default: "张三"
          },
          title: {
            type: "string",
            title: "职位头衔",
            maxLength: 100,
            default: "科技评测编辑"
          },
          bio: {
            type: "string",
            title: "个人简介",
            maxLength: 300,
            default: "资深科技产品评测师，专注于消费电子产品评测5年，对手机、笔记本等数码产品有深入了解。"
          },
          avatar: {
            type: "string",
            title: "头像",
            format: "uri",
            default: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
          },
          social: {
            type: "object",
            title: "社交媒体",
            properties: {
              website: {
                type: "string",
                title: "个人网站",
                format: "uri"
              },
              twitter: {
                type: "string",
                title: "Twitter",
                format: "uri"
              },
              weibo: {
                type: "string",
                title: "微博",
                format: "uri"
              }
            }
          }
        },
        required: ["name", "title"]
      },
      category: {
        type: "string",
        title: "评测分类",
        enum: ["smartphone", "laptop", "tablet", "smartwatch", "headphones", "camera", "gaming", "software", "service", "other"],
        default: "smartphone"
      },
      tags: {
        type: "array",
        title: "标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["iPhone", "苹果", "手机评测", "旗舰手机"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "iPhone 15 Pro 深度评测：AI摄影与钛金属设计完美结合 | 科技评测"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "iPhone 15 Pro 全面评测，详细分析钛金属设计、A17 Pro芯片性能、AI摄影功能。包含优缺点对比、购买建议，帮你做出明智选择。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["iPhone 15 Pro评测", "苹果手机", "钛金属", "A17 Pro"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "reviewedItem", "overallRating", "detailedRatings", "prosAndCons"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "reviewedItem", "overallRating", "detailedRatings",
      "prosAndCons", "targetAudience", "comparison", "testingMethod", "conclusion",
      "reviewer", "category", "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入评测标题...",
        help: "吸引读者的评测标题，突出产品特色"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：iphone-15-pro-review",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "简要概述评测内容和结论...",
        help: "评测摘要，有助于SEO和读者快速了解内容"
      }
    },

    reviewedItem: {
      "ui:order": ["name", "brand", "model", "category", "price", "specifications", "images"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "产品全名..."
        }
      },

      brand: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "品牌名称..."
        }
      },

      model: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "产品型号..."
        }
      },

      category: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "smartphone", label: "📱 智能手机" },
            { value: "laptop", label: "💻 笔记本电脑" },
            { value: "tablet", label: "📱 平板电脑" },
            { value: "smartwatch", label: "⌚ 智能手表" },
            { value: "headphones", label: "🎧 耳机" },
            { value: "camera", label: "📷 相机" },
            { value: "gaming", label: "🎮 游戏设备" },
            { value: "software", label: "💿 软件" },
            { value: "service", label: "🔧 服务" },
            { value: "other", label: "📦 其他" }
          ]
        }
      },

      price: {
        "ui:order": ["amount", "currency", "priceRange"],

        amount: {
          "ui:widget": "updown",
          "ui:options": {
            help: "产品价格"
          }
        },

        currency: {
          "ui:widget": "select"
        },

        priceRange: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "budget", label: "💰 经济型" },
              { value: "mid-range", label: "💎 中端" },
              { value: "premium", label: "👑 高端" },
              { value: "luxury", label: "💍 奢华" }
            ]
          }
        }
      },

      specifications: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加规格"
        },
        items: {
          "ui:order": ["name", "value", "category"],

          name: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "规格名称..."
            }
          },

          value: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "规格值..."
            }
          },

          category: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "display", label: "显示屏" },
                { value: "performance", label: "性能" },
                { value: "camera", label: "摄像头" },
                { value: "battery", label: "电池" },
                { value: "storage", label: "存储" },
                { value: "connectivity", label: "连接" },
                { value: "design", label: "设计" },
                { value: "other", label: "其他" }
              ]
            }
          }
        }
      },

      images: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加图片"
        },
        items: {
          "ui:order": ["url", "caption", "alt"],

          url: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "图片URL..."
            }
          },

          caption: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "图片说明..."
            }
          },

          alt: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "替代文本..."
            }
          }
        }
      }
    },

    overallRating: {
      "ui:order": ["score", "maxScore", "recommendation"],

      score: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "总体评分（0-10分）"
        }
      },

      maxScore: {
        "ui:widget": "updown",
        "ui:options": {
          help: "满分值"
        }
      },

      recommendation: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "highly-recommended", label: "🌟 强烈推荐" },
            { value: "recommended", label: "👍 推荐" },
            { value: "conditional", label: "🤔 有条件推荐" },
            { value: "not-recommended", label: "👎 不推荐" }
          ]
        }
      }
    },

    detailedRatings: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加评分维度"
      },
      items: {
        "ui:order": ["aspect", "score", "maxScore", "weight", "comment"],

        aspect: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "评分维度..."
          }
        },

        score: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.1,
            help: "该维度得分"
          }
        },

        maxScore: {
          "ui:widget": "updown",
          "ui:options": {
            help: "该维度满分"
          }
        },

        weight: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.05,
            help: "权重（0-1）"
          }
        },

        comment: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "评分说明..."
          }
        }
      }
    },

    prosAndCons: {
      "ui:order": ["pros", "cons"],

      pros: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加优点"
        },
        items: {
          "ui:order": ["title", "description", "importance"],

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "优点标题..."
            }
          },

          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 3,
              placeholder: "详细说明..."
            }
          },

          importance: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "low", label: "🟢 低" },
                { value: "medium", label: "🟡 中" },
                { value: "high", label: "🟠 高" },
                { value: "critical", label: "🔴 关键" }
              ]
            }
          }
        }
      },

      cons: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加缺点"
        },
        items: {
          "ui:order": ["title", "description", "severity"],

          title: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "缺点标题..."
            }
          },

          description: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 3,
              placeholder: "详细说明..."
            }
          },

          severity: {
            "ui:widget": "select",
            "ui:options": {
              enumOptions: [
                { value: "minor", label: "🟢 轻微" },
                { value: "moderate", label: "🟡 中等" },
                { value: "major", label: "🟠 严重" },
                { value: "critical", label: "🔴 致命" }
              ]
            }
          }
        }
      }
    },

    targetAudience: {
      "ui:order": ["primary", "notRecommended", "useCase"],

      primary: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加目标用户"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "目标用户群体..."
          }
        }
      },

      notRecommended: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加不推荐用户"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "不推荐的用户群体..."
          }
        }
      },

      useCase: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加使用场景"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "使用场景..."
          }
        }
      }
    },

    comparison: {
      "ui:order": ["competitors", "alternatives"],

      competitors: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加竞品"
        },
        items: {
          "ui:order": ["name", "brand", "price", "advantages", "disadvantages"],

          name: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "竞品名称..."
            }
          },

          brand: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "品牌..."
            }
          },

          price: {
            "ui:widget": "updown",
            "ui:options": {
              help: "竞品价格"
            }
          },

          advantages: {
            "ui:options": {
              orderable: true,
              addable: true,
              removable: true,
              addButtonText: "添加优势"
            },
            items: {
              "ui:widget": "text",
              "ui:options": {
                placeholder: "相对优势..."
              }
            }
          },

          disadvantages: {
            "ui:options": {
              orderable: true,
              addable: true,
              removable: true,
              addButtonText: "添加劣势"
            },
            items: {
              "ui:widget": "text",
              "ui:options": {
                placeholder: "相对劣势..."
              }
            }
          }
        }
      },

      alternatives: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加替代方案"
        },
        items: {
          "ui:order": ["name", "reason", "priceAdvantage"],

          name: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "替代产品名称..."
            }
          },

          reason: {
            "ui:widget": "textarea",
            "ui:options": {
              rows: 2,
              placeholder: "推荐理由..."
            }
          },

          priceAdvantage: {
            "ui:widget": "checkbox",
            "ui:options": {
              help: "是否有价格优势"
            }
          }
        }
      }
    },

    testingMethod: {
      "ui:order": ["duration", "environment", "methodology"],

      duration: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "测试周期..."
        }
      },

      environment: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 2,
          placeholder: "测试环境描述..."
        }
      },

      methodology: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加测试方法"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "测试方法..."
          }
        }
      }
    },

    conclusion: {
      "ui:order": ["summary", "verdict", "buyRecommendation"],

      summary: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 5,
          placeholder: "评测总结..."
        }
      },

      verdict: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "excellent", label: "🌟 优秀" },
            { value: "very-good", label: "👍 很好" },
            { value: "good", label: "😊 良好" },
            { value: "average", label: "😐 一般" },
            { value: "poor", label: "👎 较差" }
          ]
        }
      },

      buyRecommendation: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "buy-now", label: "💰 立即购买" },
            { value: "wait-for-discount", label: "⏰ 等待降价" },
            { value: "consider-alternatives", label: "🤔 考虑其他" },
            { value: "avoid", label: "❌ 避免购买" }
          ]
        }
      }
    },

    reviewer: {
      "ui:order": ["name", "title", "bio", "avatar", "social"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "评测者姓名..."
        }
      },

      title: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "职位头衔..."
        }
      },

      bio: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "个人简介..."
        }
      },

      avatar: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "头像图片URL..."
        }
      },

      social: {
        "ui:order": ["website", "twitter", "weibo"],

        website: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "个人网站..."
          }
        },

        twitter: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Twitter链接..."
          }
        },

        weibo: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "微博链接..."
          }
        }
      }
    },

    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "smartphone", label: "📱 智能手机" },
          { value: "laptop", label: "💻 笔记本电脑" },
          { value: "tablet", label: "📱 平板电脑" },
          { value: "smartwatch", label: "⌚ 智能手表" },
          { value: "headphones", label: "🎧 耳机" },
          { value: "camera", label: "📷 相机" },
          { value: "gaming", label: "🎮 游戏设备" },
          { value: "software", label: "💿 软件" },
          { value: "service", label: "🔧 服务" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "评测发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default reviewConfig;
