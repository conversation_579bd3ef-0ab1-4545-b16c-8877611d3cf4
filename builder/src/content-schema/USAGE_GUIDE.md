# Content Model Usage Guide

## Quick Start

### 1. Choose Your Content Type

Select the appropriate content model based on your needs:

| Content Type | Use When | Best For |
|--------------|----------|----------|
| **Blog** | Writing articles, news, or posts | Marketing, thought leadership, news |
| **Documentation** | Creating technical guides | API docs, user manuals, help articles |
| **Tutorial** | Building step-by-step learning | Training, education, how-to guides |
| **FAQ** | Answering common questions | Customer support, knowledge base |
| **Product** | Showcasing products/services | E-commerce, catalogs, portfolios |
| **Announcement** | Important notifications | System updates, news, alerts |
| **Case Study** | Success stories and analysis | Sales, marketing, testimonials |
| **Whitepaper** | Research and thought leadership | Industry reports, research papers |
| **Changelog** | Product updates and releases | Software releases, feature updates |

### 2. Access the Editor

Navigate to `/lab/content-models` to see all available editors, or go directly to:
- `/lab/blog-editor` - Blog posts
- `/lab/documentation-editor` - Technical documentation
- `/lab/tutorial-editor` - Step-by-step tutorials
- `/lab/faq-editor` - FAQ entries
- `/lab/product-editor` - Product information
- `/lab/announcement-editor` - Announcements
- `/lab/case-study-editor` - Case studies
- `/lab/whitepaper-editor` - Whitepapers
- `/lab/changelog-editor` - Change logs

### 3. Fill Required Fields

Each content model has required fields marked with an asterisk (*). Common required fields include:
- **Title**: Clear, descriptive title
- **Content**: Main content body
- **Category**: Content categorization
- **Author/Publisher**: Attribution information

### 4. Optimize for SEO

All models include SEO fields:
- **Meta Title**: 50-60 characters, include primary keyword
- **Meta Description**: 150-160 characters, compelling summary
- **Keywords**: 5-10 relevant keywords

### 5. Preview and Publish

Use the preview panel to review your content before setting `isPublished` to true.

## Detailed Field Guides

### Common Fields

#### Title Field
- **Purpose**: Main heading for your content
- **Length**: 5-150 characters (varies by model)
- **Tips**: 
  - Be descriptive and specific
  - Include primary keywords
  - Avoid clickbait
  - Test different variations

#### Slug Field
- **Purpose**: URL-friendly identifier
- **Format**: lowercase letters, numbers, hyphens only
- **Example**: "getting-started-with-react" for title "Getting Started with React"
- **Tips**:
  - Keep it short but descriptive
  - Use hyphens to separate words
  - Avoid special characters
  - Make it memorable

#### Content Field
- **Purpose**: Main content body
- **Format**: Markdown supported
- **Features**:
  - Rich text editing
  - Code syntax highlighting
  - Image embedding
  - Link insertion
- **Tips**:
  - Use headings to structure content
  - Include relevant images
  - Break up long paragraphs
  - Add code examples where appropriate

#### Tags Field
- **Purpose**: Content categorization and discovery
- **Format**: Array of strings
- **Limits**: Usually 0-10 tags
- **Tips**:
  - Use relevant, specific tags
  - Be consistent with tag naming
  - Avoid overly generic tags
  - Consider your audience's search terms

#### Category Field
- **Purpose**: High-level content grouping
- **Format**: Single string value
- **Examples**: "Technology", "Marketing", "Product Updates"
- **Tips**:
  - Use broad, consistent categories
  - Limit the number of categories
  - Make categories mutually exclusive
  - Consider your content organization

### Model-Specific Fields

#### Blog Model

**Featured Image**:
- Include URL and alt text
- Use high-quality images (1200x630px recommended)
- Ensure alt text is descriptive for accessibility

**Author Information**:
- Include name, bio, and avatar
- Add social media links
- Keep bio concise (100-200 characters)

**Reading Time**:
- Estimate based on 200-250 words per minute
- Round to nearest minute
- Consider complexity of content

#### Product Model

**Pricing Information**:
- Include currency, base price, billing cycle
- Add discount pricing if applicable
- Specify free trial details

**Features Array**:
- List key product features
- Include descriptions for each feature
- Use benefit-focused language
- Limit to 5-10 main features

**Specifications**:
- Include technical details
- Use consistent naming conventions
- Provide specific values
- Group related specifications

#### Tutorial Model

**Prerequisites**:
- List required knowledge/skills
- Mark as required or optional
- Include links to prerequisite learning
- Be specific about skill levels

**Steps Array**:
- Break down into logical steps
- Provide time estimates for each step
- Include code examples where relevant
- Add tips and warnings

**Tools Required**:
- List all necessary software/tools
- Include version requirements
- Provide download links
- Explain why each tool is needed

#### Case Study Model

**Client Information**:
- Get permission before including details
- Use company size categories consistently
- Include industry and location
- Add logo if available

**Challenge Section**:
- Describe the problem clearly
- List specific pain points
- Quantify the impact where possible
- Set context for the solution

**Results Metrics**:
- Use specific, quantifiable data
- Include before/after comparisons
- Calculate percentage improvements
- Verify accuracy of all numbers

#### Whitepaper Model

**Abstract**:
- Summarize key findings in 100-300 words
- Include methodology if relevant
- Highlight main conclusions
- Make it compelling for downloads

**Authors Array**:
- Include credentials and affiliations
- Add professional photos
- Provide contact information
- Include brief bios

**Download Information**:
- Specify file size and format
- Set access requirements
- Track download metrics
- Provide alternative formats if needed

## Content Strategy Tips

### Planning Your Content

1. **Define Your Audience**
   - Who will read this content?
   - What's their knowledge level?
   - What problems are they trying to solve?

2. **Set Clear Objectives**
   - What action do you want readers to take?
   - How does this content support business goals?
   - What metrics will you track?

3. **Research Keywords**
   - What terms does your audience search for?
   - What's the competition like?
   - How can you differentiate your content?

### Writing Best Practices

1. **Structure for Readability**
   - Use clear headings and subheadings
   - Keep paragraphs short (2-3 sentences)
   - Use bullet points and numbered lists
   - Include relevant images and diagrams

2. **Optimize for SEO**
   - Include target keywords naturally
   - Use descriptive headings
   - Add internal and external links
   - Optimize images with alt text

3. **Make It Actionable**
   - Include specific steps or recommendations
   - Provide examples and case studies
   - Add downloadable resources
   - Include clear calls-to-action

### Quality Assurance

1. **Content Review Checklist**
   - [ ] All required fields completed
   - [ ] Content is accurate and up-to-date
   - [ ] Grammar and spelling checked
   - [ ] Links are working
   - [ ] Images have alt text
   - [ ] SEO fields optimized
   - [ ] Preview looks correct

2. **Technical Validation**
   - [ ] Schema validation passes
   - [ ] All URLs are valid
   - [ ] Date formats are correct
   - [ ] Required fields are not empty
   - [ ] Array limits are respected

3. **User Experience**
   - [ ] Content is easy to read
   - [ ] Navigation is clear
   - [ ] Mobile-friendly formatting
   - [ ] Fast loading times
   - [ ] Accessible to all users

## Troubleshooting

### Common Issues

**Validation Errors**:
- Check required fields are filled
- Verify field length limits
- Ensure proper URL formats
- Validate email addresses

**Preview Not Updating**:
- Save your changes first
- Refresh the preview panel
- Check for JavaScript errors
- Clear browser cache

**Content Not Displaying**:
- Verify `isPublished` is set to true
- Check publication date
- Ensure proper permissions
- Review content filters

### Getting Help

1. **Documentation**: Check this guide and model-specific docs
2. **Examples**: Review existing content for reference
3. **Testing**: Use the lab editors to experiment
4. **Support**: Contact the development team for technical issues

## Advanced Usage

### Custom Validation

You can extend validation rules by modifying the schema:

```typescript
// Add custom validation
title: {
  type: "string",
  title: "Title",
  minLength: 5,
  maxLength: 100,
  pattern: "^[A-Z].*" // Must start with capital letter
}
```

### Custom UI Widgets

Extend UI functionality with custom widgets:

```typescript
// Custom widget configuration
customField: {
  "ui:widget": "custom-widget",
  "ui:options": {
    customOption: "value"
  }
}
```

### Integration with CMS

These content models integrate with the broader CMS system:

1. **API Integration**: Content is accessible via REST/GraphQL APIs
2. **Database Storage**: Content is stored in structured database tables
3. **Search Integration**: Content is indexed for full-text search
4. **Caching**: Content is cached for performance
5. **Version Control**: Changes are tracked and versioned

For more advanced usage, consult the CMS developer documentation.
