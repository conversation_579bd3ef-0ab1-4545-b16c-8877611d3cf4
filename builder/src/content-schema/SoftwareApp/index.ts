import { BlockFormConfig } from '../types';

const softwareAppConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "应用名称",
        minLength: 2,
        maxLength: 100,
        default: "TaskMaster Pro - 智能任务管理工具"
      },
      slug: {
        type: "string",
        title: "应用标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "taskmaster-pro-task-management"
      },
      description: {
        type: "string",
        title: "应用描述",
        minLength: 50,
        maxLength: 1000,
        default: "TaskMaster Pro是一款功能强大的智能任务管理工具，采用AI技术帮助用户高效管理日常工作和项目。支持团队协作、智能提醒、数据分析等功能，让工作更有条理，效率倍增。"
      },
      category: {
        type: "string",
        title: "应用分类",
        enum: ["productivity", "business", "education", "entertainment", "social", "utilities", "health", "finance", "travel", "lifestyle", "games", "developer-tools", "other"],
        default: "productivity"
      },
      platform: {
        type: "array",
        title: "支持平台",
        items: {
          type: "string",
          enum: ["web", "ios", "android", "windows", "macos", "linux"]
        },
        default: ["web", "ios", "android"],
        minItems: 1,
        maxItems: 6
      },
      version: {
        type: "string",
        title: "当前版本",
        maxLength: 20,
        default: "2.1.0"
      },
      developer: {
        type: "object",
        title: "开发者信息",
        properties: {
          name: {
            type: "string",
            title: "开发者名称",
            maxLength: 100,
            default: "TechFlow Studio"
          },
          website: {
            type: "string",
            title: "开发者网站",
            format: "uri"
          },
          email: {
            type: "string",
            title: "联系邮箱",
            format: "email"
          },
          support: {
            type: "string",
            title: "技术支持",
            format: "uri"
          }
        },
        required: ["name"]
      },
      pricing: {
        type: "object",
        title: "价格信息",
        properties: {
          model: {
            type: "string",
            title: "定价模式",
            enum: ["free", "freemium", "paid", "subscription", "one-time"],
            default: "freemium"
          },
          price: {
            type: "number",
            title: "价格",
            minimum: 0,
            default: 0
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          billing: {
            type: "string",
            title: "计费周期",
            enum: ["monthly", "yearly", "one-time", "usage-based"],
            default: "monthly"
          },
          freeTrial: {
            type: "boolean",
            title: "提供免费试用",
            default: true
          },
          trialDays: {
            type: "integer",
            title: "试用天数",
            minimum: 1,
            maximum: 365,
            default: 14
          }
        }
      },
      features: {
        type: "array",
        title: "核心功能",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "功能名称",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "功能描述",
              maxLength: 300
            },
            category: {
              type: "string",
              title: "功能分类",
              enum: ["core", "advanced", "premium", "enterprise"],
              default: "core"
            },
            available: {
              type: "boolean",
              title: "是否可用",
              default: true
            }
          },
          required: ["name", "description", "category"]
        },
        default: [
          {
            name: "智能任务分配",
            description: "基于AI算法自动分配任务优先级和执行顺序",
            category: "core",
            available: true
          },
          {
            name: "团队协作",
            description: "支持多人实时协作，共享项目进度和文件",
            category: "core",
            available: true
          },
          {
            name: "数据分析报表",
            description: "生成详细的工作效率和项目进度分析报告",
            category: "advanced",
            available: true
          },
          {
            name: "API集成",
            description: "提供开放API，支持与第三方工具集成",
            category: "premium",
            available: true
          }
        ],
        maxItems: 20,
        minItems: 1
      },
      systemRequirements: {
        type: "object",
        title: "系统要求",
        properties: {
          web: {
            type: "object",
            title: "Web版本要求",
            properties: {
              browsers: {
                type: "array",
                title: "支持浏览器",
                items: {
                  type: "string"
                },
                default: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+"]
              },
              internetRequired: {
                type: "boolean",
                title: "需要网络连接",
                default: true
              }
            }
          },
          mobile: {
            type: "object",
            title: "移动端要求",
            properties: {
              ios: {
                type: "string",
                title: "iOS最低版本",
                default: "iOS 13.0+"
              },
              android: {
                type: "string",
                title: "Android最低版本",
                default: "Android 8.0+"
              },
              storage: {
                type: "string",
                title: "存储空间",
                default: "100MB"
              }
            }
          },
          desktop: {
            type: "object",
            title: "桌面端要求",
            properties: {
              windows: {
                type: "string",
                title: "Windows版本",
                default: "Windows 10+"
              },
              macos: {
                type: "string",
                title: "macOS版本",
                default: "macOS 10.15+"
              },
              ram: {
                type: "string",
                title: "内存要求",
                default: "4GB RAM"
              },
              storage: {
                type: "string",
                title: "存储空间",
                default: "500MB"
              }
            }
          }
        }
      },
      screenshots: {
        type: "array",
        title: "应用截图",
        items: {
          type: "object",
          properties: {
            url: {
              type: "string",
              title: "图片URL",
              format: "uri"
            },
            caption: {
              type: "string",
              title: "图片说明",
              maxLength: 200
            },
            alt: {
              type: "string",
              title: "替代文本",
              maxLength: 100
            },
            platform: {
              type: "string",
              title: "平台",
              enum: ["web", "ios", "android", "windows", "macos", "linux"],
              default: "web"
            }
          },
          required: ["url", "caption", "platform"]
        },
        default: [
          {
            url: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop",
            caption: "TaskMaster Pro 主界面 - 任务管理面板",
            alt: "应用主界面截图",
            platform: "web"
          },
          {
            url: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop",
            caption: "团队协作功能 - 项目看板视图",
            alt: "团队协作界面截图",
            platform: "web"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      downloadLinks: {
        type: "array",
        title: "下载链接",
        items: {
          type: "object",
          properties: {
            platform: {
              type: "string",
              title: "平台",
              enum: ["web", "ios", "android", "windows", "macos", "linux"]
            },
            url: {
              type: "string",
              title: "下载链接",
              format: "uri"
            },
            store: {
              type: "string",
              title: "应用商店",
              enum: ["app-store", "google-play", "microsoft-store", "official-website", "github", "other"],
              default: "official-website"
            },
            version: {
              type: "string",
              title: "版本号",
              maxLength: 20
            }
          },
          required: ["platform", "url", "store"]
        },
        default: [
          {
            platform: "web",
            url: "https://taskmaster-pro.com",
            store: "official-website",
            version: "2.1.0"
          },
          {
            platform: "ios",
            url: "https://apps.apple.com/app/taskmaster-pro",
            store: "app-store",
            version: "2.1.0"
          },
          {
            platform: "android",
            url: "https://play.google.com/store/apps/details?id=com.taskmaster.pro",
            store: "google-play",
            version: "2.1.0"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      rating: {
        type: "object",
        title: "用户评分",
        properties: {
          average: {
            type: "number",
            title: "平均评分",
            minimum: 0,
            maximum: 5,
            default: 4.6
          },
          count: {
            type: "integer",
            title: "评分数量",
            minimum: 0,
            default: 1250
          },
          distribution: {
            type: "object",
            title: "评分分布",
            properties: {
              five: { type: "integer", title: "5星", minimum: 0, default: 850 },
              four: { type: "integer", title: "4星", minimum: 0, default: 280 },
              three: { type: "integer", title: "3星", minimum: 0, default: 80 },
              two: { type: "integer", title: "2星", minimum: 0, default: 25 },
              one: { type: "integer", title: "1星", minimum: 0, default: 15 }
            }
          }
        }
      },
      tags: {
        type: "array",
        title: "应用标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["任务管理", "效率工具", "团队协作", "AI智能"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "TaskMaster Pro - 智能任务管理工具 | 提升工作效率的AI助手"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "TaskMaster Pro是功能强大的智能任务管理工具，支持AI智能分配、团队协作、数据分析。免费试用14天，让工作更高效有序。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["任务管理软件", "效率工具", "团队协作", "AI智能助手"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "category", "platform", "version", "developer", "features"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "category", "platform", "version", "developer",
      "pricing", "features", "systemRequirements", "screenshots", "downloadLinks",
      "rating", "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入应用名称...",
        help: "应用的完整名称，包含主要功能特色"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：taskmaster-pro-app",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 4,
        placeholder: "详细描述应用的功能和特色...",
        help: "应用的详细介绍，突出核心功能和优势"
      }
    },
    
    category: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "productivity", label: "📈 效率工具" },
          { value: "business", label: "💼 商务办公" },
          { value: "education", label: "📚 教育学习" },
          { value: "entertainment", label: "🎬 娱乐休闲" },
          { value: "social", label: "👥 社交网络" },
          { value: "utilities", label: "🔧 实用工具" },
          { value: "health", label: "🏥 健康医疗" },
          { value: "finance", label: "💰 金融理财" },
          { value: "travel", label: "✈️ 旅行出行" },
          { value: "lifestyle", label: "🌟 生活方式" },
          { value: "games", label: "🎮 游戏娱乐" },
          { value: "developer-tools", label: "⚙️ 开发工具" },
          { value: "other", label: "📦 其他" }
        ]
      }
    },
    
    platform: {
      "ui:widget": "checkboxes",
      "ui:options": {
        enumOptions: [
          { value: "web", label: "🌐 Web网页版" },
          { value: "ios", label: "📱 iOS" },
          { value: "android", label: "🤖 Android" },
          { value: "windows", label: "🪟 Windows" },
          { value: "macos", label: "🍎 macOS" },
          { value: "linux", label: "🐧 Linux" }
        ]
      }
    },

    version: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：2.1.0",
        help: "当前应用版本号"
      }
    },

    developer: {
      "ui:order": ["name", "website", "email", "support"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "开发者或公司名称..."
        }
      },

      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "开发者官网..."
        }
      },

      email: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "联系邮箱..."
        }
      },

      support: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "技术支持页面..."
        }
      }
    },

    pricing: {
      "ui:order": ["model", "price", "currency", "billing", "freeTrial", "trialDays"],

      model: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "free", label: "💚 完全免费" },
            { value: "freemium", label: "🆓 免费增值" },
            { value: "paid", label: "💰 付费应用" },
            { value: "subscription", label: "🔄 订阅制" },
            { value: "one-time", label: "💳 一次性付费" }
          ]
        }
      },

      price: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "应用价格"
        }
      },

      currency: {
        "ui:widget": "select"
      },

      billing: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "monthly", label: "📅 按月" },
            { value: "yearly", label: "📆 按年" },
            { value: "one-time", label: "💳 一次性" },
            { value: "usage-based", label: "📊 按使用量" }
          ]
        }
      },

      freeTrial: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否提供免费试用期"
        }
      },

      trialDays: {
        "ui:widget": "updown",
        "ui:options": {
          help: "免费试用天数"
        }
      }
    },

    features: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加功能"
      },
      items: {
        "ui:order": ["name", "description", "category", "available"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "功能名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "详细描述这个功能..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "core", label: "🔵 核心功能" },
              { value: "advanced", label: "🟡 高级功能" },
              { value: "premium", label: "🟠 付费功能" },
              { value: "enterprise", label: "🔴 企业功能" }
            ]
          }
        },

        available: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "该功能是否已可用"
          }
        }
      }
    },

    systemRequirements: {
      "ui:order": ["web", "mobile", "desktop"],

      web: {
        "ui:order": ["browsers", "internetRequired"],

        browsers: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加浏览器"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "浏览器版本要求..."
            }
          }
        },

        internetRequired: {
          "ui:widget": "checkbox"
        }
      },

      mobile: {
        "ui:order": ["ios", "android", "storage"],

        ios: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "iOS版本要求..."
          }
        },

        android: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Android版本要求..."
          }
        },

        storage: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "存储空间要求..."
          }
        }
      },

      desktop: {
        "ui:order": ["windows", "macos", "ram", "storage"],

        windows: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "Windows版本要求..."
          }
        },

        macos: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "macOS版本要求..."
          }
        },

        ram: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "内存要求..."
          }
        },

        storage: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "存储空间要求..."
          }
        }
      }
    },

    screenshots: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加截图"
      },
      items: {
        "ui:order": ["url", "caption", "alt", "platform"],

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "截图URL..."
          }
        },

        caption: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "截图说明..."
          }
        },

        alt: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "替代文本..."
          }
        },

        platform: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "web", label: "🌐 Web" },
              { value: "ios", label: "📱 iOS" },
              { value: "android", label: "🤖 Android" },
              { value: "windows", label: "🪟 Windows" },
              { value: "macos", label: "🍎 macOS" },
              { value: "linux", label: "🐧 Linux" }
            ]
          }
        }
      }
    },

    downloadLinks: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加下载链接"
      },
      items: {
        "ui:order": ["platform", "url", "store", "version"],

        platform: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "web", label: "🌐 Web" },
              { value: "ios", label: "📱 iOS" },
              { value: "android", label: "🤖 Android" },
              { value: "windows", label: "🪟 Windows" },
              { value: "macos", label: "🍎 macOS" },
              { value: "linux", label: "🐧 Linux" }
            ]
          }
        },

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "下载链接..."
          }
        },

        store: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "app-store", label: "🍎 App Store" },
              { value: "google-play", label: "🤖 Google Play" },
              { value: "microsoft-store", label: "🪟 Microsoft Store" },
              { value: "official-website", label: "🌐 官方网站" },
              { value: "github", label: "🐙 GitHub" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        version: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "版本号..."
          }
        }
      }
    },

    rating: {
      "ui:order": ["average", "count", "distribution"],

      average: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "平均评分（0-5分）"
        }
      },

      count: {
        "ui:widget": "updown",
        "ui:options": {
          help: "总评分数量"
        }
      },

      distribution: {
        "ui:order": ["five", "four", "three", "two", "one"],

        five: {
          "ui:widget": "updown",
          "ui:options": {
            help: "5星评分数量"
          }
        },

        four: {
          "ui:widget": "updown",
          "ui:options": {
            help: "4星评分数量"
          }
        },

        three: {
          "ui:widget": "updown",
          "ui:options": {
            help: "3星评分数量"
          }
        },

        two: {
          "ui:widget": "updown",
          "ui:options": {
            help: "2星评分数量"
          }
        },

        one: {
          "ui:widget": "updown",
          "ui:options": {
            help: "1星评分数量"
          }
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "应用发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default softwareAppConfig;
