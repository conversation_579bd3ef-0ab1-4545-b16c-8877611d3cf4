import { BlockFormConfig } from '../types';

const videoConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "视频标题",
        minLength: 5,
        maxLength: 150,
        default: "JavaScript高级技巧：深入理解闭包和作用域"
      },
      slug: {
        type: "string",
        title: "视频标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "javascript-closures-and-scope"
      },
      description: {
        type: "string",
        title: "视频描述",
        minLength: 50,
        maxLength: 1000,
        default: "深入探讨JavaScript中的闭包和作用域概念，通过实际案例和代码演示，帮助你彻底理解这些重要的编程概念。本视频适合有一定JavaScript基础的开发者，将帮助你提升代码质量和编程思维。"
      },
      videoUrl: {
        type: "string",
        title: "视频文件URL",
        format: "uri",
        default: "https://example.com/videos/javascript-closures-scope.mp4"
      },
      thumbnailUrl: {
        type: "string",
        title: "视频缩略图",
        format: "uri",
        default: "https://example.com/thumbnails/javascript-closures-scope.jpg"
      },
      duration: {
        type: "integer",
        title: "视频时长(秒)",
        minimum: 10,
        maximum: 18000,
        default: 1800
      },
      type: {
        type: "string",
        title: "视频类型",
        enum: ["educational", "tutorial", "promotional", "documentary", "entertainment", "webinar", "demo"],
        default: "tutorial"
      },
      category: {
        type: "string",
        title: "视频分类",
        minLength: 2,
        maxLength: 50,
        default: "编程教程"
      },
      tags: {
        type: "array",
        title: "视频标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["JavaScript", "闭包", "作用域", "前端开发"],
        maxItems: 15,
        minItems: 1
      },
      language: {
        type: "string",
        title: "视频语言",
        enum: ["zh-CN", "en", "ja", "ko", "es", "fr", "de"],
        default: "zh-CN"
      },
      subtitles: {
        type: "array",
        title: "字幕文件",
        items: {
          type: "object",
          properties: {
            language: {
              type: "string",
              title: "字幕语言",
              enum: ["zh-CN", "en", "ja", "ko", "es", "fr", "de"],
              default: "zh-CN"
            },
            url: {
              type: "string",
              title: "字幕文件URL",
              format: "uri"
            },
            label: {
              type: "string",
              title: "字幕标签",
              maxLength: 50,
              default: "中文"
            }
          },
          required: ["language", "url", "label"]
        },
        default: [
          {
            language: "zh-CN",
            url: "https://example.com/subtitles/javascript-closures-zh.vtt",
            label: "中文"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      chapters: {
        type: "array",
        title: "视频章节",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "章节标题",
              minLength: 2,
              maxLength: 100
            },
            startTime: {
              type: "integer",
              title: "开始时间(秒)",
              minimum: 0
            },
            endTime: {
              type: "integer",
              title: "结束时间(秒)",
              minimum: 0
            },
            description: {
              type: "string",
              title: "章节描述",
              maxLength: 300
            }
          },
          required: ["title", "startTime"]
        },
        default: [
          {
            title: "什么是闭包",
            startTime: 0,
            endTime: 300,
            description: "介绍闭包的基本概念和定义"
          },
          {
            title: "作用域链详解",
            startTime: 300,
            endTime: 900,
            description: "深入理解JavaScript的作用域链机制"
          },
          {
            title: "实际应用案例",
            startTime: 900,
            endTime: 1500,
            description: "通过实际代码案例演示闭包的应用"
          },
          {
            title: "常见问题和陷阱",
            startTime: 1500,
            endTime: 1800,
            description: "分析闭包使用中的常见问题和解决方案"
          }
        ],
        maxItems: 50,
        minItems: 0
      },
      transcript: {
        type: "string",
        title: "视频文稿",
        default: "# JavaScript闭包和作用域详解\n\n## 开场白\n大家好，欢迎来到今天的JavaScript高级技巧课程。今天我们要深入探讨两个非常重要的概念：闭包和作用域。\n\n## 什么是闭包？\n闭包是JavaScript中一个强大而又容易被误解的概念。简单来说，闭包就是函数和其词法环境的组合...\n\n## 作用域链\n在理解闭包之前，我们需要先理解作用域链的概念...\n\n## 实际应用\n让我们通过一些实际的代码例子来看看闭包是如何工作的...\n\n## 总结\n今天我们学习了闭包和作用域的核心概念，希望这些知识能帮助你写出更好的JavaScript代码。"
      },
      quality: {
        type: "object",
        title: "视频质量",
        properties: {
          resolution: {
            type: "string",
            title: "分辨率",
            enum: ["720p", "1080p", "1440p", "4K"],
            default: "1080p"
          },
          bitrate: {
            type: "integer",
            title: "比特率(kbps)",
            minimum: 500,
            maximum: 50000,
            default: 5000
          },
          frameRate: {
            type: "integer",
            title: "帧率(fps)",
            enum: [24, 30, 60],
            default: 30
          },
          audioQuality: {
            type: "string",
            title: "音频质量",
            enum: ["standard", "high", "lossless"],
            default: "high"
          }
        }
      },
      creator: {
        type: "object",
        title: "创作者信息",
        properties: {
          name: {
            type: "string",
            title: "创作者姓名",
            minLength: 2,
            maxLength: 50,
            default: "李老师"
          },
          avatar: {
            type: "string",
            title: "头像URL",
            format: "uri",
            default: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
          },
          bio: {
            type: "string",
            title: "创作者简介",
            maxLength: 300,
            default: "资深前端工程师，拥有10年Web开发经验，专注于JavaScript技术栈和现代前端框架。"
          },
          social: {
            type: "object",
            title: "社交媒体",
            properties: {
              website: {
                type: "string",
                title: "个人网站",
                format: "uri"
              },
              youtube: {
                type: "string",
                title: "YouTube",
                format: "uri"
              },
              bilibili: {
                type: "string",
                title: "哔哩哔哩",
                format: "uri"
              },
              github: {
                type: "string",
                title: "GitHub",
                format: "uri"
              }
            }
          }
        },
        required: ["name"]
      },
      series: {
        type: "object",
        title: "视频系列",
        properties: {
          seriesId: {
            type: "string",
            title: "系列ID",
            default: "javascript-advanced-series"
          },
          seriesTitle: {
            type: "string",
            title: "系列标题",
            maxLength: 100,
            default: "JavaScript高级技巧系列"
          },
          episodeNumber: {
            type: "integer",
            title: "集数",
            minimum: 1,
            default: 3
          },
          totalEpisodes: {
            type: "integer",
            title: "总集数",
            minimum: 1,
            default: 10
          }
        }
      },
      accessibility: {
        type: "object",
        title: "无障碍功能",
        properties: {
          hasClosedCaptions: {
            type: "boolean",
            title: "包含字幕",
            default: true
          },
          hasAudioDescription: {
            type: "boolean",
            title: "包含音频描述",
            default: false
          },
          hasSignLanguage: {
            type: "boolean",
            title: "包含手语翻译",
            default: false
          },
          transcriptAvailable: {
            type: "boolean",
            title: "提供文稿",
            default: true
          }
        }
      },
      engagement: {
        type: "object",
        title: "互动数据",
        properties: {
          viewCount: {
            type: "integer",
            title: "观看次数",
            minimum: 0,
            default: 0
          },
          likeCount: {
            type: "integer",
            title: "点赞数",
            minimum: 0,
            default: 0
          },
          dislikeCount: {
            type: "integer",
            title: "踩数",
            minimum: 0,
            default: 0
          },
          commentCount: {
            type: "integer",
            title: "评论数",
            minimum: 0,
            default: 0
          },
          shareCount: {
            type: "integer",
            title: "分享次数",
            minimum: 0,
            default: 0
          },
          averageWatchTime: {
            type: "integer",
            title: "平均观看时长(秒)",
            minimum: 0,
            default: 0
          },
          completionRate: {
            type: "number",
            title: "完成率(%)",
            minimum: 0,
            maximum: 100,
            default: 0
          }
        }
      },
      monetization: {
        type: "object",
        title: "变现设置",
        properties: {
          isPaid: {
            type: "boolean",
            title: "付费视频",
            default: false
          },
          price: {
            type: "number",
            title: "价格",
            minimum: 0,
            default: 0
          },
          currency: {
            type: "string",
            title: "货币",
            enum: ["CNY", "USD", "EUR"],
            default: "CNY"
          },
          allowAds: {
            type: "boolean",
            title: "允许广告",
            default: true
          },
          sponsorship: {
            type: "object",
            title: "赞助信息",
            properties: {
              hasSponsor: {
                type: "boolean",
                title: "有赞助商",
                default: false
              },
              sponsorName: {
                type: "string",
                title: "赞助商名称",
                maxLength: 100
              },
              sponsorUrl: {
                type: "string",
                title: "赞助商链接",
                format: "uri"
              }
            }
          }
        }
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "processing", "published", "private", "unlisted", "archived"],
        default: "draft"
      },
      visibility: {
        type: "string",
        title: "可见性",
        enum: ["public", "unlisted", "private", "members-only"],
        default: "public"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      expiryDate: {
        type: "string",
        title: "过期日期",
        format: "date-time"
      },
      rating: {
        type: "object",
        title: "视频评分",
        properties: {
          average: {
            type: "number",
            title: "平均评分",
            minimum: 0,
            maximum: 5,
            default: 0
          },
          count: {
            type: "integer",
            title: "评分人数",
            minimum: 0,
            default: 0
          }
        }
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "JavaScript闭包和作用域详解 - 高级编程技巧视频教程"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "深入学习JavaScript闭包和作用域概念，通过实际案例和代码演示，提升你的编程技能。适合有基础的前端开发者观看。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["JavaScript视频", "闭包教程", "作用域详解", "前端开发"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "videoUrl", "duration", "type", "category", "language", "creator"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "videoUrl", "thumbnailUrl", "duration",
      "type", "category", "tags", "language", "subtitles", "chapters", "transcript",
      "quality", "creator", "series", "accessibility", "engagement", "monetization",
      "status", "visibility", "publishDate", "lastUpdated", "expiryDate", "rating", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入视频标题...",
        help: "吸引观众的视频标题，突出核心内容"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：javascript-closures-scope",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "详细描述视频内容、适用人群和学习收获...",
        help: "详细的视频介绍，有助于SEO和用户理解"
      }
    },
    
    videoUrl: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "https://example.com/video.mp4",
        help: "视频文件的完整URL地址"
      }
    },
    
    thumbnailUrl: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "https://example.com/thumbnail.jpg",
        help: "视频缩略图URL，建议尺寸16:9"
      }
    },
    
    duration: {
      "ui:widget": "updown",
      "ui:options": {
        help: "视频总时长（秒）"
      }
    },
    
    type: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "educational", label: "📚 教育内容" },
          { value: "tutorial", label: "🎯 教程指南" },
          { value: "promotional", label: "📢 宣传推广" },
          { value: "documentary", label: "🎬 纪录片" },
          { value: "entertainment", label: "🎭 娱乐内容" },
          { value: "webinar", label: "💼 网络研讨会" },
          { value: "demo", label: "🔧 产品演示" }
        ]
      }
    },
    
    category: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：编程教程、产品介绍、企业培训",
        help: "视频所属的主要分类"
      }
    },
    
    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },
    
    language: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "zh-CN", label: "中文" },
          { value: "en", label: "English" },
          { value: "ja", label: "日本語" },
          { value: "ko", label: "한국어" },
          { value: "es", label: "Español" },
          { value: "fr", label: "Français" },
          { value: "de", label: "Deutsch" }
        ]
      }
    },

    subtitles: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加字幕"
      },
      items: {
        "ui:order": ["language", "url", "label"],

        language: {
          "ui:widget": "select"
        },

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "字幕文件URL..."
          }
        },

        label: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "字幕显示标签..."
          }
        }
      }
    },

    chapters: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加章节"
      },
      items: {
        "ui:order": ["title", "startTime", "endTime", "description"],

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "章节标题..."
          }
        },

        startTime: {
          "ui:widget": "updown",
          "ui:options": {
            help: "开始时间（秒）"
          }
        },

        endTime: {
          "ui:widget": "updown",
          "ui:options": {
            help: "结束时间（秒）"
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "章节描述..."
          }
        }
      }
    },

    transcript: {
      "ui:widget": "markdown",
      "ui:options": {
        placeholder: "输入视频文稿内容...",
        fullWidth: true,
        defaultMode: "wysiwyg",
        showModeToggle: true,
        height: 400,
        minHeight: 200
      }
    },

    quality: {
      "ui:order": ["resolution", "bitrate", "frameRate", "audioQuality"],

      resolution: {
        "ui:widget": "select"
      },

      bitrate: {
        "ui:widget": "updown",
        "ui:options": {
          help: "视频比特率（kbps）"
        }
      },

      frameRate: {
        "ui:widget": "select"
      },

      audioQuality: {
        "ui:widget": "select",
        "ui:options": {
          enumOptions: [
            { value: "standard", label: "标准音质" },
            { value: "high", label: "高音质" },
            { value: "lossless", label: "无损音质" }
          ]
        }
      }
    },

    creator: {
      "ui:order": ["name", "avatar", "bio", "social"],

      name: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "创作者姓名..."
        }
      },

      avatar: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "头像图片URL..."
        }
      },

      bio: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "创作者简介..."
        }
      },

      social: {
        "ui:order": ["website", "youtube", "bilibili", "github"],

        website: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "个人网站URL..."
          }
        },

        youtube: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "YouTube频道URL..."
          }
        },

        bilibili: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "哔哩哔哩主页URL..."
          }
        },

        github: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "GitHub个人资料URL..."
          }
        }
      }
    },

    series: {
      "ui:order": ["seriesId", "seriesTitle", "episodeNumber", "totalEpisodes"],

      seriesId: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "系列标识符..."
        }
      },

      seriesTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "系列标题..."
        }
      },

      episodeNumber: {
        "ui:widget": "updown",
        "ui:options": {
          help: "当前集数"
        }
      },

      totalEpisodes: {
        "ui:widget": "updown",
        "ui:options": {
          help: "系列总集数"
        }
      }
    },

    accessibility: {
      "ui:order": ["hasClosedCaptions", "hasAudioDescription", "hasSignLanguage", "transcriptAvailable"],

      hasClosedCaptions: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否包含字幕"
        }
      },

      hasAudioDescription: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否包含音频描述"
        }
      },

      hasSignLanguage: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否包含手语翻译"
        }
      },

      transcriptAvailable: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否提供文稿"
        }
      }
    },

    engagement: {
      "ui:order": ["viewCount", "likeCount", "dislikeCount", "commentCount", "shareCount", "averageWatchTime", "completionRate"],

      viewCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "视频观看次数"
        }
      },

      likeCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "点赞数量"
        }
      },

      dislikeCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "踩的数量"
        }
      },

      commentCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "评论数量"
        }
      },

      shareCount: {
        "ui:widget": "updown",
        "ui:options": {
          help: "分享次数"
        }
      },

      averageWatchTime: {
        "ui:widget": "updown",
        "ui:options": {
          help: "平均观看时长（秒）"
        }
      },

      completionRate: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "完成率百分比"
        }
      }
    },

    monetization: {
      "ui:order": ["isPaid", "price", "currency", "allowAds", "sponsorship"],

      isPaid: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否为付费视频"
        }
      },

      price: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.01,
          help: "视频价格"
        }
      },

      currency: {
        "ui:widget": "select"
      },

      allowAds: {
        "ui:widget": "checkbox",
        "ui:options": {
          help: "是否允许显示广告"
        }
      },

      sponsorship: {
        "ui:order": ["hasSponsor", "sponsorName", "sponsorUrl"],

        hasSponsor: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否有赞助商"
          }
        },

        sponsorName: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "赞助商名称..."
          }
        },

        sponsorUrl: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "赞助商网站URL..."
          }
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "processing", label: "⚙️ 处理中" },
          { value: "published", label: "✅ 已发布" },
          { value: "private", label: "🔒 私有" },
          { value: "unlisted", label: "🔗 不公开列出" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    visibility: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "public", label: "🌍 公开" },
          { value: "unlisted", label: "🔗 不公开列出" },
          { value: "private", label: "🔒 私有" },
          { value: "members-only", label: "👥 仅会员" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "视频发布日期和时间"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期和时间"
      }
    },

    expiryDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "视频过期日期（可选）"
      }
    },

    rating: {
      "ui:order": ["average", "count"],

      average: {
        "ui:widget": "updown",
        "ui:options": {
          step: 0.1,
          help: "平均评分（0-5分）"
        }
      },

      count: {
        "ui:widget": "updown",
        "ui:options": {
          help: "评分人数"
        }
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default videoConfig;
