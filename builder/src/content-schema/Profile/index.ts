import { BlockFormConfig } from '../types';

const profileConfig: BlockFormConfig = {
  schema: {
    type: "object",
    properties: {
      title: {
        type: "string",
        title: "页面标题",
        minLength: 2,
        maxLength: 100,
        default: "张三 - 全栈开发工程师的个人主页"
      },
      slug: {
        type: "string",
        title: "页面标识",
        pattern: "^[a-z0-9]+(?:-[a-z0-9]+)*$",
        minLength: 3,
        maxLength: 100,
        default: "zhang-san-fullstack-developer"
      },
      description: {
        type: "string",
        title: "个人简介",
        minLength: 50,
        maxLength: 1000,
        default: "我是张三，一名拥有5年经验的全栈开发工程师，专注于React、Node.js和云原生技术。热爱开源，致力于用技术创造价值，帮助团队构建高质量的产品。"
      },
      personalInfo: {
        type: "object",
        title: "基本信息",
        properties: {
          fullName: {
            type: "string",
            title: "姓名",
            maxLength: 50,
            default: "张三"
          },
          displayName: {
            type: "string",
            title: "显示名称",
            maxLength: 50,
            default: "<PERSON> San"
          },
          title: {
            type: "string",
            title: "职位头衔",
            maxLength: 100,
            default: "全栈开发工程师"
          },
          avatar: {
            type: "string",
            title: "头像",
            format: "uri",
            default: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face"
          },
          location: {
            type: "string",
            title: "所在地",
            maxLength: 100,
            default: "北京，中国"
          },
          email: {
            type: "string",
            title: "邮箱",
            format: "email",
            default: "<EMAIL>"
          },
          phone: {
            type: "string",
            title: "电话",
            maxLength: 20
          },
          website: {
            type: "string",
            title: "个人网站",
            format: "uri"
          },
          birthDate: {
            type: "string",
            title: "出生日期",
            format: "date"
          },
          nationality: {
            type: "string",
            title: "国籍",
            maxLength: 50,
            default: "中国"
          }
        },
        required: ["fullName", "title", "avatar"]
      },
      bio: {
        type: "string",
        title: "详细介绍",
        minLength: 100,
        maxLength: 2000,
        default: "我是一名充满激情的全栈开发工程师，拥有5年的软件开发经验。专精于现代Web技术栈，包括React、Vue.js、Node.js、Python等。在我的职业生涯中，我参与了多个大型项目的开发，从初创公司的MVP产品到企业级应用系统。我相信技术的力量能够改变世界，致力于编写高质量、可维护的代码，并热衷于学习新技术和分享知识。"
      },
      skills: {
        type: "array",
        title: "技能专长",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "技能名称",
              maxLength: 50
            },
            category: {
              type: "string",
              title: "技能分类",
              enum: ["programming", "framework", "database", "cloud", "design", "soft-skills", "language", "other"],
              default: "programming"
            },
            level: {
              type: "string",
              title: "熟练程度",
              enum: ["beginner", "intermediate", "advanced", "expert"],
              default: "intermediate"
            },
            years: {
              type: "integer",
              title: "使用年限",
              minimum: 0,
              maximum: 50,
              default: 2
            },
            description: {
              type: "string",
              title: "技能描述",
              maxLength: 200
            }
          },
          required: ["name", "category", "level"]
        },
        default: [
          {
            name: "JavaScript",
            category: "programming",
            level: "expert",
            years: 5,
            description: "精通ES6+语法，熟悉异步编程和函数式编程"
          },
          {
            name: "React",
            category: "framework",
            level: "advanced",
            years: 4,
            description: "熟练使用React Hooks、Context API和状态管理"
          },
          {
            name: "Node.js",
            category: "programming",
            level: "advanced",
            years: 3,
            description: "擅长构建RESTful API和微服务架构"
          },
          {
            name: "团队协作",
            category: "soft-skills",
            level: "advanced",
            years: 5,
            description: "具备良好的沟通能力和团队合作精神"
          }
        ],
        maxItems: 30,
        minItems: 0
      },
      experience: {
        type: "array",
        title: "工作经历",
        items: {
          type: "object",
          properties: {
            company: {
              type: "string",
              title: "公司名称",
              maxLength: 100
            },
            position: {
              type: "string",
              title: "职位",
              maxLength: 100
            },
            startDate: {
              type: "string",
              title: "开始日期",
              format: "date"
            },
            endDate: {
              type: "string",
              title: "结束日期",
              format: "date"
            },
            current: {
              type: "boolean",
              title: "当前工作",
              default: false
            },
            location: {
              type: "string",
              title: "工作地点",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "工作描述",
              maxLength: 1000
            },
            achievements: {
              type: "array",
              title: "主要成就",
              items: {
                type: "string",
                maxLength: 200
              },
              maxItems: 10
            },
            technologies: {
              type: "array",
              title: "使用技术",
              items: {
                type: "string",
                maxLength: 50
              },
              maxItems: 20
            }
          },
          required: ["company", "position", "startDate", "description"]
        },
        default: [
          {
            company: "TechFlow科技有限公司",
            position: "高级全栈开发工程师",
            startDate: "2022-03-01",
            current: true,
            location: "北京",
            description: "负责公司核心产品的前后端开发，参与系统架构设计和技术选型。",
            achievements: [
              "主导开发了新一代SaaS平台，用户量增长300%",
              "优化系统性能，页面加载速度提升50%",
              "建立了完善的CI/CD流程，部署效率提升80%"
            ],
            technologies: ["React", "Node.js", "TypeScript", "AWS", "Docker"]
          },
          {
            company: "创新软件公司",
            position: "前端开发工程师",
            startDate: "2020-06-01",
            endDate: "2022-02-28",
            location: "上海",
            description: "专注于前端开发，负责多个Web应用的开发和维护。",
            achievements: [
              "开发了公司官网和管理后台",
              "引入了现代化的前端工程化工具"
            ],
            technologies: ["Vue.js", "JavaScript", "Webpack", "Sass"]
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      education: {
        type: "array",
        title: "教育背景",
        items: {
          type: "object",
          properties: {
            institution: {
              type: "string",
              title: "学校名称",
              maxLength: 100
            },
            degree: {
              type: "string",
              title: "学位",
              enum: ["high-school", "associate", "bachelor", "master", "phd", "certificate", "other"],
              default: "bachelor"
            },
            major: {
              type: "string",
              title: "专业",
              maxLength: 100
            },
            startDate: {
              type: "string",
              title: "开始日期",
              format: "date"
            },
            endDate: {
              type: "string",
              title: "结束日期",
              format: "date"
            },
            gpa: {
              type: "number",
              title: "GPA",
              minimum: 0,
              maximum: 4.0
            },
            description: {
              type: "string",
              title: "描述",
              maxLength: 500
            },
            achievements: {
              type: "array",
              title: "学术成就",
              items: {
                type: "string",
                maxLength: 200
              },
              maxItems: 10
            }
          },
          required: ["institution", "degree", "major", "startDate"]
        },
        default: [
          {
            institution: "北京理工大学",
            degree: "bachelor",
            major: "计算机科学与技术",
            startDate: "2016-09-01",
            endDate: "2020-06-30",
            gpa: 3.7,
            description: "主修计算机科学与技术，专注于软件工程和算法设计",
            achievements: [
              "获得校级优秀学生奖学金",
              "参与ACM程序设计竞赛获得省级二等奖"
            ]
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      projects: {
        type: "array",
        title: "项目作品",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "项目名称",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "项目描述",
              maxLength: 500
            },
            role: {
              type: "string",
              title: "担任角色",
              maxLength: 50
            },
            startDate: {
              type: "string",
              title: "开始日期",
              format: "date"
            },
            endDate: {
              type: "string",
              title: "结束日期",
              format: "date"
            },
            status: {
              type: "string",
              title: "项目状态",
              enum: ["planning", "in-progress", "completed", "on-hold", "cancelled"],
              default: "completed"
            },
            url: {
              type: "string",
              title: "项目链接",
              format: "uri"
            },
            github: {
              type: "string",
              title: "GitHub链接",
              format: "uri"
            },
            technologies: {
              type: "array",
              title: "使用技术",
              items: {
                type: "string",
                maxLength: 50
              },
              maxItems: 20
            },
            highlights: {
              type: "array",
              title: "项目亮点",
              items: {
                type: "string",
                maxLength: 200
              },
              maxItems: 10
            }
          },
          required: ["name", "description", "role"]
        },
        default: [
          {
            name: "智能任务管理系统",
            description: "基于AI的智能任务管理平台，帮助团队提高工作效率",
            role: "全栈开发工程师",
            startDate: "2023-01-01",
            endDate: "2023-06-30",
            status: "completed",
            url: "https://taskmaster.example.com",
            github: "https://github.com/zhangsan/taskmaster",
            technologies: ["React", "Node.js", "MongoDB", "OpenAI API"],
            highlights: [
              "集成了GPT-4 API实现智能任务分配",
              "支持实时协作和消息推送",
              "用户活跃度提升40%"
            ]
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      social: {
        type: "object",
        title: "社交媒体",
        properties: {
          linkedin: {
            type: "string",
            title: "LinkedIn",
            format: "uri"
          },
          github: {
            type: "string",
            title: "GitHub",
            format: "uri"
          },
          twitter: {
            type: "string",
            title: "Twitter",
            format: "uri"
          },
          weibo: {
            type: "string",
            title: "微博",
            format: "uri"
          },
          wechat: {
            type: "string",
            title: "微信号",
            maxLength: 50
          },
          blog: {
            type: "string",
            title: "个人博客",
            format: "uri"
          },
          youtube: {
            type: "string",
            title: "YouTube",
            format: "uri"
          },
          instagram: {
            type: "string",
            title: "Instagram",
            format: "uri"
          }
        }
      },
      achievements: {
        type: "array",
        title: "成就荣誉",
        items: {
          type: "object",
          properties: {
            title: {
              type: "string",
              title: "成就标题",
              maxLength: 100
            },
            description: {
              type: "string",
              title: "成就描述",
              maxLength: 300
            },
            date: {
              type: "string",
              title: "获得日期",
              format: "date"
            },
            organization: {
              type: "string",
              title: "颁发机构",
              maxLength: 100
            },
            category: {
              type: "string",
              title: "成就类别",
              enum: ["award", "certification", "publication", "speaking", "competition", "other"],
              default: "award"
            },
            url: {
              type: "string",
              title: "相关链接",
              format: "uri"
            }
          },
          required: ["title", "description", "category"]
        },
        default: [
          {
            title: "年度最佳员工",
            description: "因在项目开发中的突出表现获得公司年度最佳员工奖",
            date: "2023-12-01",
            organization: "TechFlow科技有限公司",
            category: "award"
          }
        ],
        maxItems: 20,
        minItems: 0
      },
      languages: {
        type: "array",
        title: "语言能力",
        items: {
          type: "object",
          properties: {
            language: {
              type: "string",
              title: "语言",
              maxLength: 50
            },
            level: {
              type: "string",
              title: "熟练程度",
              enum: ["native", "fluent", "conversational", "basic"],
              default: "conversational"
            },
            description: {
              type: "string",
              title: "详细说明",
              maxLength: 200
            }
          },
          required: ["language", "level"]
        },
        default: [
          {
            language: "中文",
            level: "native",
            description: "母语"
          },
          {
            language: "英语",
            level: "fluent",
            description: "流利的听说读写能力，可进行技术交流"
          }
        ],
        maxItems: 10,
        minItems: 0
      },
      interests: {
        type: "array",
        title: "兴趣爱好",
        items: {
          type: "object",
          properties: {
            name: {
              type: "string",
              title: "兴趣名称",
              maxLength: 50
            },
            description: {
              type: "string",
              title: "描述",
              maxLength: 200
            },
            category: {
              type: "string",
              title: "类别",
              enum: ["technology", "sports", "arts", "music", "reading", "travel", "gaming", "cooking", "other"],
              default: "other"
            }
          },
          required: ["name", "category"]
        },
        default: [
          {
            name: "开源贡献",
            description: "热衷于参与开源项目，贡献代码和文档",
            category: "technology"
          },
          {
            name: "摄影",
            description: "喜欢用镜头记录生活中的美好瞬间",
            category: "arts"
          }
        ],
        maxItems: 15,
        minItems: 0
      },
      tags: {
        type: "array",
        title: "个人标签",
        items: {
          type: "string",
          minLength: 2,
          maxLength: 30
        },
        default: ["全栈开发", "React专家", "Node.js", "开源贡献者"],
        maxItems: 15,
        minItems: 0
      },
      status: {
        type: "string",
        title: "发布状态",
        enum: ["draft", "review", "published", "archived"],
        default: "draft"
      },
      publishDate: {
        type: "string",
        title: "发布日期",
        format: "date-time",
        default: new Date().toISOString()
      },
      lastUpdated: {
        type: "string",
        title: "最后更新",
        format: "date-time",
        default: new Date().toISOString()
      },
      seo: {
        type: "object",
        title: "SEO设置",
        properties: {
          metaTitle: {
            type: "string",
            title: "SEO标题",
            maxLength: 60,
            default: "张三 - 全栈开发工程师 | 个人主页"
          },
          metaDescription: {
            type: "string",
            title: "SEO描述",
            maxLength: 160,
            default: "张三是一名经验丰富的全栈开发工程师，专注于React、Node.js和云原生技术。查看详细简历、项目作品和技能专长。"
          },
          keywords: {
            type: "array",
            title: "关键词",
            items: {
              type: "string",
              minLength: 2,
              maxLength: 50
            },
            default: ["全栈开发工程师", "React开发", "Node.js", "个人简历"],
            maxItems: 10,
            minItems: 0
          }
        }
      }
    },
    required: ["title", "slug", "description", "personalInfo", "bio"]
  },
  uiSchema: {
    "ui:order": [
      "title", "slug", "description", "personalInfo", "bio", "skills", "experience",
      "education", "projects", "social", "achievements", "languages", "interests",
      "tags", "status", "publishDate", "lastUpdated", "seo"
    ],
    
    title: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "输入个人主页标题...",
        help: "个人主页的标题，包含姓名和职业"
      }
    },
    
    slug: {
      "ui:widget": "text",
      "ui:options": {
        placeholder: "例如：zhang-san-developer",
        help: "URL友好的标识符（仅限小写字母、数字和连字符）"
      }
    },
    
    description: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 3,
        placeholder: "简要介绍自己的专业背景和特长...",
        help: "个人简介，用于SEO和社交媒体分享"
      }
    },
    
    personalInfo: {
      "ui:order": ["fullName", "displayName", "title", "avatar", "location", "email", "phone", "website", "birthDate", "nationality"],
      
      fullName: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "真实姓名..."
        }
      },
      
      displayName: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "显示名称或英文名..."
        }
      },
      
      title: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "职位头衔..."
        }
      },
      
      avatar: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "头像图片URL..."
        }
      },
      
      location: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "所在城市..."
        }
      },
      
      email: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "联系邮箱..."
        }
      },
      
      phone: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "联系电话..."
        }
      },
      
      website: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "个人网站..."
        }
      },
      
      birthDate: {
        "ui:widget": "date",
        "ui:options": {
          help: "出生日期（可选）"
        }
      },
      
      nationality: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "国籍..."
        }
      }
    },
    
    bio: {
      "ui:widget": "textarea",
      "ui:options": {
        rows: 6,
        placeholder: "详细介绍你的专业背景、经验和目标...",
        help: "详细的个人介绍，展示你的专业能力和个人特色"
      }
    },

    skills: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加技能"
      },
      items: {
        "ui:order": ["name", "category", "level", "years", "description"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "技能名称..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "programming", label: "💻 编程语言" },
              { value: "framework", label: "🔧 框架工具" },
              { value: "database", label: "🗄️ 数据库" },
              { value: "cloud", label: "☁️ 云服务" },
              { value: "design", label: "🎨 设计" },
              { value: "soft-skills", label: "🤝 软技能" },
              { value: "language", label: "🌍 语言" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        level: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "beginner", label: "🟢 初学者" },
              { value: "intermediate", label: "🟡 中级" },
              { value: "advanced", label: "🟠 高级" },
              { value: "expert", label: "🔴 专家" }
            ]
          }
        },

        years: {
          "ui:widget": "updown",
          "ui:options": {
            help: "使用年限"
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "技能描述..."
          }
        }
      }
    },

    experience: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加工作经历"
      },
      items: {
        "ui:order": ["company", "position", "startDate", "endDate", "current", "location", "description", "achievements", "technologies"],

        company: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "公司名称..."
          }
        },

        position: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "职位名称..."
          }
        },

        startDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "入职日期"
          }
        },

        endDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "离职日期（如果是当前工作可不填）"
          }
        },

        current: {
          "ui:widget": "checkbox",
          "ui:options": {
            help: "是否为当前工作"
          }
        },

        location: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "工作地点..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 4,
            placeholder: "工作职责和内容描述..."
          }
        },

        achievements: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加成就"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "主要成就..."
            }
          }
        },

        technologies: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加技术"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "使用的技术..."
            }
          }
        }
      }
    },

    education: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加教育经历"
      },
      items: {
        "ui:order": ["institution", "degree", "major", "startDate", "endDate", "gpa", "description", "achievements"],

        institution: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "学校名称..."
          }
        },

        degree: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "high-school", label: "🎓 高中" },
              { value: "associate", label: "🎓 专科" },
              { value: "bachelor", label: "🎓 本科" },
              { value: "master", label: "🎓 硕士" },
              { value: "phd", label: "🎓 博士" },
              { value: "certificate", label: "📜 证书" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        major: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "专业名称..."
          }
        },

        startDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "入学日期"
          }
        },

        endDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "毕业日期"
          }
        },

        gpa: {
          "ui:widget": "updown",
          "ui:options": {
            step: 0.1,
            help: "GPA成绩（可选）"
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "教育经历描述..."
          }
        },

        achievements: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加学术成就"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "学术成就..."
            }
          }
        }
      }
    },

    projects: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加项目"
      },
      items: {
        "ui:order": ["name", "description", "role", "startDate", "endDate", "status", "url", "github", "technologies", "highlights"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "项目名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "项目描述..."
          }
        },

        role: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "担任角色..."
          }
        },

        startDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "项目开始日期"
          }
        },

        endDate: {
          "ui:widget": "date",
          "ui:options": {
            help: "项目结束日期"
          }
        },

        status: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "planning", label: "📋 规划中" },
              { value: "in-progress", label: "🚧 进行中" },
              { value: "completed", label: "✅ 已完成" },
              { value: "on-hold", label: "⏸️ 暂停" },
              { value: "cancelled", label: "❌ 已取消" }
            ]
          }
        },

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "项目链接..."
          }
        },

        github: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "GitHub链接..."
          }
        },

        technologies: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加技术"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "使用的技术..."
            }
          }
        },

        highlights: {
          "ui:options": {
            orderable: true,
            addable: true,
            removable: true,
            addButtonText: "添加亮点"
          },
          items: {
            "ui:widget": "text",
            "ui:options": {
              placeholder: "项目亮点..."
            }
          }
        }
      }
    },

    social: {
      "ui:order": ["linkedin", "github", "twitter", "weibo", "wechat", "blog", "youtube", "instagram"],

      linkedin: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "LinkedIn个人资料链接..."
        }
      },

      github: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "GitHub个人主页..."
        }
      },

      twitter: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Twitter个人主页..."
        }
      },

      weibo: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "微博个人主页..."
        }
      },

      wechat: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "微信号..."
        }
      },

      blog: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "个人博客链接..."
        }
      },

      youtube: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "YouTube频道..."
        }
      },

      instagram: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "Instagram个人主页..."
        }
      }
    },

    achievements: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加成就"
      },
      items: {
        "ui:order": ["title", "description", "date", "organization", "category", "url"],

        title: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "成就标题..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 3,
            placeholder: "成就描述..."
          }
        },

        date: {
          "ui:widget": "date",
          "ui:options": {
            help: "获得日期"
          }
        },

        organization: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "颁发机构..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "award", label: "🏆 奖项" },
              { value: "certification", label: "📜 认证" },
              { value: "publication", label: "📚 发表" },
              { value: "speaking", label: "🎤 演讲" },
              { value: "competition", label: "🏁 竞赛" },
              { value: "other", label: "📦 其他" }
            ]
          }
        },

        url: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "相关链接..."
          }
        }
      }
    },

    languages: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加语言"
      },
      items: {
        "ui:order": ["language", "level", "description"],

        language: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "语言名称..."
          }
        },

        level: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "native", label: "🌟 母语" },
              { value: "fluent", label: "💬 流利" },
              { value: "conversational", label: "🗣️ 对话" },
              { value: "basic", label: "📖 基础" }
            ]
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "语言能力描述..."
          }
        }
      }
    },

    interests: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加兴趣"
      },
      items: {
        "ui:order": ["name", "description", "category"],

        name: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "兴趣名称..."
          }
        },

        description: {
          "ui:widget": "textarea",
          "ui:options": {
            rows: 2,
            placeholder: "兴趣描述..."
          }
        },

        category: {
          "ui:widget": "select",
          "ui:options": {
            enumOptions: [
              { value: "technology", label: "💻 技术" },
              { value: "sports", label: "⚽ 运动" },
              { value: "arts", label: "🎨 艺术" },
              { value: "music", label: "🎵 音乐" },
              { value: "reading", label: "📚 阅读" },
              { value: "travel", label: "✈️ 旅行" },
              { value: "gaming", label: "🎮 游戏" },
              { value: "cooking", label: "🍳 烹饪" },
              { value: "other", label: "📦 其他" }
            ]
          }
        }
      }
    },

    tags: {
      "ui:options": {
        orderable: true,
        addable: true,
        removable: true,
        addButtonText: "添加标签"
      },
      items: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "输入标签..."
        }
      }
    },

    status: {
      "ui:widget": "select",
      "ui:options": {
        enumOptions: [
          { value: "draft", label: "📝 草稿" },
          { value: "review", label: "👀 审核中" },
          { value: "published", label: "✅ 已发布" },
          { value: "archived", label: "📦 已归档" }
        ]
      }
    },

    publishDate: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "个人主页发布日期"
      }
    },

    lastUpdated: {
      "ui:widget": "datetime",
      "ui:options": {
        help: "最后更新日期"
      }
    },

    seo: {
      "ui:order": ["metaTitle", "metaDescription", "keywords"],

      metaTitle: {
        "ui:widget": "text",
        "ui:options": {
          placeholder: "SEO标题...",
          help: "搜索引擎显示的标题"
        }
      },

      metaDescription: {
        "ui:widget": "textarea",
        "ui:options": {
          rows: 3,
          placeholder: "SEO描述...",
          help: "搜索引擎显示的描述"
        }
      },

      keywords: {
        "ui:options": {
          orderable: true,
          addable: true,
          removable: true,
          addButtonText: "添加关键词"
        },
        items: {
          "ui:widget": "text",
          "ui:options": {
            placeholder: "输入SEO关键词..."
          }
        }
      }
    }
  }
};

export default profileConfig;
