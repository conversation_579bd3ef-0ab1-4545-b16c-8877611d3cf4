import React, { useState, useEffect } from 'react';
import { FieldTemplateProps } from '@rjsf/utils';
import { cn } from '@/lib/utils';
import { getCustomClassNames, isFullWidthField } from '../utils';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { HelpCircle, Info, AlertCircle } from "lucide-react";

// Help tooltip configuration type definition
interface HelpOptions {
  iconType?: 'help' | 'info' | 'alert';
  position?: 'icon' | 'inline' | 'both';
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left';
  tooltipAlign?: 'start' | 'center' | 'end';
}

// Extended UI Schema options type
interface UIOptions {
  compact?: boolean;
  help?: HelpOptions;
  [key: string]: any;
}

/**
 * Shadcn UI Field Template
 *
 * Responsible for rendering individual form field container, including label, description, error messages, etc.
 */
const FieldTemplate = ({
  id,
  label,
  children,
  errors,
  help,
  description,
  hidden,
  required,
  displayLabel,
  classNames,
  disabled,
  // readonly,
  uiSchema,
}: FieldTemplateProps) => {
  // Get UI options
  const uiOptions = uiSchema?.["ui:options"] as UIOptions | undefined;

  // Get help tooltip related configuration
  const helpOptions: HelpOptions = uiOptions?.help || {};
  const helpPosition = helpOptions.position || "icon";

  // Move useState to component top level, no longer conditionally initialized
  const [showInlineHelp, setShowInlineHelp] = useState(false);

  // Generate accessibility IDs
  const errorId = errors && errors.length > 0 ? `${id}-error` : undefined;
  const helpId = help ? `${id}-help` : undefined;
  const descriptionId = description ? `${id}-description` : undefined;

  // Build aria-describedby attribute
  const ariaDescribedBy = [helpId, descriptionId, errorId].filter(Boolean).join(' ') || undefined;
  
  // Use useEffect to set initial state
  useEffect(() => {
    setShowInlineHelp(helpPosition === "inline");
  }, [helpPosition]);

  if (hidden) {
    return <div className="hidden">{children}</div>;
  }

  // Get custom class names and layout information
  const fieldClassName = getCustomClassNames(uiSchema, classNames);
  const isFullWidth = isFullWidthField(uiSchema);
  
  const isCompact = uiOptions?.compact === true;
  
  // Check if it's a first-level field (by ID check)
  const isFirstLevel = id && id.split('_').length === 2 && id.startsWith('root_');

  const helpIconType = helpOptions.iconType || "help";
  const tooltipSide = helpOptions.tooltipSide || "right";
  const tooltipAlign = helpOptions.tooltipAlign || "start";
  
  // Determine if there's help configuration
  const hasHelpConfig = !!uiOptions?.help;
  // Help text content - only use help or description when help options are explicitly configured
  const helpText = hasHelpConfig ? (help || description) : null;
  
  // Choose help icon based on configuration
  const getHelpIcon = () => {
    switch (helpIconType) {
      case "info":
        return <Info className="h-4 w-4 text-blue-500 dark:text-blue-400 cursor-help opacity-70 hover:opacity-100 transition-all duration-200" />;
      case "alert":
        return <AlertCircle className="h-4 w-4 text-amber-500 dark:text-amber-400 cursor-help opacity-70 hover:opacity-100 transition-all duration-200" />;
      case "help":
      default:
        return <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help opacity-70 hover:opacity-100 transition-all duration-200" />;
    }
  };
  
  // Render help icon tooltip
  const renderHelpIcon = () => {
    // Only show icon when help text exists and display mode is not purely inline
    if (!helpText || helpPosition === "inline") return null;
    
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <button 
              type="button" 
              onClick={() => helpPosition === "both" && setShowInlineHelp(!showInlineHelp)}
              className="focus:outline-none"
              aria-label="Show help information"
            >
              {getHelpIcon()}
            </button>
          </TooltipTrigger>
          <TooltipContent 
            side={tooltipSide} 
            align={tooltipAlign} 
            className="max-w-80 text-sm"
          >
            <p>{helpText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  // Render inline help text
  const renderInlineHelp = () => {
    if (!helpText || (!showInlineHelp && helpPosition !== "inline")) return null;
    
    return (
      <div className="text-xs text-muted-foreground mt-1">
        {helpText}
      </div>
    );
  };

  return (
    <div className={cn(
      "form-field",
      "border-border/50 bg-card/50 text-card-foreground",
      "transition-colors duration-200",
      isCompact ? "mb-2" : "mb-4",
      isFirstLevel ? "first-level-field border-l-2 border-l-primary/60 pl-4" : "",
      fieldClassName,
      isFullWidth ? "col-span-full" : "",
      "w-full min-w-0 overflow-hidden"
    )}>
      {displayLabel && label && (
        <div className={cn(
          "flex items-center mb-1.5",
          isCompact ? "mb-1" : "mb-1.5"
        )}>
          <label
            htmlFor={id}
            className={cn(
              "block text-sm font-medium",
              disabled ? "text-muted-foreground" : "text-foreground",
              "truncate"
            )}
          >
            {label}
            {required && <span className="text-destructive ml-0.5">*</span>}
          </label>
          
          {/* Render help icon */}
          {renderHelpIcon()}
          
          {/* Button to toggle inline help display */}
          {helpText && helpPosition === "both" && (
            <button
              type="button"
              onClick={() => setShowInlineHelp(!showInlineHelp)}
              className="ml-2 text-xs text-muted-foreground hover:text-foreground"
            >
              {showInlineHelp ? "Hide Help" : "Show Help"}
            </button>
          )}
        </div>
      )}
      
      {/* Inline help text */}
      {helpText && (showInlineHelp || helpPosition === "inline") && (
        <div className="text-xs text-muted-foreground mb-2 max-w-full overflow-hidden">
          {helpText}
        </div>
      )}
      
      {/* Field content */}
      <div className="w-full min-w-0 overflow-hidden">
        {children}
      </div>
      
      {/* Error information */}
      {errors && (
        <div className="text-xs text-destructive mt-1 max-w-full overflow-hidden">
          {errors}
        </div>
      )}
    </div>
  );
};

export default FieldTemplate;
