/**
 * 性能优化组件
 * 
 * 为CMS编辑器提供性能优化功能
 */

'use client';

import React, { memo, useMemo, useCallback, useRef } from 'react';
import { debounce } from 'lodash-es';

// 防抖Hook
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  return useMemo(
    () => debounce((...args: Parameters<T>) => callbackRef.current(...args), delay) as T,
    [delay]
  );
}

// 虚拟化长列表组件
interface VirtualizedListProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  className?: string;
}

export const VirtualizedList = memo<VirtualizedListProps>(({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className
}) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );
  
  const visibleItems = items.slice(visibleStart, visibleEnd);
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  return (
    <div
      className={className}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleStart * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) =>
            renderItem(item, visibleStart + index)
          )}
        </div>
      </div>
    </div>
  );
});

VirtualizedList.displayName = 'VirtualizedList';

// 懒加载组件
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  rootMargin?: string;
}

export const LazyComponent = memo<LazyComponentProps>(({
  children,
  fallback = <div>Loading...</div>,
  rootMargin = '50px'
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [rootMargin]);

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  );
});

LazyComponent.displayName = 'LazyComponent';

// 表单字段优化HOC
export function withFieldOptimization<P extends object>(
  Component: React.ComponentType<P>
) {
  const OptimizedComponent = memo((props: P) => {
    return <Component {...props} />;
  });

  OptimizedComponent.displayName = `withFieldOptimization(${Component.displayName || Component.name})`;
  
  return OptimizedComponent;
}

// 预览组件优化HOC
export function withPreviewOptimization<P extends { data: any }>(
  Component: React.ComponentType<P>
) {
  const OptimizedPreview = memo<P>((props) => {
    return <Component {...props} />;
  }, (prevProps, nextProps) => {
    // 深度比较data属性
    return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
  });

  OptimizedPreview.displayName = `withPreviewOptimization(${Component.displayName || Component.name})`;
  
  return OptimizedPreview;
}

// 性能监控Hook
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0);
  const startTime = useRef(performance.now());

  React.useEffect(() => {
    renderCount.current += 1;
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} - Render #${renderCount.current}, Time: ${renderTime.toFixed(2)}ms`);
    }

    startTime.current = performance.now();
  });

  return {
    renderCount: renderCount.current,
    logPerformance: (action: string, time: number) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName} - ${action}: ${time.toFixed(2)}ms`);
      }
    }
  };
}

// 内存使用监控Hook
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = React.useState<any>(null);

  React.useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory);
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

export default {
  useDebounce,
  VirtualizedList,
  LazyComponent,
  withFieldOptimization,
  withPreviewOptimization,
  usePerformanceMonitor,
  useMemoryMonitor
};
