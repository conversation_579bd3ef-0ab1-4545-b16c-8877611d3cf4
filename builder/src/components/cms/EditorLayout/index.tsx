/**
 * 通用CMS编辑器布局组件
 * 
 * 为所有CMS编辑器提供统一的布局和功能
 */

'use client';

import React, { useState, useCallback } from 'react';
import { BlockEditorShadcn } from '@/components/pageRender/BlockEditor/BlockEditorShadcn';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, Eye, Code } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlockFormConfig } from '@/content-schema/types';

interface EditorLayoutProps<T = any> {
  /** 编辑器标题 */
  title: string;
  /** 表单配置 */
  config: BlockFormConfig;
  /** 初始数据 */
  initialData: T;
  /** 保存回调 */
  onSave: (data: T) => Promise<void> | void;
  /** 预览组件 */
  PreviewComponent: React.ComponentType<{ data: T }>;
  /** 是否显示原始JSON */
  showRawJson?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 是否全屏高度 */
  fullHeight?: boolean;
  /** 保存按钮文本 */
  saveButtonText?: string;
}

export function EditorLayout<T = any>({
  title,
  config,
  initialData,
  onSave,
  PreviewComponent,
  showRawJson = true,
  className,
  fullHeight = false,
  saveButtonText = 'Save Changes'
}: EditorLayoutProps<T>) {
  const [formData, setFormData] = useState<T>(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'preview' | 'json'>('preview');

  const handleSave = useCallback(async (data: T) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      await onSave(data);
      setFormData(data);
      setSaveStatus('success');
      
      // 3秒后重置状态
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Save failed';
      setError(errorMessage);
      setSaveStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  }, [onSave]);

  const handleFormChange = useCallback((data: T) => {
    setFormData(data);
    // 重置保存状态
    if (saveStatus !== 'idle') {
      setSaveStatus('idle');
    }
  }, [saveStatus]);

  const containerHeight = fullHeight ? 'h-[calc(100vh-120px)]' : 'min-h-[600px]';

  return (
    <div className={cn("min-h-screen bg-background p-4", className)}>
      <div className="w-full">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-foreground">{title}</h1>
          
          {/* 保存状态和操作按钮 */}
          <div className="flex items-center gap-4">
            {saveStatus === 'success' && (
              <Alert className="w-auto border-green-200 bg-green-50">
                <AlertDescription className="text-green-800">
                  Changes saved successfully!
                </AlertDescription>
              </Alert>
            )}
            
            <Button
              onClick={() => handleSave(formData)}
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSubmitting ? 'Saving...' : saveButtonText}
            </Button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-4", containerHeight)}>
          {/* 表单编辑器 */}
          <div className="bg-card rounded-lg shadow-lg p-4 border flex flex-col h-full">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">
              Editor
            </h2>
            <div className="flex-1 overflow-auto">
              <BlockEditorShadcn
                config={config}
                initialData={formData}
                onSave={handleFormChange}
              />
            </div>
          </div>
          
          {/* 预览区域 */}
          <div className="bg-card rounded-lg shadow-lg p-4 border flex flex-col h-full">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-card-foreground">
                Preview
              </h2>
              
              {showRawJson && (
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'preview' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('preview')}
                    className="flex items-center gap-1"
                  >
                    <Eye className="h-3 w-3" />
                    Preview
                  </Button>
                  <Button
                    variant={viewMode === 'json' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('json')}
                    className="flex items-center gap-1"
                  >
                    <Code className="h-3 w-3" />
                    JSON
                  </Button>
                </div>
              )}
            </div>
            
            <div className="flex-1 overflow-auto">
              {viewMode === 'preview' ? (
                <PreviewComponent data={formData} />
              ) : (
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm text-muted-foreground font-mono">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditorLayout;
