/**
 * 表单验证提供者组件
 * 
 * 为CMS编辑器提供统一的验证逻辑和错误处理
 */

'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { ValidationResult, ValidationError } from '@/content-schema/types';
import { RJSFSchema } from '@rjsf/utils';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

interface ValidationContextType {
  validate: (data: any, schema: RJSFSchema) => ValidationResult;
  errors: ValidationError[];
  isValid: boolean;
  clearErrors: () => void;
}

const ValidationContext = createContext<ValidationContextType | null>(null);

export function useValidation() {
  const context = useContext(ValidationContext);
  if (!context) {
    throw new Error('useValidation must be used within ValidationProvider');
  }
  return context;
}

interface ValidationProviderProps {
  children: React.ReactNode;
}

export function ValidationProvider({ children }: ValidationProviderProps) {
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isValid, setIsValid] = useState(true);

  // 创建AJV实例
  const ajv = new Ajv({ allErrors: true, verbose: true });
  addFormats(ajv);

  const validate = useCallback((data: any, schema: RJSFSchema): ValidationResult => {
    try {
      const validateFn = ajv.compile(schema);
      const valid = validateFn(data);
      
      if (valid) {
        setErrors([]);
        setIsValid(true);
        return { isValid: true, errors: [] };
      }

      const validationErrors: ValidationError[] = (validateFn.errors || []).map((error, index) => ({
        field: error.instancePath || error.schemaPath || `field_${index}`,
        message: getErrorMessage(error),
        code: error.keyword || 'validation_error'
      }));

      setErrors(validationErrors);
      setIsValid(false);
      
      return { isValid: false, errors: validationErrors };
    } catch (error) {
      const fallbackError: ValidationError = {
        field: 'form',
        message: 'Validation failed due to schema error',
        code: 'schema_error'
      };
      
      setErrors([fallbackError]);
      setIsValid(false);
      
      return { isValid: false, errors: [fallbackError] };
    }
  }, [ajv]);

  const clearErrors = useCallback(() => {
    setErrors([]);
    setIsValid(true);
  }, []);

  const value: ValidationContextType = {
    validate,
    errors,
    isValid,
    clearErrors
  };

  return (
    <ValidationContext.Provider value={value}>
      {children}
    </ValidationContext.Provider>
  );
}

// 错误消息格式化函数
function getErrorMessage(error: any): string {
  const { keyword, message, params } = error;
  
  switch (keyword) {
    case 'required':
      return `${params?.missingProperty || 'This field'} is required`;
    case 'minLength':
      return `Must be at least ${params?.limit} characters long`;
    case 'maxLength':
      return `Must be no more than ${params?.limit} characters long`;
    case 'minimum':
      return `Must be at least ${params?.limit}`;
    case 'maximum':
      return `Must be no more than ${params?.limit}`;
    case 'pattern':
      return 'Invalid format';
    case 'format':
      return `Invalid ${params?.format} format`;
    case 'enum':
      return `Must be one of: ${params?.allowedValues?.join(', ')}`;
    case 'minItems':
      return `Must have at least ${params?.limit} items`;
    case 'maxItems':
      return `Must have no more than ${params?.limit} items`;
    default:
      return message || 'Invalid value';
  }
}

// 验证错误显示组件
interface ValidationErrorsProps {
  errors: ValidationError[];
  className?: string;
}

export function ValidationErrors({ errors, className }: ValidationErrorsProps) {
  if (errors.length === 0) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {errors.map((error, index) => (
        <div
          key={index}
          className="flex items-start gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md"
        >
          <div className="flex-shrink-0 w-2 h-2 bg-destructive rounded-full mt-2" />
          <div className="flex-1">
            <p className="text-sm font-medium text-destructive">
              {error.field !== 'form' && (
                <span className="capitalize">{error.field.replace(/[._]/g, ' ')}: </span>
              )}
              {error.message}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}

export default ValidationProvider;
