/**
 * EditorLayout组件测试
 * 
 * 测试通用编辑器布局组件的功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EditorLayout } from '../EditorLayout';
import { BlogDetailData } from '@/content-schema/types';

// Mock BlockEditorShadcn组件
jest.mock('@/components/pageRender/BlockEditor/BlockEditorShadcn', () => ({
  BlockEditorShadcn: ({ onSave, initialData }: any) => (
    <div data-testid="block-editor">
      <button onClick={() => onSave({ ...initialData, title: 'Updated Title' })}>
        Update Data
      </button>
    </div>
  )
}));

// Mock预览组件
const MockPreviewComponent = ({ data }: { data: BlogDetailData }) => (
  <div data-testid="preview-component">
    <h1>{data.title}</h1>
    <p>{data.excerpt}</p>
  </div>
);

const mockConfig = {
  schema: {
    type: 'object',
    properties: {
      title: { type: 'string' },
      excerpt: { type: 'string' }
    }
  }
};

const mockInitialData: BlogDetailData = {
  title: 'Test Blog',
  excerpt: 'Test excerpt',
  content: 'Test content',
  featuredImage: { url: '/test.jpg', alt: 'Test' },
  author: { name: 'Test Author', avatar: '/avatar.jpg', bio: 'Test bio' },
  category: 'Test Category',
  tags: ['test'],
  readingTime: 5,
  seo: { metaTitle: 'Test', metaDescription: 'Test' }
};

describe('EditorLayout', () => {
  const mockOnSave = jest.fn();

  beforeEach(() => {
    mockOnSave.mockClear();
  });

  it('renders editor layout with title', () => {
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    expect(screen.getByText('Test Editor')).toBeInTheDocument();
    expect(screen.getByText('Editor')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
  });

  it('displays preview component with data', () => {
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    expect(screen.getByTestId('preview-component')).toBeInTheDocument();
    expect(screen.getByText('Test Blog')).toBeInTheDocument();
    expect(screen.getByText('Test excerpt')).toBeInTheDocument();
  });

  it('handles save operation successfully', async () => {
    const user = userEvent.setup();
    
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(mockInitialData);
    });
  });

  it('shows loading state during save', async () => {
    const user = userEvent.setup();
    const slowSave = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={slowSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);

    expect(screen.getByText('Saving...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Save Changes')).toBeInTheDocument();
    });
  });

  it('displays error message on save failure', async () => {
    const user = userEvent.setup();
    const failingSave = jest.fn(() => Promise.reject(new Error('Save failed')));
    
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={failingSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Save failed')).toBeInTheDocument();
    });
  });

  it('switches between preview and JSON view', async () => {
    const user = userEvent.setup();
    
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
        showRawJson={true}
      />
    );

    // Initially shows preview
    expect(screen.getByTestId('preview-component')).toBeInTheDocument();

    // Switch to JSON view
    const jsonButton = screen.getByText('JSON');
    await user.click(jsonButton);

    expect(screen.getByText(/"title": "Test Blog"/)).toBeInTheDocument();

    // Switch back to preview
    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);

    expect(screen.getByTestId('preview-component')).toBeInTheDocument();
  });

  it('updates data when form changes', async () => {
    const user = userEvent.setup();
    
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
      />
    );

    // Simulate form data update
    const updateButton = screen.getByText('Update Data');
    await user.click(updateButton);

    // Switch to JSON view to verify data update
    const jsonButton = screen.getByText('JSON');
    await user.click(jsonButton);

    expect(screen.getByText(/"title": "Updated Title"/)).toBeInTheDocument();
  });

  it('applies full height when specified', () => {
    const { container } = render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
        fullHeight={true}
      />
    );

    const gridContainer = container.querySelector('.grid');
    expect(gridContainer).toHaveClass('h-[calc(100vh-120px)]');
  });

  it('uses custom save button text', () => {
    render(
      <EditorLayout
        title="Test Editor"
        config={mockConfig}
        initialData={mockInitialData}
        onSave={mockOnSave}
        PreviewComponent={MockPreviewComponent}
        saveButtonText="Publish Now"
      />
    );

    expect(screen.getByText('Publish Now')).toBeInTheDocument();
  });
});
